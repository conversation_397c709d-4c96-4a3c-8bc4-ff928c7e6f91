'use client';

import { useState, useEffect } from 'react';
import { Calendar, Clock, CreditCard, CheckCircle } from 'lucide-react';

interface LaunchOption {
  id: 'free' | 'paid';
  title: string;
  description: string;
  price: number;
  features: string[];
  recommended?: boolean;
}

const launchOptions: LaunchOption[] = [
  {
    id: 'free',
    title: '免费发布',
    description: '选择一个月后的任意发布日期',
    price: 0,
    features: [
      '免费提交审核',
      '发布日期：一个月后起',
      '正常审核流程',
      '标准展示位置'
    ]
  },
  {
    id: 'paid',
    title: '优先发布',
    description: '选择任意发布日期',
    price: 99, // 99元
    features: [
      '可选择任意发布日期',
      '优先审核处理',
      '首页推荐位置',
      '专属客服支持'
    ],
    recommended: true
  }
];

interface LaunchDateSelectorProps {
  toolId?: string;
  currentOption?: 'free' | 'paid';
  currentDate?: string;
  isEditing?: boolean;
  onSubmit: (option: 'free' | 'paid', date: string) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

export default function LaunchDateSelector({
  currentOption = 'free',
  currentDate,
  isEditing = false,
  onSubmit,
  isSubmitting,
  error
}: LaunchDateSelectorProps) {
  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>(currentOption);
  const [selectedDate, setSelectedDate] = useState<string>('');

  // 获取最早可选择的免费日期（一个月后）
  const getMinFreeDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date.toISOString().split('T')[0];
  };

  // 获取最早可选择的付费日期（明天）
  const getMinPaidDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  };

  useEffect(() => {
    if (currentDate) {
      setSelectedDate(currentDate);
    } else {
      // 根据选择的选项设置默认日期
      if (selectedOption === 'free') {
        setSelectedDate(getMinFreeDate());
      } else {
        setSelectedDate(getMinPaidDate());
      }
    }
  }, [selectedOption, currentDate]);

  const handleOptionChange = (option: 'free' | 'paid') => {
    setSelectedOption(option);
    // 当切换选项时，重新设置日期
    if (option === 'free') {
      setSelectedDate(getMinFreeDate());
    } else {
      setSelectedDate(getMinPaidDate());
    }
  };

  const handleSubmit = async () => {
    if (!selectedDate) {
      return;
    }
    await onSubmit(selectedOption, selectedDate);
  };

  return (
    <div className="space-y-8">
      {/* 选项选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {isEditing ? '选择发布方案' : '选择发布选项'}
        </h3>
        <div className="grid md:grid-cols-2 gap-6">
          {launchOptions.map((option) => (
            <div
              key={option.id}
              className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${
                selectedOption === option.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${option.recommended ? 'ring-2 ring-blue-200' : ''}`}
              onClick={() => handleOptionChange(option.id)}
            >
              {option.recommended && (
                <div className="absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  推荐
                </div>
              )}
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  {option.id === 'free' ? (
                    <Calendar className="h-6 w-6 text-gray-600 mr-3" />
                  ) : (
                    <CreditCard className="h-6 w-6 text-blue-600 mr-3" />
                  )}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{option.title}</h4>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {option.price === 0 ? '免费' : `¥${option.price}`}
                  </div>
                </div>
              </div>
              
              <ul className="space-y-2">
                {option.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
              
              <div className="mt-4">
                <input
                  type="radio"
                  name="launchOption"
                  value={option.id}
                  checked={selectedOption === option.id}
                  onChange={() => handleOptionChange(option.id)}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 ${
                  selectedOption === option.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                }`}>
                  {selectedOption === option.id && (
                    <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 日期选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          选择发布日期
        </h3>
        
        <div className="max-w-md">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            min={selectedOption === 'free' ? getMinFreeDate() : getMinPaidDate()}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          
          <p className="text-sm text-gray-500 mt-2">
            {selectedOption === 'free' 
              ? '免费选项可以选择一个月后的任意日期' 
              : '付费选项可以选择明天及以后的任意日期'
            }
          </p>
        </div>
      </div>

      {/* 提交按钮 */}
      <div className="text-center">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || !selectedDate}
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {selectedOption === 'paid' ? '处理中...' : '保存中...'}
            </>
          ) : (
            <>
              {selectedOption === 'paid' ? (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  {isEditing ? '升级并支付 ¥99' : '支付 ¥99'}
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {isEditing ? '保存修改' : '确认发布日期'}
                </>
              )}
            </>
          )}
        </button>
        
        {error && (
          <p className="text-red-600 text-sm mt-4">{error}</p>
        )}
        
        <p className="text-gray-500 text-sm mt-4">
          {selectedOption === 'paid' 
            ? '点击后将跳转到支付页面' 
            : isEditing 
              ? '修改后的发布日期将立即生效'
              : '确认后工具将进入审核队列'
          }
        </p>
      </div>
    </div>
  );
}
