'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { FaHeart, FaRegHeart } from 'react-icons/fa';

interface LikeButtonProps {
  toolId: string;
  initialLikes?: number;
  initialLiked?: boolean;
  onLoginRequired?: () => void;
  onUnlike?: (toolId: string) => void;
  isInLikedPage?: boolean; // 新增：标识是否在liked页面
}

export default function LikeButton({
  toolId,
  initialLikes = 0,
  initialLiked = false,
  onLoginRequired,
  onUnlike,
  isInLikedPage = false
}: LikeButtonProps) {
  const { data: session } = useSession();
  const [liked, setLiked] = useState(initialLiked);
  const [likes, setLikes] = useState(initialLikes);
  const [isLoading, setIsLoading] = useState(false);

  // 获取点赞状态
  useEffect(() => {
    const fetchLikeStatus = async () => {
      try {
        const response = await fetch(`/api/tools/${toolId}/like`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setLiked(data.data.liked);
            setLikes(data.data.likes);
          }
        }
      } catch (error) {
        console.error('Failed to fetch like status:', error);
      }
    };

    if (session) {
      fetchLikeStatus();
    }
  }, [toolId, session]);

  const handleLike = async () => {
    if (!session) {
      onLoginRequired?.();
      return;
    }

    if (isLoading) return;

    setIsLoading(true);
    
    try {
      // 如果在liked页面，发送强制unlike请求
      const requestBody = isInLikedPage ? { forceUnlike: true } : {};

      const response = await fetch(`/api/tools/${toolId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const newLikedState = data.data.liked;
          setLiked(newLikedState);
          setLikes(data.data.likes);

          // 如果用户取消了点赞，并且提供了onUnlike回调，则调用它
          if (!newLikedState && onUnlike) {
            onUnlike(toolId);
          }
        }
      } else {
        const errorData = await response.json();
        console.error('Like failed:', errorData.message);
      }
    } catch (error) {
      console.error('Like request failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleLike}
      disabled={isLoading}
      className={`
        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200
        ${liked 
          ? 'bg-red-50 text-red-600 hover:bg-red-100' 
          : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
        }
        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
        border border-gray-200 hover:border-gray-300
      `}
    >
      {liked ? (
        <FaHeart className="w-4 h-4 text-red-500" />
      ) : (
        <FaRegHeart className="w-4 h-4" />
      )}
      <span className="text-sm font-medium">
        {likes > 0 ? likes : ''}
      </span>
    </button>
  );
}
