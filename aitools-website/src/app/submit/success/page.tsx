'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import { CheckCircle, Clock, CreditCard, ArrowRight, Home } from 'lucide-react';

export default function SubmitSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const toolId = searchParams.get('toolId');
  const isPaid = searchParams.get('paid') === 'true';

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && toolId) {
      fetchToolInfo();
    }
  }, [status, toolId]);

  const fetchToolInfo = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`);
      const data = await response.json();
      
      if (data.success) {
        setTool(data.data.tool);
      }
    } catch (err) {
      console.error('Failed to fetch tool info:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-green-100 rounded-full p-3">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {isPaid ? '支付成功！' : '提交成功！'}
          </h1>
          
          <p className="text-lg text-gray-600">
            {isPaid 
              ? '您的工具已进入优先审核队列，我们会在1个工作日内完成审核。'
              : '您的工具已进入审核队列，我们会在1-3个工作日内完成审核。'
            }
          </p>
        </div>

        {/* Tool Info */}
        {tool && (
          <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">工具信息</h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">{tool.name}</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>提交状态：</span>
                    <span className="font-medium text-green-600">已提交</span>
                  </div>
                  <div className="flex justify-between">
                    <span>审核状态：</span>
                    <span className="font-medium text-yellow-600">
                      {tool.status === 'pending' ? '等待审核' : '草稿'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>发布选项：</span>
                    <span className="font-medium">
                      {tool.launchOption === 'paid' ? '优先发布' : '免费发布'}
                    </span>
                  </div>
                  {tool.selectedLaunchDate && (
                    <div className="flex justify-between">
                      <span>计划发布日期：</span>
                      <span className="font-medium">
                        {new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">接下来会发生什么？</h4>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5">
                      <Clock className="h-3 w-3 text-blue-600" />
                    </div>
                    <div className="text-sm text-gray-600">
                      <div className="font-medium">审核阶段</div>
                      <div>我们的团队会审核您的工具信息</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    </div>
                    <div className="text-sm text-gray-600">
                      <div className="font-medium">审核通过</div>
                      <div>工具将在指定日期发布到平台</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="bg-purple-100 rounded-full p-1 mr-3 mt-0.5">
                      <ArrowRight className="h-3 w-3 text-purple-600" />
                    </div>
                    <div className="text-sm text-gray-600">
                      <div className="font-medium">正式发布</div>
                      <div>用户可以在平台上发现您的工具</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Service Features for Paid Users */}
        {isPaid && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div className="flex items-center mb-4">
              <CreditCard className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-blue-900">
                优先发布服务已激活
              </h3>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center text-blue-800">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm">优先审核（1个工作日内）</span>
                </div>
                <div className="flex items-center text-blue-800">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm">首页推荐位置展示</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center text-blue-800">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm">自定义发布日期</span>
                </div>
                <div className="flex items-center text-blue-800">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm">专属客服支持</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Contact Info */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            需要帮助？
          </h3>
          <p className="text-gray-600 mb-4">
            如果您有任何问题或需要修改工具信息，请联系我们：
          </p>
          <div className="space-y-2 text-sm text-gray-600">
            <div>📧 邮箱：<EMAIL></div>
            <div>💬 微信：aitools_support</div>
            <div>⏰ 工作时间：周一至周五 9:00-18:00</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => router.push('/profile/submitted')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 flex items-center justify-center"
          >
            <Clock className="h-4 w-4 mr-2" />
            查看我的提交
          </button>
          
          <button
            onClick={() => router.push('/')}
            className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 flex items-center justify-center"
          >
            <Home className="h-4 w-4 mr-2" />
            返回首页
          </button>
        </div>
      </div>
    </Layout>
  );
}
