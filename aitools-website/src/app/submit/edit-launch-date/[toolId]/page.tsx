'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import { Calendar, Save, ArrowLeft, AlertCircle, CheckCircle } from 'lucide-react';

export default function EditLaunchDatePage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const toolId = params.toolId as string;

  // 获取最早可选择的付费日期（明天）
  const getMinPaidDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  };

  // 获取最早可选择的免费日期（一个月后）
  const getMinFreeDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date.toISOString().split('T')[0];
  };

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && toolId) {
      fetchToolInfo();
    }
  }, [status, toolId]);

  const fetchToolInfo = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`);
      const data = await response.json();
      
      if (data.success) {
        const toolData = data.data.tool;
        setTool(toolData);
        
        // 检查权限和状态
        // 注意：这里应该检查用户ID而不是email，但由于session中没有ID，暂时跳过这个检查
        // 实际的权限检查会在API层面进行

        if (!['pending', 'approved'].includes(toolData.status)) {
          setError('当前状态不允许修改发布日期');
          return;
        }

        // 检查是否已经发布
        const now = new Date();
        if (toolData.selectedLaunchDate && new Date(toolData.selectedLaunchDate) <= now && toolData.status === 'approved') {
          setError('工具已发布，无法修改发布日期');
          return;
        }

        // 设置当前日期
        if (toolData.selectedLaunchDate) {
          setSelectedDate(new Date(toolData.selectedLaunchDate).toISOString().split('T')[0]);
        } else {
          // 根据付费状态设置默认日期
          setSelectedDate(toolData.launchOption === 'paid' ? getMinPaidDate() : getMinFreeDate());
        }
      } else {
        setError('工具不存在');
      }
    } catch (err) {
      setError('获取工具信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!selectedDate) {
      setError('请选择发布日期');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedDate,
        }),
      });

      const data = await response.json();

      if (data.success) {
        router.push(`/submit/success?toolId=${toolId}&paid=true`);
      } else {
        setError(data.message || '修改失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">出错了</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.back()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回上一页
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            修改发布日期
          </h1>
          <p className="text-gray-600">
            您可以随时修改工具的发布日期，直到工具正式发布
          </p>
        </div>

        {/* Tool Info */}
        {tool && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {tool.name}
            </h2>
            <p className="text-gray-600 mb-4">{tool.description}</p>
            
            <div className="flex items-center text-sm text-gray-600">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              {tool.launchOption === 'paid' ? '优先发布服务已激活' : '免费发布服务'}
            </div>
          </div>
        )}

        {/* Date Selection */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            选择新的发布日期
          </h3>
          
          <div className="max-w-md">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              min={tool?.launchOption === 'paid' ? getMinPaidDate() : getMinFreeDate()}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />

            <p className="text-sm text-gray-500 mt-2">
              {tool?.launchOption === 'paid'
                ? '您可以选择明天及以后的任意日期'
                : '您可以选择一个月后的任意日期'
              }
            </p>
          </div>
        </div>

        {/* Current vs New Date Comparison */}
        {tool?.selectedLaunchDate && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h4 className="font-medium text-blue-900 mb-3">日期变更对比</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">当前日期：</span>
                <div className="font-medium text-gray-900">
                  {new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}
                </div>
              </div>
              <div>
                <span className="text-gray-600">新日期：</span>
                <div className="font-medium text-blue-600">
                  {selectedDate ? new Date(selectedDate).toLocaleDateString('zh-CN') : '请选择'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="text-center">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !selectedDate}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存修改
              </>
            )}
          </button>
          
          {error && (
            <p className="text-red-600 text-sm mt-4">{error}</p>
          )}
          
          <p className="text-gray-500 text-sm mt-4">
            修改后的发布日期将立即生效
          </p>
        </div>
      </div>
    </Layout>
  );
}
