'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import { Calendar, Clock, CreditCard, CheckCircle, AlertCircle } from 'lucide-react';

interface LaunchOption {
  id: 'free' | 'paid';
  title: string;
  description: string;
  price: number;
  features: string[];
  recommended?: boolean;
}

const launchOptions: LaunchOption[] = [
  {
    id: 'free',
    title: '免费发布',
    description: '选择一个月后的发布日期',
    price: 0,
    features: [
      '免费提交审核',
      '发布日期：一个月后',
      '正常审核流程',
      '标准展示位置'
    ]
  },
  {
    id: 'paid',
    title: '优先发布',
    description: '选择任意发布日期',
    price: 99, // 99元
    features: [
      '可选择任意发布日期',
      '优先审核处理',
      '首页推荐位置',
      '专属客服支持'
    ],
    recommended: true
  }
];

export default function LaunchDatePage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>('free');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const toolId = params.toolId as string;

  // 获取最早可选择的免费日期（一个月后）
  const getMinFreeDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date.toISOString().split('T')[0];
  };

  // 获取最早可选择的付费日期（明天）
  const getMinPaidDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  };

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && toolId) {
      fetchToolInfo();
    }
  }, [status, toolId]);

  useEffect(() => {
    // 根据选择的选项设置默认日期
    if (selectedOption === 'free') {
      setSelectedDate(getMinFreeDate());
    } else {
      setSelectedDate(getMinPaidDate());
    }
  }, [selectedOption]);

  const fetchToolInfo = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}`);
      const data = await response.json();
      
      if (data.success) {
        setTool(data.data);
        // 检查工具是否属于当前用户
        if (data.data.submittedBy !== session?.user?.id) {
          setError('您没有权限访问此工具');
          return;
        }
        // 检查工具状态
        if (data.data.status !== 'draft') {
          setError('此工具已经选择了发布日期');
          return;
        }
      } else {
        setError('工具不存在');
      }
    } catch (err) {
      setError('获取工具信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!selectedDate) {
      setError('请选择发布日期');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          launchOption: selectedOption,
          selectedDate,
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (selectedOption === 'paid' && data.data.paymentUrl) {
          // 跳转到支付页面
          window.location.href = data.data.paymentUrl;
        } else {
          // 免费选项，直接进入审核
          router.push(`/submit/success?toolId=${toolId}`);
        }
      } else {
        setError(data.message || '提交失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">出错了</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push('/submit')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回提交页面
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            工具信息提交成功！
          </h1>
          <p className="text-lg text-gray-600">
            现在请选择您的发布日期和选项
          </p>
        </div>

        {/* Tool Info */}
        {tool && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {tool.name}
            </h2>
            <p className="text-gray-600">{tool.description}</p>
          </div>
        )}

        {/* Launch Options */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {launchOptions.map((option) => (
            <div
              key={option.id}
              className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${
                selectedOption === option.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${option.recommended ? 'ring-2 ring-blue-200' : ''}`}
              onClick={() => setSelectedOption(option.id)}
            >
              {option.recommended && (
                <div className="absolute -top-3 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  推荐
                </div>
              )}
              
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">
                  {option.title}
                </h3>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {option.price === 0 ? '免费' : `¥${option.price}`}
                  </div>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4">{option.description}</p>
              
              <ul className="space-y-2">
                {option.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
              
              <div className="mt-4">
                <input
                  type="radio"
                  name="launchOption"
                  value={option.id}
                  checked={selectedOption === option.id}
                  onChange={() => setSelectedOption(option.id)}
                  className="h-4 w-4 text-blue-600"
                />
              </div>
            </div>
          ))}
        </div>

        {/* Date Selection */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            选择发布日期
          </h3>
          
          <div className="max-w-md">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              min={selectedOption === 'free' ? getMinFreeDate() : getMinPaidDate()}
              max={selectedOption === 'free' ? getMinFreeDate() : undefined}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            
            <p className="text-sm text-gray-500 mt-2">
              {selectedOption === 'free' 
                ? '免费选项只能选择一个月后的日期' 
                : '付费选项可以选择明天及以后的任意日期'
              }
            </p>
          </div>
        </div>

        {/* Submit Button */}
        <div className="text-center">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !selectedDate}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                处理中...
              </>
            ) : selectedOption === 'paid' ? (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                立即支付 ¥{launchOptions.find(o => o.id === 'paid')?.price}
              </>
            ) : (
              <>
                <Clock className="h-4 w-4 mr-2" />
                确认免费发布
              </>
            )}
          </button>
          
          {error && (
            <p className="text-red-600 text-sm mt-4">{error}</p>
          )}
        </div>
      </div>
    </Layout>
  );
}
