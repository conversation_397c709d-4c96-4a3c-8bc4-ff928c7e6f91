'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import { Tool } from '@/lib/api';
import {
  Plus,
  Edit,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  ExternalLink,
  BarChart3,
  ArrowLeft
} from 'lucide-react';



const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'approved':
      return '已通过';
    case 'pending':
      return '审核中';
    case 'rejected':
      return '已拒绝';
    case 'draft':
      return '草稿';
    default:
      return status;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'approved':
      return <CheckCircle className="h-4 w-4" />;
    case 'pending':
      return <Clock className="h-4 w-4" />;
    case 'rejected':
      return <XCircle className="h-4 w-4" />;
    case 'draft':
      return <Edit className="h-4 w-4" />;
    default:
      return null;
  }
};

export default function SubmittedToolsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }
    
    if (status === 'authenticated') {
      fetchTools();
    }
  }, [status, router]);

  const fetchTools = async () => {
    try {
      setLoading(true);
      setError('');

      // 获取用户提交的工具
      const response = await fetch('/api/user/tools');
      const data = await response.json();

      if (data.success && data.data) {
        setTools(data.data.tools);
      } else {
        setError(data.message || '获取工具列表失败');
      }
    } catch (error) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredTools = tools.filter(tool =>
    selectedStatus === 'all' || tool.status === selectedStatus
  );

  const stats = {
    total: tools.length,
    draft: tools.filter(t => t.status === 'draft').length,
    approved: tools.filter(t => t.status === 'approved').length,
    pending: tools.filter(t => t.status === 'pending').length,
    rejected: tools.filter(t => t.status === 'rejected').length,
    totalViews: tools.reduce((sum, t) => sum + t.views, 0),
    totalLikes: tools.reduce((sum, t) => sum + t.likes, 0)
  };

  if (status === 'loading' || loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <LoadingSpinner size="lg" className="py-20" />
        </div>
      </Layout>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <div className="flex items-center mb-2">
              <Link
                href="/profile"
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">我提交的AI工具</h1>
            </div>
            <p className="text-lg text-gray-600">管理您提交的所有AI工具</p>
          </div>
          <Link
            href="/submit"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Plus className="mr-2 h-5 w-5" />
            提交新工具
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总提交数</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">已通过</p>
                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Eye className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总浏览量</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 text-red-600">❤️</div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总点赞数</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalLikes}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedStatus('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              全部 ({stats.total})
            </button>
            <button
              onClick={() => setSelectedStatus('draft')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'draft'
                  ? 'bg-gray-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              草稿 ({stats.draft})
            </button>
            <button
              onClick={() => setSelectedStatus('approved')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'approved'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              已通过 ({stats.approved})
            </button>
            <button
              onClick={() => setSelectedStatus('pending')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'pending'
                  ? 'bg-yellow-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              审核中 ({stats.pending})
            </button>
            <button
              onClick={() => setSelectedStatus('rejected')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'rejected'
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              已拒绝 ({stats.rejected})
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <ErrorMessage
            message={error}
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Tools List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredTools.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredTools.map((tool) => (
                <div key={tool._id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {tool.name}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)}`}>
                          {getStatusIcon(tool.status)}
                          <span className="ml-1">{getStatusText(tool.status)}</span>
                        </span>
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {tool.description}
                      </p>
                      
                      <div className="flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>提交于 {new Date(tool.submittedAt).toLocaleDateString('zh-CN')}</span>
                        </div>
                        {tool.publishedAt && (
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="h-4 w-4" />
                            <span>发布于 {new Date(tool.publishedAt).toLocaleDateString('zh-CN')}</span>
                          </div>
                        )}
                        {tool.status === 'approved' && (
                          <>
                            <div className="flex items-center space-x-1">
                              <Eye className="h-4 w-4" />
                              <span>{tool.views} 浏览</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <span>❤️</span>
                              <span>{tool.likes} 点赞</span>
                            </div>
                          </>
                        )}
                      </div>

                      {tool.status === 'rejected' && tool.reviewNotes && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-800">
                            <strong>拒绝原因：</strong> {tool.reviewNotes}
                          </p>
                        </div>
                      )}

                      {tool.status === 'draft' && (
                        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <p className="text-sm text-blue-800 mb-2">
                            <strong>下一步：</strong> 选择发布日期
                          </p>
                          <Link
                            href={`/submit/launch-date/${tool._id}`}
                            className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            选择发布日期
                          </Link>
                        </div>
                      )}

                      {tool.status === 'pending' && tool.launchOption && (
                        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div className="text-sm text-yellow-800">
                            <div className="flex justify-between items-center mb-1">
                              <span><strong>发布选项：</strong></span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                tool.launchOption === 'paid'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {tool.launchOption === 'paid' ? '优先发布' : '免费发布'}
                              </span>
                            </div>
                            {tool.selectedLaunchDate && (
                              <div className="flex justify-between items-center mb-1">
                                <span><strong>计划发布：</strong></span>
                                <span>{new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}</span>
                              </div>
                            )}
                            {tool.paymentRequired && (
                              <div className="flex justify-between items-center mb-2">
                                <span><strong>支付状态：</strong></span>
                                <span className={`px-2 py-1 rounded text-xs ${
                                  tool.paymentStatus === 'completed'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {tool.paymentStatus === 'completed' ? '已支付' : '待支付'}
                                </span>
                              </div>
                            )}
                            <div className="flex justify-end mt-2">
                              <Link
                                href={`/submit/edit-launch-date/${tool._id}`}
                                className="inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors"
                              >
                                <Calendar className="h-3 w-3 mr-1" />
                                修改发布日期
                              </Link>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 已通过的工具显示launch date信息 */}
                      {tool.status === 'approved' && tool.launchOption && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <div className="text-sm text-green-800">
                            <div className="flex justify-between items-center mb-1">
                              <span><strong>发布选项：</strong></span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                tool.launchOption === 'paid'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {tool.launchOption === 'paid' ? '优先发布' : '免费发布'}
                              </span>
                            </div>
                            {tool.selectedLaunchDate && (
                              <div className="flex justify-between items-center">
                                <span><strong>发布日期：</strong></span>
                                <span>{new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      {tool.status === 'approved' && (
                        <Link
                          href={`/tools/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="查看详情"
                        >
                          <Eye className="h-5 w-5" />
                        </Link>
                      )}

                      {/* Launch Date 管理按钮 */}
                      {tool.status === 'draft' && !tool.launchDateSelected && (
                        <Link
                          href={`/submit/launch-date/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="设定发布日期"
                        >
                          <Calendar className="h-5 w-5" />
                        </Link>
                      )}

                      {(tool.status === 'pending' || tool.status === 'approved') && tool.launchDateSelected && (
                        <Link
                          href={`/submit/edit-launch-date/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-orange-600 transition-colors"
                          title="修改发布日期"
                        >
                          <Calendar className="h-5 w-5" />
                        </Link>
                      )}

                      <a
                        href={tool.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                        title="访问网站"
                      >
                        <ExternalLink className="h-5 w-5" />
                      </a>
                      {(tool.status === 'rejected' || tool.status === 'pending') && (
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="编辑"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <BarChart3 className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {selectedStatus === 'all' ? '还没有提交任何工具' : `没有${getStatusText(selectedStatus)}的工具`}
              </h3>
              <p className="text-gray-600 mb-4">
                {selectedStatus === 'all' 
                  ? '开始提交您的第一个 AI 工具吧！'
                  : '尝试选择其他状态查看工具'
                }
              </p>
              {selectedStatus === 'all' && (
                <Link
                  href="/submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  提交工具
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
