'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');

  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && orderId) {
      fetchOrderInfo();
    }
  }, [status, orderId]);

  const fetchOrderInfo = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`);
      const data = await response.json();
      
      if (data.success) {
        setOrder(data.data);
        
        // 检查订单状态
        if (data.data.status === 'completed') {
          router.push(`/submit/success?toolId=${data.data.toolId}`);
          return;
        }
        
        if (data.data.status !== 'pending') {
          setError('订单状态异常');
          return;
        }
      } else {
        setError('订单不存在');
      }
    } catch (err) {
      setError('获取订单信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    setProcessing(true);
    setError('');

    try {
      // 模拟支付处理
      await new Promise(resolve => setTimeout(resolve, 2000));

      const response = await fetch(`/api/orders/${orderId}/pay`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: 'mock_payment'
        }),
      });

      const data = await response.json();

      if (data.success) {
        router.push(`/submit/success?toolId=${order.toolId}&paid=true`);
      } else {
        setError(data.message || '支付失败');
      }
    } catch (err) {
      setError('支付处理失败，请重试');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载订单信息...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">支付出错</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push('/submit')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回提交页面
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  if (!order) {
    return null;
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <CreditCard className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            完成支付
          </h1>
          <p className="text-lg text-gray-600">
            为您的工具选择优先发布服务
          </p>
        </div>

        {/* Order Summary */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">订单详情</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">服务类型</span>
              <span className="font-medium">工具优先发布</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">工具名称</span>
              <span className="font-medium">{order.tool?.name || '加载中...'}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">发布日期</span>
              <span className="font-medium">
                {new Date(order.selectedLaunchDate).toLocaleDateString('zh-CN')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">订单号</span>
              <span className="font-medium text-sm">{order._id}</span>
            </div>
            
            <hr className="my-4" />
            
            <div className="flex justify-between text-lg font-semibold">
              <span>总计</span>
              <span className="text-blue-600">¥{(order.amount / 100).toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Service Features */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            优先发布服务包含：
          </h3>
          <ul className="space-y-2">
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              可选择任意发布日期
            </li>
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              优先审核处理（1个工作日内）
            </li>
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              首页推荐位置展示
            </li>
            <li className="flex items-center text-blue-800">
              <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              专属客服支持
            </li>
          </ul>
        </div>

        {/* Security Notice */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <div className="flex items-center text-gray-700">
            <Shield className="h-5 w-5 mr-2 text-green-500" />
            <span className="text-sm">
              您的支付信息受到银行级别的安全保护
            </span>
          </div>
        </div>

        {/* Payment Button */}
        <div className="text-center">
          <button
            onClick={handlePayment}
            disabled={processing}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {processing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                处理支付中...
              </>
            ) : (
              <>
                <CreditCard className="h-5 w-5 mr-2" />
                立即支付 ¥{(order.amount / 100).toFixed(2)}
              </>
            )}
          </button>
          
          {error && (
            <p className="text-red-600 text-sm mt-4">{error}</p>
          )}
          
          <p className="text-gray-500 text-sm mt-4">
            点击支付即表示您同意我们的服务条款和隐私政策
          </p>
        </div>
      </div>
    </Layout>
  );
}
