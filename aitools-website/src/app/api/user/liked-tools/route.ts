import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../lib/auth';
import dbConnect from '../../../../lib/mongodb';
import User from '../../../../models/User';
import Tool from '../../../../models/Tool';

// GET /api/user/liked-tools - 获取用户收藏的工具列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // 获取用户收藏的工具ID列表
    const likedToolIds = user.likedTools || [];

    if (likedToolIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          tools: [],
          pagination: {
            page,
            limit,
            total: 0,
            pages: 0
          }
        }
      });
    }

    // 查询收藏的工具详情
    const tools = await Tool.find({ 
      _id: { $in: likedToolIds },
      isActive: true,
      status: 'approved'
    })
    .sort({ updatedAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();

    const total = await Tool.countDocuments({ 
      _id: { $in: likedToolIds },
      isActive: true,
      status: 'approved'
    });

    return NextResponse.json({
      success: true,
      data: {
        tools,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get liked tools error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}
