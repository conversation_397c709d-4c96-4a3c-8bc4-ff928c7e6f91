import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// POST /api/orders/[id]/pay - 处理订单支付
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const { paymentMethod } = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: '无效的订单ID' },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const order = await Order.findById(id);
    if (!order) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: '您没有权限操作此订单' },
        { status: 403 }
      );
    }

    // 检查订单状态
    if (order.status !== 'pending') {
      return NextResponse.json(
        { success: false, message: '订单状态异常，无法支付' },
        { status: 400 }
      );
    }

    // 这里应该集成真实的支付系统
    // 现在我们模拟支付成功
    const paymentSuccess = true; // 在真实环境中，这应该是支付网关的响应

    if (paymentSuccess) {
      // 更新订单状态
      await order.markAsPaid();
      order.paymentMethod = paymentMethod;
      await order.save();

      // 更新工具状态
      await Tool.findByIdAndUpdate(order.toolId, {
        $set: {
          paymentStatus: 'completed',
          paidAt: new Date(),
          status: 'pending' // 进入审核队列
        }
      });

      return NextResponse.json({
        success: true,
        data: {
          orderId: order._id,
          message: '支付成功，工具已进入优先审核队列'
        }
      });

    } else {
      // 支付失败
      await order.markAsFailed();

      return NextResponse.json(
        { success: false, message: '支付失败，请重试' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Payment processing error:', error);
    return NextResponse.json(
      { success: false, message: '支付处理失败' },
      { status: 500 }
    );
  }
}
