import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// GET /api/orders/[id] - 获取订单信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: '无效的订单ID' },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const order = await Order.findById(id).populate('toolId', 'name description');
    if (!order) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: '您没有权限访问此订单' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        _id: order._id,
        type: order.type,
        amount: order.amount,
        currency: order.currency,
        status: order.status,
        description: order.description,
        selectedLaunchDate: order.selectedLaunchDate,
        createdAt: order.createdAt,
        paidAt: order.paidAt,
        tool: order.toolId,
        toolId: order.toolId._id
      }
    });

  } catch (error) {
    console.error('Get order error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}
