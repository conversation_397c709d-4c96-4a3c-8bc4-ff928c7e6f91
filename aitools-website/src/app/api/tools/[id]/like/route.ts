import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const { id: toolId } = await params;

    // 查找工具
    const tool = await Tool.findById(toolId);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查请求体中是否有强制操作标识
    const body = await request.json().catch(() => ({}));
    const forceUnlike = body.forceUnlike === true;

    console.log('force unlike..........', forceUnlike, toolId, user._id, tool.likedBy, user.likedTools
    );

    if (forceUnlike) {
      // 强制取消点赞 - 直接从两个地方移除，不管当前状态
      tool.likedBy = tool.likedBy.filter((id: string) => id?.toString() !== user._id.toString());
      user.likedTools = user.likedTools.filter((id: string) => id?.toString() !== toolId);

      // 重新计算likes数量以确保准确性
      tool.likes = tool.likedBy.length;

      // 保存更改
      await tool.save();
      await user.save();

      return NextResponse.json({
        success: true,
        data: {
          liked: false,
          likes: tool.likes
        }
      });
    }

    // 原有的切换逻辑 - 检查用户是否已经点赞
    const hasLikedInTool = tool.likedBy.includes(user._id.toString());
    const hasLikedInUser = user.likedTools.includes(toolId);

    // 处理数据不一致的情况
    if (hasLikedInTool !== hasLikedInUser) {
      console.warn(`Data inconsistency detected for user ${user._id} and tool ${toolId}:`, {
        hasLikedInTool,
        hasLikedInUser,
        toolLikedBy: tool.likedBy,
        userLikedTools: user.likedTools
      });
    }

    // 使用逻辑OR来决定当前状态 - 如果任一数据源显示已点赞，则认为已点赞
    const currentlyLiked = hasLikedInTool || hasLikedInUser;

    if (currentlyLiked) {
      // 取消点赞 - 确保从两个地方都移除
      tool.likedBy = tool.likedBy.filter((id: string) => id !== user._id.toString());
      user.likedTools = user.likedTools.filter((id: string) => id !== toolId);

      // 重新计算likes数量以确保准确性
      tool.likes = tool.likedBy.length;
    } else {
      // 添加点赞 - 确保添加到两个地方且避免重复
      if (!tool.likedBy.includes(user._id.toString())) {
        tool.likedBy.push(user._id.toString());
      }
      if (!user.likedTools.includes(toolId)) {
        user.likedTools.push(toolId);
      }

      // 重新计算likes数量以确保准确性
      tool.likes = tool.likedBy.length;
    }

    // 保存更改
    await tool.save();
    await user.save();

    return NextResponse.json({
      success: true,
      data: {
        liked: !currentlyLiked,
        likes: tool.likes
      }
    });

  } catch (error) {
    console.error('Like tool error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    await dbConnect();

    const { id: toolId } = await params;
    const tool = await Tool.findById(toolId);

    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    let liked = false;
    if (session?.user?.email) {
      const user = await User.findOne({ email: session.user.email });
      if (user) {
        // 检查两个数据源的一致性
        const hasLikedInTool = tool.likedBy.includes(user._id.toString());
        const hasLikedInUser = user.likedTools.includes(toolId);

        // 如果数据不一致，记录警告
        if (hasLikedInTool !== hasLikedInUser) {
          console.warn(`Data inconsistency detected in GET for user ${user._id} and tool ${toolId}:`, {
            hasLikedInTool,
            hasLikedInUser
          });
        }

        // 使用逻辑OR来决定状态
        liked = hasLikedInTool || hasLikedInUser;
      }
    }

    // 确保likes数量与likedBy数组长度一致
    const actualLikes = tool.likedBy.length;
    if (tool.likes !== actualLikes) {
      console.warn(`Likes count inconsistency for tool ${toolId}: stored=${tool.likes}, actual=${actualLikes}`);
      // 可以选择在这里修复数据
      tool.likes = actualLikes;
      await tool.save();
    }

    return NextResponse.json({
      success: true,
      data: {
        liked,
        likes: tool.likes
      }
    });

  } catch (error) {
    console.error('Get like status error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}
