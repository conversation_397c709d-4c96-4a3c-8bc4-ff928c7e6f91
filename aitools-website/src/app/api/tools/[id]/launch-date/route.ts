import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import Order from '@/models/Order';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// POST /api/tools/[id]/launch-date - 设置工具发布日期和选项
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const { launchOption, selectedDate } = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 验证输入
    if (!launchOption || !selectedDate) {
      return NextResponse.json(
        { success: false, message: '请选择发布选项和日期' },
        { status: 400 }
      );
    }

    if (!['free', 'paid'].includes(launchOption)) {
      return NextResponse.json(
        { success: false, message: '无效的发布选项' },
        { status: 400 }
      );
    }

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: '您没有权限修改此工具' },
        { status: 403 }
      );
    }

    // 检查工具状态
    if (tool.status !== 'draft') {
      return NextResponse.json(
        { success: false, message: '此工具已经选择了发布日期' },
        { status: 400 }
      );
    }

    const selectedLaunchDate = new Date(selectedDate);
    const now = new Date();

    // 验证日期
    if (launchOption === 'free') {
      // 免费选项：必须是一个月后或更晚的日期
      const oneMonthLater = new Date();
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
      oneMonthLater.setHours(0, 0, 0, 0);

      const selectedDateOnly = new Date(selectedLaunchDate);
      selectedDateOnly.setHours(0, 0, 0, 0);

      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {
        return NextResponse.json(
          { success: false, message: '免费选项只能选择一个月后的日期' },
          { status: 400 }
        );
      }
    } else {
      // 付费选项：必须是明天或以后的日期
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      if (selectedLaunchDate < tomorrow) {
        return NextResponse.json(
          { success: false, message: '付费选项最早只能选择明天的日期' },
          { status: 400 }
        );
      }
    }

    if (launchOption === 'free') {
      // 免费选项：直接更新工具状态并进入审核
      await Tool.findByIdAndUpdate(id, {
        $set: {
          launchDateSelected: true,
          selectedLaunchDate,
          launchOption: 'free',
          paymentRequired: false,
          status: 'pending' // 进入审核队列
        }
      });

      return NextResponse.json({
        success: true,
        data: {
          message: '发布日期设置成功，工具已进入审核队列'
        }
      });

    } else {
      // 付费选项：创建订单
      const paymentAmount = 9900; // 99元，以分为单位

      const order = new Order({
        userId: user._id,
        toolId: id,
        type: 'launch_date_priority',
        amount: paymentAmount,
        currency: 'CNY',
        status: 'pending',
        description: `工具 "${tool.name}" 优先发布服务`,
        selectedLaunchDate
      });

      await order.save();

      // 更新工具状态
      await Tool.findByIdAndUpdate(id, {
        $set: {
          launchDateSelected: true,
          selectedLaunchDate,
          launchOption: 'paid',
          paymentRequired: true,
          paymentAmount,
          paymentStatus: 'pending',
          orderId: order._id.toString()
        }
      });

      // 这里应该集成真实的支付系统（如Stripe、支付宝等）
      // 现在返回一个模拟的支付URL
      const paymentUrl = `/payment/checkout?orderId=${order._id}`;

      return NextResponse.json({
        success: true,
        data: {
          orderId: order._id,
          paymentUrl,
          amount: paymentAmount,
          message: '订单创建成功，请完成支付'
        }
      });
    }

  } catch (error) {
    console.error('Launch date selection error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

// GET /api/tools/[id]/launch-date - 获取工具发布日期信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: '无效的工具ID' },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: '您没有权限访问此工具' },
        { status: 403 }
      );
    }

    // 如果有订单，获取订单信息
    let order = null;
    if (tool.orderId) {
      order = await Order.findById(tool.orderId);
    }

    return NextResponse.json({
      success: true,
      data: {
        tool: {
          id: tool._id,
          name: tool.name,
          status: tool.status,
          launchDateSelected: tool.launchDateSelected,
          selectedLaunchDate: tool.selectedLaunchDate,
          launchOption: tool.launchOption,
          paymentRequired: tool.paymentRequired,
          paymentStatus: tool.paymentStatus
        },
        order
      }
    });

  } catch (error) {
    console.error('Get launch date info error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

// PATCH /api/tools/[id]/launch-date - 修改工具发布日期（仅限付费用户）
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const { selectedDate } = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: '无效的工具ID' },
        { status: 400 }
      );
    }

    if (!selectedDate) {
      return NextResponse.json(
        { success: false, message: '请选择发布日期' },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: '您没有权限修改此工具' },
        { status: 403 }
      );
    }

    // 检查是否为付费用户
    if (tool.launchOption !== 'paid') {
      return NextResponse.json(
        { success: false, message: '只有付费用户可以修改发布日期' },
        { status: 400 }
      );
    }

    // 检查工具状态（只有pending状态可以修改）
    if (tool.status !== 'pending') {
      return NextResponse.json(
        { success: false, message: '当前状态不允许修改发布日期' },
        { status: 400 }
      );
    }

    const selectedLaunchDate = new Date(selectedDate);

    // 验证日期（付费选项：必须是明天或以后的日期）
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    if (selectedLaunchDate < tomorrow) {
      return NextResponse.json(
        { success: false, message: '发布日期最早只能选择明天' },
        { status: 400 }
      );
    }

    // 更新工具的发布日期
    await Tool.findByIdAndUpdate(id, {
      $set: {
        selectedLaunchDate
      }
    });

    // 如果有关联的订单，也更新订单的发布日期
    if (tool.orderId) {
      await Order.findByIdAndUpdate(tool.orderId, {
        $set: {
          selectedLaunchDate
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        message: '发布日期修改成功',
        selectedLaunchDate
      }
    });

  } catch (error) {
    console.error('Update launch date error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}
