# AI Tools Website

这是一个基于 Next.js 构建的AI工具展示和提交平台，集成了Stripe支付系统。

## 功能特性

- 🔐 用户认证（Google/GitHub OAuth + 邮箱验证码）
- 🛠️ AI工具展示和搜索
- 📝 工具提交和审核流程
- 💳 Stripe支付集成（优先发布服务）
- 👤 用户个人中心
- 📊 管理员后台

## 环境配置

1. 复制环境变量文件：
```bash
cp .env.example .env.local
```

2. 配置必要的环境变量：
```bash
# MongoDB
MONGODB_URI=mongodb://localhost:27017/aitools

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Stripe (必须配置)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# OAuth providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：

```bash
npm run dev
```

3. 访问应用：
打开 [http://localhost:3000](http://localhost:3000) 查看应用

## Stripe支付集成

### 测试支付

访问 `/test-stripe` 页面可以测试Stripe支付功能。

测试卡号：
- 成功支付: `4242 4242 4242 4242`
- 支付失败: `4000 0000 0000 0002`
- 需要验证: `4000 0025 0000 3155`

使用任意未来日期作为过期时间，任意3位数作为CVC。

### Webhook配置

1. 在Stripe Dashboard中配置Webhook端点：
   - URL: `https://your-domain.com/api/stripe/webhook`
   - 事件: `payment_intent.succeeded`, `payment_intent.payment_failed`, `payment_intent.canceled`

2. 将Webhook签名密钥添加到环境变量：
```bash
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### 支付流程

1. 用户提交工具信息（保存为草稿状态）
2. 选择发布日期和付费选项
3. 创建订单和Stripe支付意图
4. 用户完成支付
5. Webhook处理支付确认
6. 工具进入审核队列

## 工具提交流程

1. **提交工具信息** - 填写基本信息，保存为草稿
2. **选择发布日期** - 免费（一个月后起）或付费（任意日期）
3. **支付处理** - 付费选项需要完成Stripe支付
4. **审核流程** - 管理员审核工具
5. **发布上线** - 按计划日期发布

### 发布选项详情

**免费发布**
- 可选择一个月后的任意日期
- 正常审核流程（1-3个工作日）
- 标准展示位置

**优先发布（¥99）**
- 可选择明天及以后的任意日期
- 优先审核处理（1个工作日内）
- 首页推荐位置展示
- 可随时修改发布日期（工具发布前）
- 专属客服支持

### 发布日期修改

**所有用户都可以修改发布日期**，只要工具还未正式发布：

- **免费用户**：可修改为一个月后的任意日期
- **付费用户**：可修改为明天及以后的任意日期
- **修改限制**：工具正式发布后不可再修改

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
