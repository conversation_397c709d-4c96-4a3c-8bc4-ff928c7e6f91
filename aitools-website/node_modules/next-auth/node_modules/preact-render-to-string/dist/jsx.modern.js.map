{"version": 3, "file": "jsx.modern.js", "sources": ["../src/polyfills.js", "../src/util.js", "../src/pretty.js", "../src/index.js", "../node_modules/pretty-format/printString.js", "../node_modules/pretty-format/index.js", "../src/jsx.js"], "sourcesContent": ["if (typeof Symbol !== 'function') {\n\tlet c = 0;\n\t// eslint-disable-next-line\n\tSymbol = function (s) {\n\t\treturn `@@${s}${++c}`;\n\t};\n\tSymbol.for = (s) => `@@${s}`;\n}\n", "// DOM properties that should NOT have \"px\" added when numeric\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;\nexport const VOID_ELEMENTS = /^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const XLINK = /^xlink:?./;\n\nconst ENCODED_ENTITIES = /[\"&<]/;\n\nexport function encodeEntities(str) {\n\t// Ensure we're always parsing and returning a string:\n\tstr += '';\n\n\t// Skip all work for strings with no entities needing encoding:\n\tif (ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out += str.slice(last, i);\n\t\tout += ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out += str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst CSS_REGEX = /([A-Z])/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tif (str) str += ' ';\n\t\t\t// str += jsToCss(prop);\n\t\t\tstr +=\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$1').toLowerCase());\n\n\t\t\tif (typeof val === 'number' && IS_NON_DIMENSIONAL.test(prop) === false) {\n\t\t\t\tstr = str + ': ' + val + 'px;';\n\t\t\t} else {\n\t\t\t\tstr = str + ': ' + val + ';';\n\t\t\t}\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: []\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n", "import {\n\tencodeEntities,\n\tindent,\n\tisLargeString,\n\tstyleObjTo<PERSON>s,\n\tgetChildren,\n\tcreateComponent,\n\tgetContext,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, Fragment } from 'preact';\n\n// components without names, kept as a hash for later comparison to return consistent UnnamedComponentXX names.\nconst UNNAMED = [];\n\nexport function _renderToStringPretty(\n\tvnode,\n\tcontext,\n\topts,\n\tinner,\n\tisSvgMode,\n\tselectValue\n) {\n\tif (vnode == null || typeof vnode === 'boolean') {\n\t\treturn '';\n\t}\n\n\t// #text nodes\n\tif (typeof vnode !== 'object') {\n\t\tif (typeof vnode === 'function') return '';\n\t\treturn encodeEntities(vnode);\n\t}\n\n\tlet pretty = opts.pretty,\n\t\tindentChar = pretty && typeof pretty === 'string' ? pretty : '\\t';\n\n\tif (Array.isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tif (pretty && i > 0) rendered = rendered + '\\n';\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToStringPretty(\n\t\t\t\t\tvnode[i],\n\t\t\t\t\tcontext,\n\t\t\t\t\topts,\n\t\t\t\t\tinner,\n\t\t\t\t\tisSvgMode,\n\t\t\t\t\tselectValue\n\t\t\t\t);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return '';\n\n\tlet nodeName = vnode.type,\n\t\tprops = vnode.props,\n\t\tisComponent = false;\n\n\t// components\n\tif (typeof nodeName === 'function') {\n\t\tisComponent = true;\n\t\tif (opts.shallow && (inner || opts.renderRootComponent === false)) {\n\t\t\tnodeName = getComponentName(nodeName);\n\t\t} else if (nodeName === Fragment) {\n\t\t\tconst children = [];\n\t\t\tgetChildren(children, vnode.props.children);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\tchildren,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t} else {\n\t\t\tlet rendered;\n\n\t\t\tlet c = (vnode.__c = createComponent(vnode, context));\n\n\t\t\t// options._diff\n\t\t\tif (options.__b) options.__b(vnode);\n\n\t\t\t// options._render\n\t\t\tlet renderHook = options.__r;\n\n\t\t\tif (\n\t\t\t\t!nodeName.prototype ||\n\t\t\t\ttypeof nodeName.prototype.render !== 'function'\n\t\t\t) {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (c.__d && count++ < 25) {\n\t\t\t\t\tc.__d = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\t// stateless functional components\n\t\t\t\t\trendered = nodeName.call(vnode.__c, props, cctx);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// c = new nodeName(props, context);\n\t\t\t\tc = vnode.__c = new nodeName(props, cctx);\n\t\t\t\tc.__v = vnode;\n\t\t\t\t// turn off stateful re-rendering:\n\t\t\t\tc._dirty = c.__d = true;\n\t\t\t\tc.props = props;\n\t\t\t\tif (c.state == null) c.state = {};\n\n\t\t\t\tif (c._nextState == null && c.__s == null) {\n\t\t\t\t\tc._nextState = c.__s = c.state;\n\t\t\t\t}\n\n\t\t\t\tc.context = cctx;\n\t\t\t\tif (nodeName.getDerivedStateFromProps)\n\t\t\t\t\tc.state = Object.assign(\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tc.state,\n\t\t\t\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t\t\t\t);\n\t\t\t\telse if (c.componentWillMount) {\n\t\t\t\t\tc.componentWillMount();\n\n\t\t\t\t\t// If the user called setState in cWM we need to flush pending,\n\t\t\t\t\t// state updates. This is the same behaviour in React.\n\t\t\t\t\tc.state =\n\t\t\t\t\t\tc._nextState !== c.state\n\t\t\t\t\t\t\t? c._nextState\n\t\t\t\t\t\t\t: c.__s !== c.state\n\t\t\t\t\t\t\t? c.__s\n\t\t\t\t\t\t\t: c.state;\n\t\t\t\t}\n\n\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\trendered = c.render(c.props, c.state, c.context);\n\t\t\t}\n\n\t\t\tif (c.getChildContext) {\n\t\t\t\tcontext = Object.assign({}, context, c.getChildContext());\n\t\t\t}\n\n\t\t\tif (options.diffed) options.diffed(vnode);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t}\n\t}\n\n\t// render JSX to HTML\n\tlet s = '<' + nodeName,\n\t\tpropChildren,\n\t\thtml;\n\n\tif (props) {\n\t\tlet attrs = Object.keys(props);\n\n\t\t// allow sorting lexicographically for more determinism (useful for tests, such as via preact-jsx-chai)\n\t\tif (opts && opts.sortAttributes === true) attrs.sort();\n\n\t\tfor (let i = 0; i < attrs.length; i++) {\n\t\t\tlet name = attrs[i],\n\t\t\t\tv = props[name];\n\t\t\tif (name === 'children') {\n\t\t\t\tpropChildren = v;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tif (\n\t\t\t\t!(opts && opts.allAttributes) &&\n\t\t\t\t(name === 'key' ||\n\t\t\t\t\tname === 'ref' ||\n\t\t\t\t\tname === '__self' ||\n\t\t\t\t\tname === '__source')\n\t\t\t)\n\t\t\t\tcontinue;\n\n\t\t\tif (name === 'defaultValue') {\n\t\t\t\tname = 'value';\n\t\t\t} else if (name === 'defaultChecked') {\n\t\t\t\tname = 'checked';\n\t\t\t} else if (name === 'defaultSelected') {\n\t\t\t\tname = 'selected';\n\t\t\t} else if (name === 'className') {\n\t\t\t\tif (typeof props.class !== 'undefined') continue;\n\t\t\t\tname = 'class';\n\t\t\t} else if (isSvgMode && XLINK.test(name)) {\n\t\t\t\tname = name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t\t\t}\n\n\t\t\tif (name === 'htmlFor') {\n\t\t\t\tif (props.for) continue;\n\t\t\t\tname = 'for';\n\t\t\t}\n\n\t\t\tif (name === 'style' && v && typeof v === 'object') {\n\t\t\t\tv = styleObjToCss(v);\n\t\t\t}\n\n\t\t\t// always use string values instead of booleans for aria attributes\n\t\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\t\tif (name[0] === 'a' && name['1'] === 'r' && typeof v === 'boolean') {\n\t\t\t\tv = String(v);\n\t\t\t}\n\n\t\t\tlet hooked =\n\t\t\t\topts.attributeHook &&\n\t\t\t\topts.attributeHook(name, v, context, opts, isComponent);\n\t\t\tif (hooked || hooked === '') {\n\t\t\t\ts = s + hooked;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (nodeName === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tpropChildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\t// in non-xml mode, allow boolean attributes\n\t\t\t\t\tif (!opts || !opts.xml) {\n\t\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (nodeName === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\tnodeName === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\ttypeof props.selected === 'undefined'\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ` selected`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ` ${name}=\"${encodeEntities(v)}\"`;\n\t\t\t}\n\t\t}\n\t}\n\n\t// account for >1 multiline attribute\n\tif (pretty) {\n\t\tlet sub = s.replace(/\\n\\s*/, ' ');\n\t\tif (sub !== s && !~sub.indexOf('\\n')) s = sub;\n\t\telse if (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t}\n\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(nodeName))\n\t\tthrow new Error(`${nodeName} is not a valid HTML tag name in ${s}`);\n\n\tlet isVoid =\n\t\tVOID_ELEMENTS.test(nodeName) ||\n\t\t(opts.voidElements && opts.voidElements.test(nodeName));\n\tlet pieces = [];\n\n\tlet children;\n\tif (html) {\n\t\t// if multiline, indent.\n\t\tif (pretty && isLargeString(html)) {\n\t\t\thtml = '\\n' + indentChar + indent(html, indentChar);\n\t\t}\n\t\ts = s + html;\n\t} else if (\n\t\tpropChildren != null &&\n\t\tgetChildren((children = []), propChildren).length\n\t) {\n\t\tlet hasLarge = pretty && ~s.indexOf('\\n');\n\t\tlet lastWasText = false;\n\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\t\tnodeName === 'svg'\n\t\t\t\t\t\t\t? true\n\t\t\t\t\t\t\t: nodeName === 'foreignObject'\n\t\t\t\t\t\t\t? false\n\t\t\t\t\t\t\t: isSvgMode,\n\t\t\t\t\tret = _renderToStringPretty(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\topts,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tchildSvgMode,\n\t\t\t\t\t\tselectValue\n\t\t\t\t\t);\n\n\t\t\t\tif (pretty && !hasLarge && isLargeString(ret)) hasLarge = true;\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tif (pretty) {\n\t\t\t\t\t\tlet isText = ret.length > 0 && ret[0] != '<';\n\n\t\t\t\t\t\t// We merge adjacent text nodes, otherwise each piece would be printed\n\t\t\t\t\t\t// on a new line.\n\t\t\t\t\t\tif (lastWasText && isText) {\n\t\t\t\t\t\t\tpieces[pieces.length - 1] += ret;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlastWasText = isText;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (pretty && hasLarge) {\n\t\t\tfor (let i = pieces.length; i--; ) {\n\t\t\t\tpieces[i] = '\\n' + indentChar + indent(pieces[i], indentChar);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (pieces.length || html) {\n\t\ts = s + pieces.join('');\n\t} else if (opts && opts.xml) {\n\t\treturn s.substring(0, s.length - 1) + ' />';\n\t}\n\n\tif (isVoid && !children && !html) {\n\t\ts = s.replace(/>$/, ' />');\n\t} else {\n\t\tif (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t\ts = s + `</${nodeName}>`;\n\t}\n\n\treturn s;\n}\n\nfunction getComponentName(component) {\n\treturn (\n\t\tcomponent.displayName ||\n\t\t(component !== Function && component.name) ||\n\t\tgetFallbackComponentName(component)\n\t);\n}\n\nfunction getFallbackComponentName(component) {\n\tlet str = Function.prototype.toString.call(component),\n\t\tname = (str.match(/^\\s*function\\s+([^( ]+)/) || '')[1];\n\tif (!name) {\n\t\t// search for an existing indexed name for the given component:\n\t\tlet index = -1;\n\t\tfor (let i = UNNAMED.length; i--; ) {\n\t\t\tif (UNNAMED[i] === component) {\n\t\t\t\tindex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\t// not found, create a new indexed name:\n\t\tif (index < 0) {\n\t\t\tindex = UNNAMED.push(component) - 1;\n\t\t}\n\t\tname = `UnnamedComponent${index}`;\n\t}\n\treturn name;\n}\n", "import {\n\tencodeEntities,\n\tstyleObjTo<PERSON>s,\n\tgetContext,\n\tcreate<PERSON>omponent,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, h, Fragment } from 'preact';\nimport { _renderToStringPretty } from './pretty';\nimport {\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tPARENT,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE,\n\tCHILDREN\n} from './constants';\n\n/** @typedef {import('preact').VNode} VNode */\n\nconst SHALLOW = { shallow: true };\n\n/** Render Preact JSX + Components to an HTML string.\n *\t@name render\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n *\t@param {Object} [options={}]\tRendering options\n *\t@param {Boolean} [options.shallow=false]\tIf `true`, renders nested Components as HTML elements (`<Foo a=\"b\" />`).\n *\t@param {Boolean} [options.xml=false]\t\tIf `true`, uses self-closing tags for elements without children.\n *\t@param {Boolean} [options.pretty=false]\t\tIf `true`, adds whitespace for readability\n *\t@param {RegExp|undefined} [options.voidElements]       RegeEx that matches elements that are considered void (self-closing)\n */\nrenderToString.render = renderToString;\n\n/** Only render elements, leaving Components inline as `<ComponentName ... />`.\n *\tThis method is just a convenience alias for `render(vnode, context, { shallow:true })`\n *\t@name shallow\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n */\nlet shallowRender = (vnode, context) => renderToString(vnode, context, SHALLOW);\n\nconst EMPTY_ARR = [];\nfunction renderToString(vnode, context, opts) {\n\tcontext = context || {};\n\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\tlet res;\n\tif (\n\t\topts &&\n\t\t(opts.pretty ||\n\t\t\topts.voidElements ||\n\t\t\topts.sortAttributes ||\n\t\t\topts.shallow ||\n\t\t\topts.allAttributes ||\n\t\t\topts.xml ||\n\t\t\topts.attributeHook)\n\t) {\n\t\tres = _renderToStringPretty(vnode, context, opts);\n\t} else {\n\t\tres = _renderToString(vnode, context, false, undefined, parent);\n\t}\n\n\t// options._commit, we don't schedule any effects in this library right now,\n\t// so we can pass an empty queue to this hook.\n\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\tEMPTY_ARR.length = 0;\n\n\treturn res;\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n * @returns {string}\n */\nfunction renderFunctionComponent(vnode, context) {\n\t// eslint-disable-next-line lines-around-comment\n\t/** @type {string} */\n\tlet rendered,\n\t\tc = createComponent(vnode, context),\n\t\tcctx = getContext(vnode.type, context);\n\n\tvnode[COMPONENT] = c;\n\n\t// If a hook invokes setState() to invalidate the component during rendering,\n\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t// Note:\n\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\tlet renderHook = options[RENDER];\n\tlet count = 0;\n\twhile (c[DIRTY] && count++ < 25) {\n\t\tc[DIRTY] = false;\n\n\t\tif (renderHook) renderHook(vnode);\n\n\t\t// stateless functional components\n\t\trendered = vnode.type.call(c, vnode.props, cctx);\n\t}\n\n\treturn rendered;\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n * @returns {VNode}\n */\nfunction renderClassComponent(vnode, context) {\n\tlet nodeName = vnode.type,\n\t\tcctx = getContext(nodeName, context);\n\n\t/** @type {import(\"preact\").Component} */\n\tlet c = new nodeName(vnode.props, cctx);\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\tc.props = vnode.props;\n\tif (c.state == null) c.state = {};\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tc.context = cctx;\n\tif (nodeName.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t}\n\n\tlet renderHook = options[RENDER];\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, c.context);\n}\n\n/**\n * @param {any} vnode\n * @returns {VNode}\n */\nfunction normalizeVNode(vnode) {\n\tif (vnode == null || typeof vnode == 'boolean') {\n\t\treturn null;\n\t} else if (\n\t\ttypeof vnode == 'string' ||\n\t\ttypeof vnode == 'number' ||\n\t\ttypeof vnode == 'bigint'\n\t) {\n\t\treturn h(null, null, vnode);\n\t}\n\treturn vnode;\n}\n\n/**\n * @param {string} name\n * @param {boolean} isSvgMode\n * @returns {string}\n */\nfunction normalizePropName(name, isSvgMode) {\n\tif (name === 'className') {\n\t\treturn 'class';\n\t} else if (name === 'htmlFor') {\n\t\treturn 'for';\n\t} else if (name === 'defaultValue') {\n\t\treturn 'value';\n\t} else if (name === 'defaultChecked') {\n\t\treturn 'checked';\n\t} else if (name === 'defaultSelected') {\n\t\treturn 'selected';\n\t} else if (isSvgMode && XLINK.test(name)) {\n\t\treturn name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t}\n\n\treturn name;\n}\n\n/**\n * @param {string} name\n * @param {string | Record<string, unknown>} v\n * @returns {string}\n */\nfunction normalizePropValue(name, v) {\n\tif (name === 'style' && v != null && typeof v === 'object') {\n\t\treturn styleObjToCss(v);\n\t} else if (name[0] === 'a' && name[1] === 'r' && typeof v === 'boolean') {\n\t\t// always use string values instead of booleans for aria attributes\n\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\treturn String(v);\n\t}\n\n\treturn v;\n}\n\nconst isArray = Array.isArray;\nconst assign = Object.assign;\n\n/**\n * The default export is an alias of `render()`.\n * @param {any} vnode\n * @param {Record<string, unknown>} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode | null} parent\n * @returns {string}\n */\nfunction _renderToString(vnode, context, isSvgMode, selectValue, parent) {\n\t// Ignore non-rendered VNodes/values\n\tif (vnode == null || vnode === true || vnode === false || vnode === '') {\n\t\treturn '';\n\t}\n\n\t// Text VNodes: escape as HTML\n\tif (typeof vnode !== 'object') {\n\t\tif (typeof vnode === 'function') return '';\n\t\treturn encodeEntities(vnode);\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tparent[CHILDREN] = vnode;\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToString(vnode[i], context, isSvgMode, selectValue, parent);\n\n\t\t\tvnode[i] = normalizeVNode(vnode[i]);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return '';\n\n\tvnode[PARENT] = parent;\n\tif (options[DIFF]) options[DIFF](vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tconst isComponent = typeof type === 'function';\n\tif (isComponent) {\n\t\tlet rendered;\n\t\tif (type === Fragment) {\n\t\t\trendered = props.children;\n\t\t} else {\n\t\t\tif (type.prototype && typeof type.prototype.render === 'function') {\n\t\t\t\trendered = renderClassComponent(vnode, context);\n\t\t\t} else {\n\t\t\t\trendered = renderFunctionComponent(vnode, context);\n\t\t\t}\n\n\t\t\tlet component = vnode[COMPONENT];\n\t\t\tif (component.getChildContext) {\n\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t}\n\t\t}\n\n\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t// need to mirror that logic here too\n\t\tlet isTopLevelFragment =\n\t\t\trendered != null && rendered.type === Fragment && rendered.key == null;\n\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t// Recurse into children before invoking the after-diff hook\n\t\tconst str = _renderToString(\n\t\t\trendered,\n\t\t\tcontext,\n\t\t\tisSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode\n\t\t);\n\n\t\tif (options[DIFFED]) options[DIFFED](vnode);\n\t\tvnode[PARENT] = undefined;\n\n\t\tif (options.unmount) options.unmount(vnode);\n\n\t\treturn str;\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<',\n\t\tchildren,\n\t\thtml;\n\n\ts = s + type;\n\n\tif (props) {\n\t\tchildren = props.children;\n\t\tfor (let name in props) {\n\t\t\tlet v = props[name];\n\n\t\t\tif (\n\t\t\t\tname === 'key' ||\n\t\t\t\tname === 'ref' ||\n\t\t\t\tname === '__self' ||\n\t\t\t\tname === '__source' ||\n\t\t\t\tname === 'children' ||\n\t\t\t\t(name === 'className' && 'class' in props) ||\n\t\t\t\t(name === 'htmlFor' && 'for' in props)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tname = normalizePropName(name, isSvgMode);\n\t\t\tv = normalizePropValue(name, v);\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (type === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tchildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (type === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\ttype === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\t!('selected' in props)\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ' ' + name + '=\"' + encodeEntities(v) + '\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tlet startElement = s;\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}`);\n\t}\n\n\tlet pieces = '';\n\tlet hasChildren = false;\n\n\tif (html) {\n\t\tpieces = pieces + html;\n\t\thasChildren = true;\n\t} else if (typeof children === 'string') {\n\t\tpieces = pieces + encodeEntities(children);\n\t\thasChildren = true;\n\t} else if (isArray(children)) {\n\t\tvnode[CHILDREN] = children;\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\t\t\tchildren[i] = normalizeVNode(child);\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\t\t\tlet ret = _renderToString(\n\t\t\t\t\tchild,\n\t\t\t\t\tcontext,\n\t\t\t\t\tchildSvgMode,\n\t\t\t\t\tselectValue,\n\t\t\t\t\tvnode\n\t\t\t\t);\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tpieces = pieces + ret;\n\t\t\t\t\thasChildren = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (children != null && children !== false && children !== true) {\n\t\tvnode[CHILDREN] = [normalizeVNode(children)];\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\tlet ret = _renderToString(\n\t\t\tchildren,\n\t\t\tcontext,\n\t\t\tchildSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode\n\t\t);\n\n\t\t// Skip if we received an empty string\n\t\tif (ret) {\n\t\t\tpieces = pieces + ret;\n\t\t\thasChildren = true;\n\t\t}\n\t}\n\n\tif (options[DIFFED]) options[DIFFED](vnode);\n\tvnode[PARENT] = undefined;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif (hasChildren) {\n\t\ts = s + pieces;\n\t} else if (VOID_ELEMENTS.test(type)) {\n\t\treturn startElement + ' />';\n\t}\n\n\treturn s + '</' + type + '>';\n}\n\n/** The default export is an alias of `render()`. */\n\nrenderToString.shallowRender = shallowRender;\n\nexport default renderToString;\n\nexport {\n\trenderToString as render,\n\trenderToString as renderToStaticMarkup,\n\trenderToString,\n\tshallowRender\n};\n", "'use strict';\n\nconst ESCAPED_CHARACTERS = /(\\\\|\\\"|\\')/g;\n\nmodule.exports = function printString(val) {\n  return val.replace(ESCAPED_CHARACTERS, '\\\\$1');\n}\n", "'use strict';\n\nconst printString = require('./printString');\n\nconst toString = Object.prototype.toString;\nconst toISOString = Date.prototype.toISOString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = Symbol.prototype.toString;\n\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nconst NEWLINE_REGEXP = /\\n/ig;\n\nconst getSymbols = Object.getOwnPropertySymbols || (obj => []);\n\nfunction isToStringedArrayType(toStringed) {\n  return (\n    toStringed === '[object Array]' ||\n    toStringed === '[object ArrayBuffer]' ||\n    toStringed === '[object DataView]' ||\n    toStringed === '[object Float32Array]' ||\n    toStringed === '[object Float64Array]' ||\n    toStringed === '[object Int8Array]' ||\n    toStringed === '[object Int16Array]' ||\n    toStringed === '[object Int32Array]' ||\n    toStringed === '[object Uint8Array]' ||\n    toStringed === '[object Uint8ClampedArray]' ||\n    toStringed === '[object Uint16Array]' ||\n    toStringed === '[object Uint32Array]'\n  );\n}\n\nfunction printNumber(val) {\n  if (val != +val) return 'NaN';\n  const isNegativeZero = val === 0 && (1 / val) < 0;\n  return isNegativeZero ? '-0' : '' + val;\n}\n\nfunction printFunction(val) {\n  if (val.name === '') {\n    return '[Function anonymous]'\n  } else {\n    return '[Function ' + val.name + ']';\n  }\n}\n\nfunction printSymbol(val) {\n  return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');\n}\n\nfunction printError(val) {\n  return '[' + errorToString.call(val) + ']';\n}\n\nfunction printBasicValue(val) {\n  if (val === true || val === false) return '' + val;\n  if (val === undefined) return 'undefined';\n  if (val === null) return 'null';\n\n  const typeOf = typeof val;\n\n  if (typeOf === 'number') return printNumber(val);\n  if (typeOf === 'string') return '\"' + printString(val) + '\"';\n  if (typeOf === 'function') return printFunction(val);\n  if (typeOf === 'symbol') return printSymbol(val);\n\n  const toStringed = toString.call(val);\n\n  if (toStringed === '[object WeakMap]') return 'WeakMap {}';\n  if (toStringed === '[object WeakSet]') return 'WeakSet {}';\n  if (toStringed === '[object Function]' || toStringed === '[object GeneratorFunction]') return printFunction(val, min);\n  if (toStringed === '[object Symbol]') return printSymbol(val);\n  if (toStringed === '[object Date]') return toISOString.call(val);\n  if (toStringed === '[object Error]') return printError(val);\n  if (toStringed === '[object RegExp]') return regExpToString.call(val);\n  if (toStringed === '[object Arguments]' && val.length === 0) return 'Arguments []';\n  if (isToStringedArrayType(toStringed) && val.length === 0) return val.constructor.name + ' []';\n\n  if (val instanceof Error) return printError(val);\n\n  return false;\n}\n\nfunction printList(list, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let body = '';\n\n  if (list.length) {\n    body += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    for (let i = 0; i < list.length; i++) {\n      body += innerIndent + print(list[i], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      if (i < list.length - 1) {\n        body += ',' + spacing;\n      }\n    }\n\n    body += edgeSpacing + prevIndent;\n  }\n\n  return '[' + body + ']';\n}\n\nfunction printArguments(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  return (min ? '' : 'Arguments ') + printList(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n}\n\nfunction printArray(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  return (min ? '' : val.constructor.name + ' ') + printList(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n}\n\nfunction printMap(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let result = 'Map {';\n  const iterator = val.entries();\n  let current = iterator.next();\n\n  if (!current.done) {\n    result += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    while (!current.done) {\n      const key = print(current.value[0], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n      const value = print(current.value[1], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      result += innerIndent + key + ' => ' + value;\n\n      current = iterator.next();\n\n      if (!current.done) {\n        result += ',' + spacing;\n      }\n    }\n\n    result += edgeSpacing + prevIndent;\n  }\n\n  return result + '}';\n}\n\nfunction printObject(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  const constructor = min ? '' : (val.constructor ?  val.constructor.name + ' ' : 'Object ');\n  let result = constructor + '{';\n  let keys = Object.keys(val).sort();\n  const symbols = getSymbols(val);\n\n  if (symbols.length) {\n    keys = keys\n      .filter(key => !(typeof key === 'symbol' || toString.call(key) === '[object Symbol]'))\n      .concat(symbols);\n  }\n\n  if (keys.length) {\n    result += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const name = print(key, indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n      const value = print(val[key], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      result += innerIndent + name + ': ' + value;\n\n      if (i < keys.length - 1) {\n        result += ',' + spacing;\n      }\n    }\n\n    result += edgeSpacing + prevIndent;\n  }\n\n  return result + '}';\n}\n\nfunction printSet(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let result = 'Set {';\n  const iterator = val.entries();\n  let current = iterator.next();\n\n  if (!current.done) {\n    result += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    while (!current.done) {\n      result += innerIndent + print(current.value[1], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      current = iterator.next();\n\n      if (!current.done) {\n        result += ',' + spacing;\n      }\n    }\n\n    result += edgeSpacing + prevIndent;\n  }\n\n  return result + '}';\n}\n\nfunction printComplexValue(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  refs = refs.slice();\n  if (refs.indexOf(val) > -1) {\n    return '[Circular]';\n  } else {\n    refs.push(val);\n  }\n\n  currentDepth++;\n\n  const hitMaxDepth = currentDepth > maxDepth;\n\n  if (!hitMaxDepth && val.toJSON && typeof val.toJSON === 'function') {\n    return print(val.toJSON(), indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  }\n\n  const toStringed = toString.call(val);\n  if (toStringed === '[object Arguments]') {\n    return hitMaxDepth ? '[Arguments]' : printArguments(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (isToStringedArrayType(toStringed)) {\n    return hitMaxDepth ? '[Array]' : printArray(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (toStringed === '[object Map]') {\n    return hitMaxDepth ? '[Map]' : printMap(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (toStringed === '[object Set]') {\n    return hitMaxDepth ? '[Set]' : printSet(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (typeof val === 'object') {\n    return hitMaxDepth ? '[Object]' : printObject(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  }\n}\n\nfunction printPlugin(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let match = false;\n  let plugin;\n\n  for (let p = 0; p < plugins.length; p++) {\n    plugin = plugins[p];\n\n    if (plugin.test(val)) {\n      match = true;\n      break;\n    }\n  }\n\n  if (!match) {\n    return false;\n  }\n\n  function boundPrint(val) {\n    return print(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  }\n\n  function boundIndent(str) {\n    const indentation = prevIndent + indent;\n    return indentation + str.replace(NEWLINE_REGEXP, '\\n' + indentation);\n  }\n\n  return plugin.print(val, boundPrint, boundIndent, {\n    edgeSpacing: edgeSpacing,\n    spacing: spacing\n  });\n}\n\nfunction print(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  const basic = printBasicValue(val);\n  if (basic) return basic;\n\n  const plugin = printPlugin(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  if (plugin) return plugin;\n\n  return printComplexValue(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n}\n\nconst DEFAULTS = {\n  indent: 2,\n  min: false,\n  maxDepth: Infinity,\n  plugins: []\n};\n\nfunction validateOptions(opts) {\n  Object.keys(opts).forEach(key => {\n    if (!DEFAULTS.hasOwnProperty(key)) {\n      throw new Error('prettyFormat: Invalid option: ' + key);\n    }\n  });\n\n  if (opts.min && opts.indent !== undefined && opts.indent !== 0) {\n    throw new Error('prettyFormat: Cannot run with min option and indent');\n  }\n}\n\nfunction normalizeOptions(opts) {\n  const result = {};\n\n  Object.keys(DEFAULTS).forEach(key =>\n    result[key] = opts.hasOwnProperty(key) ? opts[key] : DEFAULTS[key]\n  );\n\n  if (result.min) {\n    result.indent = 0;\n  }\n\n  return result;\n}\n\nfunction createIndent(indent) {\n  return new Array(indent + 1).join(' ');\n}\n\nfunction prettyFormat(val, opts) {\n  if (!opts) {\n    opts = DEFAULTS;\n  } else {\n    validateOptions(opts)\n    opts = normalizeOptions(opts);\n  }\n\n  let indent;\n  let refs;\n  const prevIndent = '';\n  const currentDepth = 0;\n  const spacing = opts.min ? ' ' : '\\n';\n  const edgeSpacing = opts.min ? '' : '\\n';\n\n  if (opts && opts.plugins.length) {\n    indent = createIndent(opts.indent);\n    refs = [];\n    var pluginsResult = printPlugin(val, indent, prevIndent, spacing, edgeSpacing, refs, opts.maxDepth, currentDepth, opts.plugins, opts.min);\n    if (pluginsResult) return pluginsResult;\n  }\n\n  var basicResult = printBasicValue(val);\n  if (basicResult) return basicResult;\n\n  if (!indent) indent = createIndent(opts.indent);\n  if (!refs) refs = [];\n  return printComplexValue(val, indent, prevIndent, spacing, edgeSpacing, refs, opts.maxDepth, currentDepth, opts.plugins, opts.min);\n}\n\nmodule.exports = prettyFormat;\n", "import './polyfills';\nimport renderToString from './index';\nimport { indent, encodeEntities } from './util';\nimport prettyFormat from 'pretty-format';\n\n// we have to patch in Array support, Possible issue in npm.im/pretty-format\nlet preactPlugin = {\n\ttest(object) {\n\t\treturn (\n\t\t\tobject &&\n\t\t\ttypeof object === 'object' &&\n\t\t\t'type' in object &&\n\t\t\t'props' in object &&\n\t\t\t'key' in object\n\t\t);\n\t},\n\tprint(val, print, indent) {\n\t\treturn renderToString(val, preactPlugin.context, preactPlugin.opts, true);\n\t}\n};\n\nlet prettyFormatOpts = {\n\tplugins: [preactPlugin]\n};\n\nfunction attributeHook(name, value, context, opts, isComponent) {\n\tlet type = typeof value;\n\n\t// Use render-to-string's built-in handling for these properties\n\tif (name === 'dangerouslySetInnerHTML') return false;\n\n\t// always skip null & undefined values, skip false DOM attributes, skip functions if told to\n\tif (value == null || (type === 'function' && !opts.functions)) return '';\n\n\tif (\n\t\topts.skipFalseAttributes &&\n\t\t!isComponent &&\n\t\t(value === false ||\n\t\t\t((name === 'class' || name === 'style') && value === ''))\n\t)\n\t\treturn '';\n\n\tlet indentChar = typeof opts.pretty === 'string' ? opts.pretty : '\\t';\n\tif (type !== 'string') {\n\t\tif (type === 'function' && !opts.functionNames) {\n\t\t\tvalue = 'Function';\n\t\t} else {\n\t\t\tpreactPlugin.context = context;\n\t\t\tpreactPlugin.opts = opts;\n\t\t\tvalue = prettyFormat(value, prettyFormatOpts);\n\t\t\tif (~value.indexOf('\\n')) {\n\t\t\t\tvalue = `${indent('\\n' + value, indentChar)}\\n`;\n\t\t\t}\n\t\t}\n\t\treturn indent(`\\n${name}={${value}}`, indentChar);\n\t}\n\treturn `\\n${indentChar}${name}=\"${encodeEntities(value)}\"`;\n}\n\nlet defaultOpts = {\n\tattributeHook,\n\tjsx: true,\n\txml: false,\n\tfunctions: true,\n\tfunctionNames: true,\n\tskipFalseAttributes: true,\n\tpretty: '  '\n};\n\nfunction renderToJsxString(vnode, context, opts, inner) {\n\topts = Object.assign({}, defaultOpts, opts || {});\n\treturn renderToString(vnode, context, opts, inner);\n}\n\nexport default renderToJsxString;\nexport { renderToJsxString as render };\n"], "names": ["Symbol", "c", "s", "for", "IS_NON_DIMENSIONAL", "VOID_ELEMENTS", "UNSAFE_NAME", "XLINK", "ENCODED_ENTITIES", "encodeEntities", "str", "test", "last", "i", "out", "ch", "length", "charCodeAt", "slice", "indent", "char", "String", "replace", "isLargeString", "ignoreLines", "indexOf", "JS_TO_CSS", "CSS_REGEX", "styleObjToCss", "prop", "val", "toLowerCase", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accumulator", "children", "Array", "isArray", "reduce", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "getContext", "nodeName", "cxType", "contextType", "provider", "__c", "value", "__", "UNNAMED", "_renderToStringPretty", "opts", "inner", "isSvgMode", "selectValue", "pretty", "indentChar", "rendered", "constructor", "type", "isComponent", "shallow", "renderRootComponent", "Fragment", "shallowHighOrder", "options", "__b", "renderHook", "__r", "prototype", "render", "cctx", "_dirty", "state", "_nextState", "__s", "getDerivedStateFromProps", "Object", "assign", "componentWillMount", "count", "call", "getChildContext", "diffed", "component", "displayName", "Function", "name", "toString", "match", "index", "getFallbackComponentName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "attrs", "keys", "sortAttributes", "sort", "v", "allAttributes", "class", "hooked", "attributeHook", "__html", "xml", "selected", "sub", "Error", "isVoid", "voidElements", "pieces", "<PERSON><PERSON><PERSON><PERSON>", "lastWasText", "child", "ret", "isText", "join", "substring", "SHALLOW", "renderToString", "EMPTY_ARR", "previousSkipEffects", "parent", "h", "res", "_renderToString", "normalizeVNode", "normalizePropName", "normalizePropValue", "renderClassComponent", "renderFunctionComponent", "key", "unmount", "startElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shallowRender", "ESCAPED_CHARACTERS", "toISOString", "Date", "errorToString", "regExpToString", "RegExp", "symbolToString", "SYMBOL_REGEXP", "NEWLINE_REGEXP", "getSymbols", "getOwnPropertySymbols", "obj", "isToStringedArrayType", "toStringed", "printFunction", "printSymbol", "printError", "printBasicValue", "typeOf", "printNumber", "printString", "min", "printList", "list", "prevIndent", "spacing", "edgeSpacing", "refs", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "plugins", "body", "innerIndent", "print", "printComplexValue", "hitMaxDepth", "toJSON", "printArguments", "printArray", "result", "iterator", "entries", "current", "next", "done", "printMap", "printSet", "symbols", "filter", "concat", "printObject", "printPlugin", "plugin", "p", "indentation", "DEFAULTS", "Infinity", "createIndent", "preactPlugin", "object", "prettyFormatOpts", "defaultOpts", "functions", "skipFalseAttributes", "functionNames", "for<PERSON>ach", "hasOwnProperty", "validateOptions", "normalizeOptions", "pluginsResult", "prettyFormat", "jsx", "renderToJsxString"], "mappings": "sDAAA,GAAsB,mBAAXA,OAAuB,CACjC,IAAIC,EAAI,EAERD,OAAS,SAAUE,GAClB,MAAQ,KAAIA,MAAMD,KAEnBD,OAAOG,IAAOD,GAAO,KAAIA,UCLbE,EAAqB,kEACrBC,EAAgB,2EAChBC,EAAc,mBACdC,EAAQ,YAEfC,EAAmB,iBAETC,EAAeC,GAK9B,IAAmC,IAA/BF,EAAiBG,KAHrBD,GAAO,IAGmC,OAAOA,EAEjD,IAAIE,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGN,KAAOF,EAAIH,EAAIM,OAAQH,IAAK,CAC3B,OAAQH,EAAIO,WAAWJ,IACtB,QACCE,EAAK,SACL,MACD,QACCA,EAAK,QACL,MACD,QACCA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAOJ,EAAIQ,MAAMN,EAAMC,IACvCC,GAAOC,EAEPH,EAAOC,EAAI,EAGZ,OADIA,IAAMD,IAAME,GAAOJ,EAAIQ,MAAMN,EAAMC,IAChCC,MAGGK,EAAS,CAACjB,EAAGkB,IACvBC,OAAOnB,GAAGoB,QAAQ,SAAU,MAAQF,GAAQ,OAElCG,EAAgB,CAACrB,EAAGc,EAAQQ,IACtCH,OAAOnB,GAAGc,QAAUA,GAAU,MAC5BQ,IAA4C,IAA7BH,OAAOnB,GAAGuB,QAAQ,QACP,IAA5BJ,OAAOnB,GAAGuB,QAAQ,KAEnB,MAAMC,EAAY,GAEZC,EAAY,oBAEFC,EAAc1B,GAC7B,IAAIQ,EAAM,GACV,IAAK,IAAImB,KAAQ3B,EAAG,CACnB,IAAI4B,EAAM5B,EAAE2B,GACD,MAAPC,GAAuB,KAARA,IACdpB,IAAKA,GAAO,KAEhBA,GACY,KAAXmB,EAAK,GACFA,EACAH,EAAUG,KACTH,EAAUG,GAAQA,EAAKP,QAAQK,EAAW,OAAOI,eAGrDrB,EADkB,iBAARoB,IAAsD,IAAlC1B,EAAmBO,KAAKkB,GAChDnB,EAAM,KAAOoB,EAAM,MAEnBpB,EAAM,KAAOoB,EAAM,KAI5B,OAAOpB,QAAOsB,WAUCC,EAAYC,EAAaC,GAMxC,OALIC,MAAMC,QAAQF,GACjBA,EAASG,OAAOL,EAAaC,GACP,MAAZC,IAAiC,IAAbA,GAC9BD,EAAYK,KAAKJ,GAEXD,EAGR,SAASM,IACRC,KAAKC,KAAM,WAGIC,EAAgBC,EAAOC,GACtC,MAAO,CACNC,IAAKF,EACLC,QAAAA,EACAE,MAAOH,EAAMG,MAEbC,SAAUR,EACVS,YAAaT,EACbE,KAAK,EAELQ,IAAK,aAMSC,EAAWC,EAAUP,GACpC,IAAIQ,EAASD,EAASE,YAClBC,EAAWF,GAAUR,EAAQQ,EAAOG,KACxC,OAAiB,MAAVH,EACJE,EACCA,EAASR,MAAMU,MACfJ,EAAOK,GACRb,EC5GJ,MAAMc,EAAU,YAEAC,EACfhB,EACAC,EACAgB,EACAC,EACAC,EACAC,GAEA,GAAa,MAATpB,GAAkC,kBAAVA,EAC3B,MAAO,GAIR,GAAqB,iBAAVA,EACV,MAAqB,mBAAVA,EAA6B,GACjCnC,EAAemC,GAGvB,IAAIqB,EAASJ,EAAKI,OACjBC,EAAaD,GAA4B,iBAAXA,EAAsBA,EAAS,KAE9D,GAAI7B,MAAMC,QAAQO,GAAQ,CACzB,IAAIuB,EAAW,GACf,IAAK,IAAItD,EAAI,EAAGA,EAAI+B,EAAM5B,OAAQH,IAC7BoD,GAAUpD,EAAI,IAAGsD,GAAsB,MAC3CA,GAECP,EACChB,EAAM/B,GACNgC,EACAgB,EACAC,EACAC,EACAC,GAGH,OAAOG,EAIR,QAA0BnC,IAAtBY,EAAMwB,YAA2B,MAAO,GAE5C,IAAIhB,EAAWR,EAAMyB,KACpBtB,EAAQH,EAAMG,MACduB,GAAc,EAGf,GAAwB,mBAAblB,EAAyB,CAEnC,GADAkB,GAAc,GACVT,EAAKU,UAAYT,IAAsC,IAA7BD,EAAKW,wBAExBpB,IAAaqB,EAAU,CACjC,MAAMtC,EAAW,GAEjB,OADAF,EAAYE,EAAUS,EAAMG,MAAMZ,UAC3ByB,EACNzB,EACAU,EACAgB,GAC0B,IAA1BA,EAAKa,iBACLX,EACAC,GAEK,CACN,IAAIG,EAEAlE,EAAK2C,EAAMY,IAAMb,EAAgBC,EAAOC,GAGxC8B,EAAQC,KAAKD,EAAQC,IAAIhC,GAG7B,IAAIiC,EAAaF,EAAQG,IAEzB,GACE1B,EAAS2B,WAC2B,mBAA9B3B,EAAS2B,UAAUC,OAkBpB,CACN,IAAIC,EAAO9B,EAAWC,EAAUP,GAGhC5C,EAAI2C,EAAMY,IAAM,IAAIJ,EAASL,EAAOkC,GACpChF,EAAE6C,IAAMF,EAER3C,EAAEiF,OAASjF,EAAEyC,KAAM,EACnBzC,EAAE8C,MAAQA,EACK,MAAX9C,EAAEkF,QAAelF,EAAEkF,MAAQ,IAEX,MAAhBlF,EAAEmF,YAA+B,MAATnF,EAAEoF,MAC7BpF,EAAEmF,WAAanF,EAAEoF,IAAMpF,EAAEkF,OAG1BlF,EAAE4C,QAAUoC,EACR7B,EAASkC,yBACZrF,EAAEkF,MAAQI,OAAOC,OAChB,GACAvF,EAAEkF,MACF/B,EAASkC,yBAAyBrF,EAAE8C,MAAO9C,EAAEkF,QAEtClF,EAAEwF,qBACVxF,EAAEwF,qBAIFxF,EAAEkF,MACDlF,EAAEmF,aAAenF,EAAEkF,MAChBlF,EAAEmF,WACFnF,EAAEoF,MAAQpF,EAAEkF,MACZlF,EAAEoF,IACFpF,EAAEkF,OAGHN,GAAYA,EAAWjC,GAE3BuB,EAAWlE,EAAE+E,OAAO/E,EAAE8C,MAAO9C,EAAEkF,MAAOlF,EAAE4C,aAtDvC,CACD,IAAIoC,EAAO9B,EAAWC,EAAUP,GAO5B6C,EAAQ,EACZ,KAAOzF,EAAEyC,KAAOgD,IAAU,IACzBzF,EAAEyC,KAAM,EAEJmC,GAAYA,EAAWjC,GAG3BuB,EAAWf,EAASuC,KAAK/C,EAAMY,IAAKT,EAAOkC,GA+C7C,OALIhF,EAAE2F,kBACL/C,EAAU0C,OAAOC,OAAO,GAAI3C,EAAS5C,EAAE2F,oBAGpCjB,EAAQkB,QAAQlB,EAAQkB,OAAOjD,GAC5BgB,EACNO,EACAtB,EACAgB,GAC0B,IAA1BA,EAAKa,iBACLX,EACAC,IA9FDZ,GAsSuB0C,EAtSK1C,GAwSnB2C,aACTD,IAAcE,UAAYF,EAAUG,MAKvC,SAAkCH,GACjC,IACCG,GADSD,SAASjB,UAAUmB,SAASP,KAAKG,GAC9BK,MAAM,4BAA8B,IAAI,GACrD,IAAKF,EAAM,CAEV,IAAIG,GAAS,EACb,IAAK,IAAIvF,EAAI8C,EAAQ3C,OAAQH,KAC5B,GAAI8C,EAAQ9C,KAAOiF,EAAW,CAC7BM,EAAQvF,EACR,MAIEuF,EAAQ,IACXA,EAAQzC,EAAQpB,KAAKuD,GAAa,GAEnCG,EAAQ,mBAAkBG,IAE3B,OAAOH,EAtBNI,CAAyBP,GAJ3B,IAA0BA,EAlMzB,IACCQ,EACAC,EAFGrG,EAAI,IAAMkD,EAId,GAAIL,EAAO,CACV,IAAIyD,EAAQjB,OAAOkB,KAAK1D,GAGpBc,IAAgC,IAAxBA,EAAK6C,gBAAyBF,EAAMG,OAEhD,IAAK,IAAI9F,EAAI,EAAGA,EAAI2F,EAAMxF,OAAQH,IAAK,CACtC,IAAIoF,EAAOO,EAAM3F,GAChB+F,EAAI7D,EAAMkD,GACX,GAAa,aAATA,EAAqB,CACxBK,EAAeM,EACf,SAGD,GAAItG,EAAYK,KAAKsF,GAAO,SAE5B,KACGpC,GAAQA,EAAKgD,eACL,QAATZ,GACS,QAATA,GACS,WAATA,GACS,aAATA,GAED,SAED,GAAa,iBAATA,EACHA,EAAO,gBACY,mBAATA,EACVA,EAAO,kBACY,oBAATA,EACVA,EAAO,mBACY,cAATA,EAAsB,CAChC,QAA2B,IAAhBlD,EAAM+D,MAAuB,SACxCb,EAAO,aACGlC,GAAaxD,EAAMI,KAAKsF,KAClCA,EAAOA,EAAKlE,cAAcT,QAAQ,WAAY,WAG/C,GAAa,YAAT2E,EAAoB,CACvB,GAAIlD,EAAM5C,IAAK,SACf8F,EAAO,MAGK,UAATA,GAAoBW,GAAkB,iBAANA,IACnCA,EAAIhF,EAAcgF,IAKH,MAAZX,EAAK,IAA4B,MAAdA,EAAK,IAA6B,kBAANW,IAClDA,EAAIvF,OAAOuF,IAGZ,IAAIG,EACHlD,EAAKmD,eACLnD,EAAKmD,cAAcf,EAAMW,EAAG/D,EAASgB,EAAMS,GAC5C,GAAIyC,GAAqB,KAAXA,EACb7G,GAAQ6G,OAIT,GAAa,4BAATd,EACHM,EAAOK,GAAKA,EAAEK,eACS,aAAb7D,GAAoC,UAAT6C,EAErCK,EAAeM,WACJA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,MAAU,IAANA,GAAoB,KAANA,IACjBA,EAAIX,EAECpC,GAASA,EAAKqD,MAAK,CACvBhH,EAAIA,EAAI,IAAM+F,EACd,SAIF,GAAa,UAATA,EAAkB,CACrB,GAAiB,WAAb7C,EAAuB,CAC1BY,EAAc4C,EACd,SAGa,WAAbxD,GACAY,GAAe4C,QAEW,IAAnB7D,EAAMoE,WAEbjH,GAAS,aAGXA,GAAS,IAAG+F,MAASxF,EAAemG,QAMvC,GAAI3C,EAAQ,CACX,IAAImD,EAAMlH,EAAEoB,QAAQ,QAAS,KACzB8F,IAAQlH,IAAOkH,EAAI3F,QAAQ,MACtBwC,IAAW/D,EAAEuB,QAAQ,QAAOvB,GAAQ,MADPA,EAAIkH,EAM3C,GAFAlH,GAAQ,IAEJI,EAAYK,KAAKyC,GACpB,UAAUiE,MAAO,GAAEjE,qCAA4ClD,KAEhE,IAKIiC,EALAmF,EACHjH,EAAcM,KAAKyC,IAClBS,EAAK0D,cAAgB1D,EAAK0D,aAAa5G,KAAKyC,GAC1CoE,EAAS,GAGb,GAAIjB,EAECtC,GAAU1C,EAAcgF,KAC3BA,EAAO,KAAOrC,EAAa/C,EAAOoF,EAAMrC,IAEzChE,GAAQqG,UAEQ,MAAhBD,GACArE,EAAaE,EAAW,GAAKmE,GAActF,OAC1C,CACD,IAAIyG,EAAWxD,IAAW/D,EAAEuB,QAAQ,MAChCiG,GAAc,EAElB,IAAK,IAAI7G,EAAI,EAAGA,EAAIsB,EAASnB,OAAQH,IAAK,CACzC,IAAI8G,EAAQxF,EAAStB,GAErB,GAAa,MAAT8G,IAA2B,IAAVA,EAAiB,CACrC,IAMCC,EAAMhE,EACL+D,EACA9E,EACAgB,GACA,EATa,QAAbT,GAEgB,kBAAbA,GAEAW,EAOHC,GAMF,GAHIC,IAAWwD,GAAYlG,EAAcqG,KAAMH,GAAW,GAGtDG,EACH,GAAI3D,EAAQ,CACX,IAAI4D,EAASD,EAAI5G,OAAS,GAAe,KAAV4G,EAAI,GAI/BF,GAAeG,EAClBL,EAAOA,EAAOxG,OAAS,IAAM4G,EAE7BJ,EAAOjF,KAAKqF,GAGbF,EAAcG,OAEdL,EAAOjF,KAAKqF,IAKhB,GAAI3D,GAAUwD,EACb,IAAK,IAAI5G,EAAI2G,EAAOxG,OAAQH,KAC3B2G,EAAO3G,GAAK,KAAOqD,EAAa/C,EAAOqG,EAAO3G,GAAIqD,GAKrD,GAAIsD,EAAOxG,QAAUuF,EACpBrG,GAAQsH,EAAOM,KAAK,YACVjE,GAAQA,EAAKqD,IACvB,OAAOhH,EAAE6H,UAAU,EAAG7H,EAAEc,OAAS,GAAK,MAUvC,OAPIsG,GAAWnF,GAAaoE,GAGvBtC,IAAW/D,EAAEuB,QAAQ,QAAOvB,GAAQ,MACxCA,GAAS,KAAIkD,MAHblD,EAAIA,EAAEoB,QAAQ,KAAM,OAMdpB,QC3UF8H,EAAU,CAAEzD,SAAS,GAa3B0D,EAAejD,OAASiD,EAWxB,MAAMC,EAAY,GAClB,SAASD,EAAerF,EAAOC,EAASgB,GACvChB,EAAUA,GAAW,GAOrB,MAAMsF,EAAsBxD,EAAO,IACnCA,EAAO,KAAiB,EAExB,MAAMyD,EAASC,EAAE5D,EAAU,MAG3B,IAAI6D,EAsBJ,OAxBAF,EAAM,IAAa,CAACxF,GAanB0F,EATAzE,IACCA,EAAKI,QACLJ,EAAK0D,cACL1D,EAAK6C,gBACL7C,EAAKU,SACLV,EAAKgD,eACLhD,EAAKqD,KACLrD,EAAKmD,eAEApD,EAAsBhB,EAAOC,EAASgB,GAEtC0E,EAAgB3F,EAAOC,GAAS,OAAOb,EAAWoG,GAKrDzD,EAAO,KAAUA,EAAO,IAAS/B,EAAOsF,GAC5CvD,EAAO,IAAiBwD,EACxBD,EAAUlH,OAAS,EAEZsH,EAmFR,SAASE,EAAe5F,GACvB,OAAa,MAATA,GAAiC,kBAATA,OAGX,iBAATA,GACS,iBAATA,GACS,iBAATA,EAEAyF,EAAE,KAAM,KAAMzF,GAEfA,EAQR,SAAS6F,EAAkBxC,EAAMlC,GAChC,MAAa,cAATkC,EACI,QACY,YAATA,EACH,MACY,iBAATA,EACH,QACY,mBAATA,EACH,UACY,oBAATA,EACH,WACGlC,GAAaxD,EAAMI,KAAKsF,GAC3BA,EAAKlE,cAAcT,QAAQ,WAAY,UAGxC2E,EAQR,SAASyC,EAAmBzC,EAAMW,GACjC,MAAa,UAATX,GAAyB,MAALW,GAA0B,iBAANA,EACpChF,EAAcgF,GACC,MAAZX,EAAK,IAA0B,MAAZA,EAAK,IAA2B,kBAANW,EAGhDvF,OAAOuF,GAGRA,EAGR,MAAMvE,EAAUD,MAAMC,QAChBmD,EAASD,OAAOC,OAWtB,SAAS+C,EAAgB3F,EAAOC,EAASkB,EAAWC,EAAaoE,GAEhE,GAAa,MAATxF,IAA2B,IAAVA,IAA4B,IAAVA,GAA6B,KAAVA,EACzD,MAAO,GAIR,GAAqB,iBAAVA,EACV,MAAqB,mBAAVA,EAA6B,GACjCnC,EAAemC,GAIvB,GAAIP,EAAQO,GAAQ,CACnB,IAAIuB,EAAW,GACfiE,EAAM,IAAaxF,EACnB,IAAK,IAAI/B,EAAI,EAAGA,EAAI+B,EAAM5B,OAAQH,IACjCsD,GAECoE,EAAgB3F,EAAM/B,GAAIgC,EAASkB,EAAWC,EAAaoE,GAE5DxF,EAAM/B,GAAK2H,EAAe5F,EAAM/B,IAEjC,OAAOsD,EAIR,QAA0BnC,IAAtBY,EAAMwB,YAA2B,MAAO,GAE5CxB,EAAK,GAAWwF,EACZzD,EAAO,KAAQA,EAAO,IAAO/B,GAEjC,IAAIyB,EAAOzB,EAAMyB,KAChBtB,EAAQH,EAAMG,MAIf,GADoC,mBAATsB,EACV,CAChB,IAAIF,EACJ,GAAIE,IAASI,EACZN,EAAWpB,EAAMZ,aACX,CAELgC,EADGE,EAAKU,WAA8C,mBAA1BV,EAAKU,UAAUC,OArJ/C,SAA8BpC,EAAOC,GACpC,IAAIO,EAAWR,EAAMyB,KACpBY,EAAO9B,EAAWC,EAAUP,GAGzB5C,EAAI,IAAImD,EAASR,EAAMG,MAAOkC,GAClCrC,EAAK,IAAc3C,EACnBA,EAAC,IAAU2C,EAEX3C,EAAC,KAAU,EACXA,EAAE8C,MAAQH,EAAMG,MACD,MAAX9C,EAAEkF,QAAelF,EAAEkF,MAAQ,IAEV,MAAjBlF,EAAC,MACJA,EAAC,IAAeA,EAAEkF,OAGnBlF,EAAE4C,QAAUoC,EACR7B,EAASkC,yBACZrF,EAAEkF,MAAQK,EACT,GACAvF,EAAEkF,MACF/B,EAASkC,yBAAyBrF,EAAE8C,MAAO9C,EAAEkF,QAEpClF,EAAEwF,qBACZxF,EAAEwF,qBAIFxF,EAAEkF,MAAQlF,EAAC,MAAiBA,EAAEkF,MAAQlF,EAAC,IAAeA,EAAEkF,OAGzD,IAAIN,EAAaF,EAAO,IAGxB,OAFIE,GAAYA,EAAWjC,GAEpB3C,EAAE+E,OAAO/E,EAAE8C,MAAO9C,EAAEkF,MAAOlF,EAAE4C,SAmHtB8F,CAAqB/F,EAAOC,GAvL3C,SAAiCD,EAAOC,GAGvC,IAAIsB,EACHlE,EAAI0C,EAAgBC,EAAOC,GAC3BoC,EAAO9B,EAAWP,EAAMyB,KAAMxB,GAE/BD,EAAK,IAAc3C,EAOnB,IAAI4E,EAAaF,EAAO,IACpBe,EAAQ,EACZ,KAAOzF,EAAC,KAAWyF,IAAU,IAC5BzF,EAAC,KAAU,EAEP4E,GAAYA,EAAWjC,GAG3BuB,EAAWvB,EAAMyB,KAAKsB,KAAK1F,EAAG2C,EAAMG,MAAOkC,GAG5C,OAAOd,EAgKOyE,CAAwBhG,EAAOC,GAG3C,IAAIiD,EAAYlD,EAAK,IACjBkD,EAAUF,kBACb/C,EAAU2C,EAAO,GAAI3C,EAASiD,EAAUF,oBAQ1CzB,EADa,MAAZA,GAAoBA,EAASE,OAASI,GAA4B,MAAhBN,EAAS0E,IAC5B1E,EAASpB,MAAMZ,SAAWgC,EAG1D,MAAMzD,EAAM6H,EACXpE,EACAtB,EACAkB,EACAC,EACApB,GAQD,OALI+B,EAAO,QAAUA,EAAO,OAAS/B,GACrCA,EAAK,QAAWZ,EAEZ2C,EAAQmE,SAASnE,EAAQmE,QAAQlG,GAE9BlC,EAIR,IACCyB,EACAoE,EAFGrG,EAAI,IAMR,GAFAA,GAAQmE,EAEJtB,EAAO,CACVZ,EAAWY,EAAMZ,SACjB,IAAK,IAAI8D,KAAQlD,EAAO,CACvB,IAAI6D,EAAI7D,EAAMkD,GAEd,KACU,QAATA,GACS,QAATA,GACS,WAATA,GACS,aAATA,GACS,aAATA,GACU,cAATA,GAAwB,UAAWlD,GAC1B,YAATkD,GAAsB,QAASlD,GAK7BzC,EAAYK,KAAKsF,IAKrB,GAHAA,EAAOwC,EAAkBxC,EAAMlC,GAC/B6C,EAAI8B,EAAmBzC,EAAMW,GAEhB,4BAATX,EACHM,EAAOK,GAAKA,EAAEK,eACK,aAAT5C,GAAgC,UAAT4B,EAEjC9D,EAAWyE,WACAA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,IAAU,IAANA,GAAoB,KAANA,EAAU,CAC3BA,EAAIX,EACJ/F,EAAIA,EAAI,IAAM+F,EACd,SAGD,GAAa,UAATA,EAAkB,CACrB,GAAa,WAAT5B,EAAmB,CACtBL,EAAc4C,EACd,SAGS,WAATvC,GACAL,GAAe4C,GAEb,aAAc7D,IAEhB7C,GAAQ,aAGVA,EAAIA,EAAI,IAAM+F,EAAO,KAAOxF,EAAemG,GAAK,MAKnD,IAAImC,EAAe7I,EAGnB,GAFAA,GAAQ,IAEJI,EAAYK,KAAK0D,GACpB,UAAUgD,MAAO,GAAEhD,qCAAwCnE,KAG5D,IAAIsH,EAAS,GACTwB,GAAc,EAElB,GAAIzC,EACHiB,GAAkBjB,EAClByC,GAAc,UACgB,iBAAb7G,EACjBqF,GAAkB/G,EAAe0B,GACjC6G,GAAc,UACJ3G,EAAQF,GAAW,CAC7BS,EAAK,IAAaT,EAClB,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAASnB,OAAQH,IAAK,CACzC,IAAI8G,EAAQxF,EAAStB,GAGrB,GAFAsB,EAAStB,GAAK2H,EAAeb,GAEhB,MAATA,IAA2B,IAAVA,EAAiB,CACrC,IAEIC,EAAMW,EACTZ,EACA9E,EAHS,QAATwB,GAA4B,kBAATA,GAA4BN,EAK/CC,EACApB,GAIGgF,IACHJ,GAAkBI,EAClBoB,GAAc,aAIK,MAAZ7G,IAAiC,IAAbA,IAAmC,IAAbA,EAAmB,CACvES,EAAK,IAAa,CAAC4F,EAAerG,IAClC,IAEIyF,EAAMW,EACTpG,EACAU,EAHS,QAATwB,GAA4B,kBAATA,GAA4BN,EAK/CC,EACApB,GAIGgF,IACHJ,GAAkBI,EAClBoB,GAAc,GAQhB,GAJIrE,EAAO,QAAUA,EAAO,OAAS/B,GACrCA,EAAK,QAAWZ,EACZ2C,EAAQmE,SAASnE,EAAQmE,QAAQlG,GAEjCoG,EACH9I,GAAQsH,UACEnH,EAAcM,KAAK0D,GAC7B,OAAO0E,EAAe,MAGvB,OAAO7I,EAAI,KAAOmE,EAAO,IAK1B4D,EAAegB,cA9YK,CAACrG,EAAOC,IAAYoF,EAAerF,EAAOC,EAASmF,GC/CvE,MAAMkB,EAAqB,cCErBhD,EAAWX,OAAOR,UAAUmB,SAC5BiD,EAAcC,KAAKrE,UAAUoE,YAC7BE,EAAgBhC,MAAMtC,UAAUmB,SAChCoD,EAAiBC,OAAOxE,UAAUmB,SAClCsD,EAAiBxJ,OAAO+E,UAAUmB,SAElCuD,EAAgB,uBAChBC,EAAiB,OAEjBC,EAAapE,OAAOqE,wBAA0BC,GAAO,IAE3D,SAASC,EAAsBC,GAC7B,MACiB,mBAAfA,GACe,yBAAfA,GACe,sBAAfA,GACe,0BAAfA,GACe,0BAAfA,GACe,uBAAfA,GACe,wBAAfA,GACe,wBAAfA,GACe,wBAAfA,GACe,+BAAfA,GACe,yBAAfA,GACe,yBAAfA,EAUJ,SAASC,EAAclI,GACrB,MAAiB,KAAbA,EAAImE,KACC,uBAEA,aAAenE,EAAImE,KAAO,IAIrC,SAASgE,EAAYnI,GACnB,OAAO0H,EAAe7D,KAAK7D,GAAKR,QAAQmI,EAAe,cAGzD,SAASS,EAAWpI,GAClB,MAAO,IAAMuH,EAAc1D,KAAK7D,GAAO,IAGzC,SAASqI,EAAgBrI,GACvB,IAAY,IAARA,IAAwB,IAARA,EAAe,MAAO,GAAKA,EAC/C,QAAYE,IAARF,EAAmB,MAAO,YAC9B,GAAY,OAARA,EAAc,MAAO,OAEzB,MAAMsI,SAAgBtI,EAEtB,GAAe,WAAXsI,EAAqB,OA7B3B,SAAqBtI,GACnB,OAAIA,IAAQA,EAAY,MACO,IAARA,GAAc,EAAIA,EAAO,EACxB,KAAO,GAAKA,EA0BJuI,CAAYvI,GAC5C,GAAe,WAAXsI,EAAqB,MAAO,ID1DjB,SAAqBtI,GACpC,OAAOA,EAAIR,QAAQ4H,EAAoB,QCyDDoB,CAAYxI,GAAO,IACzD,GAAe,aAAXsI,EAAuB,OAAOJ,EAAclI,GAChD,GAAe,WAAXsI,EAAqB,OAAOH,EAAYnI,GAE5C,MAAMiI,EAAa7D,EAASP,KAAK7D,GAEjC,MAAmB,qBAAfiI,EAA0C,aAC3B,qBAAfA,EAA0C,aAC3B,sBAAfA,GAAqD,+BAAfA,EAAoDC,EAAclI,EAAKyI,KAC9F,oBAAfR,EAAyCE,EAAYnI,GACtC,kBAAfiI,EAAuCZ,EAAYxD,KAAK7D,GACzC,mBAAfiI,EAAwCG,EAAWpI,GACpC,oBAAfiI,EAAyCT,EAAe3D,KAAK7D,GAC9C,uBAAfiI,GAAsD,IAAfjI,EAAId,OAAqB,eAChE8I,EAAsBC,IAA8B,IAAfjI,EAAId,OAAqBc,EAAIsC,YAAY6B,KAAO,MAErFnE,aAAeuF,OAAc6C,EAAWpI,GAK9C,SAAS0I,EAAUC,EAAMtJ,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACxG,IAAIU,EAAO,GAEX,GAAIR,EAAKzJ,OAAQ,CACfiK,GAAQL,EAER,MAAMM,EAAcR,EAAavJ,EAEjC,IAAK,IAAIN,EAAI,EAAGA,EAAI4J,EAAKzJ,OAAQH,IAC/BoK,GAAQC,EAAcC,EAAMV,EAAK5J,GAAIM,EAAQ+J,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAEnH1J,EAAI4J,EAAKzJ,OAAS,IACpBiK,GAAQ,IAAMN,GAIlBM,GAAQL,EAAcF,EAGxB,MAAO,IAAMO,EAAO,IAqGtB,SAASG,EAAkBtJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAE/G,IADAM,EAAOA,EAAK3J,SACHO,QAAQK,IAAQ,EACvB,MAAO,aAEP+I,EAAKtI,KAAKT,GAKZ,MAAMuJ,IAFNN,EAEmCD,EAEnC,IAAKO,GAAevJ,EAAIwJ,QAAgC,mBAAfxJ,EAAIwJ,OAC3C,OAAOH,EAAMrJ,EAAIwJ,SAAUnK,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAG9G,MAAMR,EAAa7D,EAASP,KAAK7D,GACjC,MAAmB,uBAAfiI,EACKsB,EAAc,cApHzB,SAAwBvJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAC5G,OAAQA,EAAM,GAAK,cAAgBC,EAAU1I,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAmH5FgB,CAAezJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACjIT,EAAsBC,GACxBsB,EAAc,UAlHzB,SAAoBvJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACxG,OAAQA,EAAM,GAAKzI,EAAIsC,YAAY6B,KAAO,KAAOuE,EAAU1I,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAiH9GiB,CAAW1J,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAC1G,iBAAfR,EACFsB,EAAc,QAhHzB,SAAkBvJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,IAAIkB,EAAS,QACb,MAAMC,EAAW5J,EAAI6J,UACrB,IAAIC,EAAUF,EAASG,OAEvB,IAAKD,EAAQE,KAAM,CACjBL,GAAUb,EAEV,MAAMM,EAAcR,EAAavJ,EAEjC,MAAQyK,EAAQE,MAIdL,GAAUP,EAHEC,EAAMS,EAAQnI,MAAM,GAAItC,EAAQ+J,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAGxF,OAFhBY,EAAMS,EAAQnI,MAAM,GAAItC,EAAQ+J,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAIxHqB,EAAUF,EAASG,OAEdD,EAAQE,OACXL,GAAU,IAAMd,GAIpBc,GAAUb,EAAcF,EAG1B,OAAOe,EAAS,IAsFiBM,CAASjK,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,iBAAfR,EACFsB,EAAc,QAlDzB,SAAkBvJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,IAAIkB,EAAS,QACb,MAAMC,EAAW5J,EAAI6J,UACrB,IAAIC,EAAUF,EAASG,OAEvB,IAAKD,EAAQE,KAAM,CACjBL,GAAUb,EAEV,MAAMM,EAAcR,EAAavJ,EAEjC,MAAQyK,EAAQE,MACdL,GAAUP,EAAcC,EAAMS,EAAQnI,MAAM,GAAItC,EAAQ+J,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAElIqB,EAAUF,EAASG,OAEdD,EAAQE,OACXL,GAAU,IAAMd,GAIpBc,GAAUb,EAAcF,EAG1B,OAAOe,EAAS,IA2BiBO,CAASlK,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,iBAARzI,EACTuJ,EAAc,WAvFzB,SAAqBvJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAEzG,IAAIkB,GADgBlB,EAAM,GAAMzI,EAAIsC,YAAetC,EAAIsC,YAAY6B,KAAO,IAAM,WACrD,IACvBQ,EAAOlB,OAAOkB,KAAK3E,GAAK6E,OAC5B,MAAMsF,EAAUtC,EAAW7H,GAQ3B,GANImK,EAAQjL,SACVyF,EAAOA,EACJyF,OAAOrD,KAAwB,iBAARA,GAA2C,oBAAvB3C,EAASP,KAAKkD,KACzDsD,OAAOF,IAGRxF,EAAKzF,OAAQ,CACfyK,GAAUb,EAEV,MAAMM,EAAcR,EAAavJ,EAEjC,IAAK,IAAIN,EAAI,EAAGA,EAAI4F,EAAKzF,OAAQH,IAAK,CACpC,MAAMgI,EAAMpC,EAAK5F,GAIjB4K,GAAUP,EAHGC,EAAMtC,EAAK1H,EAAQ+J,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAG3E,KAFjBY,EAAMrJ,EAAI+G,GAAM1H,EAAQ+J,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAI5G1J,EAAI4F,EAAKzF,OAAS,IACpByK,GAAU,IAAMd,GAIpBc,GAAUb,EAAcF,EAG1B,OAAOe,EAAS,IAuDoBW,CAAYtK,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,QAD/H,EAKT,SAAS8B,EAAYvK,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACzG,IACI+B,EADAnG,GAAQ,EAGZ,IAAK,IAAIoG,EAAI,EAAGA,EAAIvB,EAAQhK,OAAQuL,IAGlC,GAFAD,EAAStB,EAAQuB,GAEbD,EAAO3L,KAAKmB,GAAM,CACpBqE,GAAQ,EACR,MAIJ,QAAKA,GAaEmG,EAAOnB,MAAMrJ,EATpB,SAAoBA,GAClB,OAAOqJ,EAAMrJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,IAGrG,SAAqB7J,GACnB,MAAM8L,EAAc9B,EAAavJ,EACjC,OAAOqL,EAAc9L,EAAIY,QAAQoI,EAAgB,KAAO8C,IAGR,CAChD5B,YAAaA,EACbD,QAASA,IAIb,SAASQ,EAAMrJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAEnG,OADcJ,EAAgBrI,IAGfuK,EAAYvK,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,IAG1Ga,EAAkBtJ,EAAKX,EAAQuJ,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAGjH,MAAMkC,EAAW,CACftL,OAAQ,EACRoJ,KAAK,EACLO,SAAU4B,SACV1B,QAAS,IA6BX,SAAS2B,EAAaxL,GACpB,OAAO,IAAIiB,MAAMjB,EAAS,GAAG2G,KAAK,KC/SpC,IAAI8E,EAAe,CAClBjM,KAAKkM,GAEHA,GACkB,iBAAXA,GACP,SAAUA,GACV,UAAWA,GACX,QAASA,EAGX1B,MAAK,CAACrJ,EAAKqJ,EAAOhK,IACV8G,EAAenG,EAAK8K,EAAa/J,QAAS+J,EAAa/I,OAI5DiJ,EAAmB,CACtB9B,QAAS,CAAC4B,IAqCPG,EAAc,CACjB/F,cAnCD,SAAuBf,EAAMxC,EAAOZ,EAASgB,EAAMS,GAClD,IAAID,SAAcZ,EAGlB,GAAa,4BAATwC,EAAoC,SAGxC,GAAa,MAATxC,GAA2B,aAATY,IAAwBR,EAAKmJ,UAAY,MAAO,GAEtE,GACCnJ,EAAKoJ,sBACJ3I,KACU,IAAVb,IACW,UAATwC,GAA6B,UAATA,IAA+B,KAAVxC,GAE5C,MAAO,GAER,IAAIS,EAAoC,iBAAhBL,EAAKI,OAAsBJ,EAAKI,OAAS,KACjE,MAAa,WAATI,GACU,aAATA,GAAwBR,EAAKqJ,eAGhCN,EAAa/J,QAAUA,EACvB+J,EAAa/I,KAAOA,IACpBJ,EDuQH,SAAsB3B,EAAK+B,GAQzB,IAAI1C,EACA0J,EARChH,GA/BP,SAAyBA,GAOvB,GANA0B,OAAOkB,KAAK5C,GAAMsJ,QAAQtE,IACxB,IAAK4D,EAASW,eAAevE,GAC3B,MAAM,IAAIxB,MAAM,iCAAmCwB,KAInDhF,EAAK0G,UAAuBvI,IAAhB6B,EAAK1C,QAAwC,IAAhB0C,EAAK1C,OAChD,MAAM,IAAIkG,MAAM,uDA0BhBgG,CAAgBxJ,GAChBA,EAvBJ,SAA0BA,GACxB,MAAM4H,EAAS,GAUf,OARAlG,OAAOkB,KAAKgG,GAAUU,QAAQtE,GAC5B4C,EAAO5C,GAAOhF,EAAKuJ,eAAevE,GAAOhF,EAAKgF,GAAO4D,EAAS5D,IAG5D4C,EAAOlB,MACTkB,EAAOtK,OAAS,GAGXsK,EAYE6B,CAAiBzJ,IAHxBA,EAAO4I,EAQT,MAEM9B,EAAU9G,EAAK0G,IAAM,IAAM,KAC3BK,EAAc/G,EAAK0G,IAAM,GAAK,KAEpC,GAAI1G,GAAQA,EAAKmH,QAAQhK,OAAQ,CAC/BG,EAASwL,EAAa9I,EAAK1C,QAC3B0J,EAAO,GACP,IAAI0C,EAAgBlB,EAAYvK,EAAKX,EARpB,GAQwCwJ,EAASC,EAAaC,EAAMhH,EAAKiH,SAPvE,EAO+FjH,EAAKmH,QAASnH,EAAK0G,KACrI,GAAIgD,EAAe,OAAOA,EAI5B,OADkBpD,EAAgBrI,KAG7BX,IAAQA,EAASwL,EAAa9I,EAAK1C,SACnC0J,IAAMA,EAAO,IACXO,EAAkBtJ,EAAKX,EAjBX,GAiB+BwJ,EAASC,EAAaC,EAAMhH,EAAKiH,SAhB9D,EAgBsFjH,EAAKmH,QAASnH,EAAK0G,MClSrHiD,CAAa/J,EAAOqJ,IACjBrL,QAAQ,QAClBgC,EAAS,GAAEtC,EAAO,KAAOsC,EAAOS,SANjCT,EAAQ,WASFtC,EAAQ,KAAI8E,MAASxC,KAAUS,IAE/B,KAAIA,IAAa+B,MAASxF,EAAegD,OAKjDgK,KAAK,EACLvG,KAAK,EACL8F,WAAW,EACXE,eAAe,EACfD,qBAAqB,EACrBhJ,OAAQ,MAGT,SAASyJ,EAAkB9K,EAAOC,EAASgB,EAAMC,GAEhD,OAAOmE,EAAerF,EAAOC,EAD7BgB,EAAO0B,OAAOC,OAAO,GAAIuH,EAAalJ,GAAQ"}