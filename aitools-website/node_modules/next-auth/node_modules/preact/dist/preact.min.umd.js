!function(n){"function"==typeof define&&define.amd?define(n):n()}(function(){var n,t,e,i,r,o,f,u,c,s,a,h,l,p,y="http://www.w3.org/2000/svg",v="http://www.w3.org/1999/xhtml",d=null,w=void 0,_={},m=[],g=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,b=Array.isArray;function k(n,t){for(var e in t)n[e]=t[e];return n}function C(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function x(t,e,i){var r,o,f,u={};for(f in e)"key"==f?r=e[f]:"ref"==f?o=e[f]:u[f]=e[f];if(arguments.length>2&&(u.children=arguments.length>3?n.call(arguments,2):i),"function"==typeof t&&t.defaultProps!=d)for(f in t.defaultProps)u[f]===w&&(u[f]=t.defaultProps[f]);return S(t,u,r,o,d)}function S(n,i,r,o,f){var u={type:n,props:i,key:r,ref:o,__k:d,__:d,__b:0,__e:d,__c:d,constructor:w,__v:f==d?++e:f,__i:-1,__u:0};return f==d&&t.vnode!=d&&t.vnode(u),u}function M(n){return n.children}function $(n,t){this.props=n,this.context=t}function A(n,t){if(t==d)return n.__?A(n.__,n.__i+1):d;for(var e;t<n.__k.length;t++)if((e=n.__k[t])!=d&&e.__e!=d)return e.__e;return"function"==typeof n.type?A(n):d}function I(n){var t,e;if((n=n.__)!=d&&n.__c!=d){for(n.__e=n.__c.base=d,t=0;t<n.__k.length;t++)if((e=n.__k[t])!=d&&e.__e!=d){n.__e=n.__c.base=e.__e;break}return I(n)}}function P(n){(!n.__d&&(n.__d=!0)&&r.push(n)&&!E.__r++||o!=t.debounceRendering)&&((o=t.debounceRendering)||f)(E)}function E(){for(var n,e,i,o,f,c,s,a=1;r.length;)r.length>a&&r.sort(u),n=r.shift(),a=r.length,n.__d&&(i=void 0,f=(o=(e=n).__v).__e,c=[],s=[],e.__P&&((i=k({},o)).__v=o.__v+1,t.vnode&&t.vnode(i),z(e.__P,i,o,e.__n,e.__P.namespaceURI,32&o.__u?[f]:d,c,f==d?A(o):f,!!(32&o.__u),s),i.__v=o.__v,i.__.__k[i.__i]=i,N(c,i,s),i.__e!=f&&I(i)));E.__r=0}function F(n,t,e,i,r,o,f,u,c,s,a){var h,l,p,y,v,g,b=i&&i.__k||m,k=t.length;for(c=H(e,t,b,c,k),h=0;h<k;h++)(p=e.__k[h])!=d&&(l=-1==p.__i?_:b[p.__i]||_,p.__i=h,g=z(n,p,l,r,o,f,u,c,s,a),y=p.__e,p.ref&&l.ref!=p.ref&&(l.ref&&B(l.ref,d,p),a.push(p.ref,p.__c||y,p)),v==d&&y!=d&&(v=y),4&p.__u||l.__k===p.__k?c=L(p,c,n):"function"==typeof p.type&&g!==w?c=g:y&&(c=y.nextSibling),p.__u&=-7);return e.__e=v,c}function H(n,t,e,i,r){var o,f,u,c,s,a=e.length,h=a,l=0;for(n.__k=new Array(r),o=0;o<r;o++)(f=t[o])!=d&&"boolean"!=typeof f&&"function"!=typeof f?(c=o+l,(f=n.__k[o]="string"==typeof f||"number"==typeof f||"bigint"==typeof f||f.constructor==String?S(d,f,d,d,d):b(f)?S(M,{children:f},d,d,d):f.constructor==w&&f.__b>0?S(f.type,f.props,f.key,f.ref?f.ref:d,f.__v):f).__=n,f.__b=n.__b+1,s=f.__i=T(f,e,c,h),u=d,-1!=s&&(h--,(u=e[s])&&(u.__u|=2)),u==d||u.__v==d?(-1==s&&(r>a?l--:r<a&&l++),"function"!=typeof f.type&&(f.__u|=4)):s!=c&&(s==c-1?l--:s==c+1?l++:(s>c?l--:l++,f.__u|=4))):n.__k[o]=d;if(h)for(o=0;o<a;o++)(u=e[o])!=d&&0==(2&u.__u)&&(u.__e==i&&(i=A(u)),D(u,u));return i}function L(n,t,e){var i,r;if("function"==typeof n.type){for(i=n.__k,r=0;i&&r<i.length;r++)i[r]&&(i[r].__=n,t=L(i[r],t,e));return t}n.__e!=t&&(t&&n.type&&!e.contains(t)&&(t=A(n)),e.insertBefore(n.__e,t||d),t=n.__e);do{t=t&&t.nextSibling}while(t!=d&&8==t.nodeType);return t}function T(n,t,e,i){var r,o,f=n.key,u=n.type,c=t[e];if(c===d&&null==n.key||c&&f==c.key&&u==c.type&&0==(2&c.__u))return e;if(i>(c!=d&&0==(2&c.__u)?1:0))for(r=e-1,o=e+1;r>=0||o<t.length;){if(r>=0){if((c=t[r])&&0==(2&c.__u)&&f==c.key&&u==c.type)return r;r--}if(o<t.length){if((c=t[o])&&0==(2&c.__u)&&f==c.key&&u==c.type)return o;o++}}return-1}function j(n,t,e){"-"==t[0]?n.setProperty(t,e==d?"":e):n[t]=e==d?"":"number"!=typeof e||g.test(t)?e:e+"px"}function O(n,t,e,i,r){var o,f;n:if("style"==t)if("string"==typeof e)n.style.cssText=e;else{if("string"==typeof i&&(n.style.cssText=i=""),i)for(t in i)e&&t in e||j(n.style,t,"");if(e)for(t in e)i&&e[t]==i[t]||j(n.style,t,e[t])}else if("o"==t[0]&&"n"==t[1])o=t!=(t=t.replace(c,"$1")),f=t.toLowerCase(),t=f in n||"onFocusOut"==t||"onFocusIn"==t?f.slice(2):t.slice(2),n.l||(n.l={}),n.l[t+o]=e,e?i?e.t=i.t:(e.t=s,n.addEventListener(t,o?h:a,o)):n.removeEventListener(t,o?h:a,o);else{if(r==y)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in n)try{n[t]=e==d?"":e;break n}catch(n){}"function"==typeof e||(e==d||!1===e&&"-"!=t[4]?n.removeAttribute(t):n.setAttribute(t,"popover"==t&&1==e?"":e))}}function V(n){return function(e){if(this.l){var i=this.l[e.type+n];if(e.i==d)e.i=s++;else if(e.i<i.t)return;return i(t.event?t.event(e):e)}}}function z(n,e,i,r,o,f,u,c,s,a){var h,l,p,y,v,_,m,g,x,S,A,I,P,E,H,L,T,j=e.type;if(e.constructor!=w)return d;128&i.__u&&(s=!!(32&i.__u),f=[c=e.__e=i.__e]),(h=t.__b)&&h(e);n:if("function"==typeof j)try{if(g=e.props,x="prototype"in j&&j.prototype.render,S=(h=j.contextType)&&r[h.__c],A=h?S?S.props.value:h.__:r,i.__c?m=(l=e.__c=i.__c).__=l.__E:(x?e.__c=l=new j(g,A):(e.__c=l=new $(g,A),l.constructor=j,l.render=G),S&&S.sub(l),l.props=g,l.state||(l.state={}),l.context=A,l.__n=r,p=l.__d=!0,l.__h=[],l._sb=[]),x&&l.__s==d&&(l.__s=l.state),x&&j.getDerivedStateFromProps!=d&&(l.__s==l.state&&(l.__s=k({},l.__s)),k(l.__s,j.getDerivedStateFromProps(g,l.__s))),y=l.props,v=l.state,l.__v=e,p)x&&j.getDerivedStateFromProps==d&&l.componentWillMount!=d&&l.componentWillMount(),x&&l.componentDidMount!=d&&l.__h.push(l.componentDidMount);else{if(x&&j.getDerivedStateFromProps==d&&g!==y&&l.componentWillReceiveProps!=d&&l.componentWillReceiveProps(g,A),!l.__e&&l.shouldComponentUpdate!=d&&!1===l.shouldComponentUpdate(g,l.__s,A)||e.__v==i.__v){for(e.__v!=i.__v&&(l.props=g,l.state=l.__s,l.__d=!1),e.__e=i.__e,e.__k=i.__k,e.__k.some(function(n){n&&(n.__=e)}),I=0;I<l._sb.length;I++)l.__h.push(l._sb[I]);l._sb=[],l.__h.length&&u.push(l);break n}l.componentWillUpdate!=d&&l.componentWillUpdate(g,l.__s,A),x&&l.componentDidUpdate!=d&&l.__h.push(function(){l.componentDidUpdate(y,v,_)})}if(l.context=A,l.props=g,l.__P=n,l.__e=!1,P=t.__r,E=0,x){for(l.state=l.__s,l.__d=!1,P&&P(e),h=l.render(l.props,l.state,l.context),H=0;H<l._sb.length;H++)l.__h.push(l._sb[H]);l._sb=[]}else do{l.__d=!1,P&&P(e),h=l.render(l.props,l.state,l.context),l.state=l.__s}while(l.__d&&++E<25);l.state=l.__s,l.getChildContext!=d&&(r=k(k({},r),l.getChildContext())),x&&!p&&l.getSnapshotBeforeUpdate!=d&&(_=l.getSnapshotBeforeUpdate(y,v)),L=h,h!=d&&h.type===M&&h.key==d&&(L=R(h.props.children)),c=F(n,b(L)?L:[L],e,i,r,o,f,u,c,s,a),l.base=e.__e,e.__u&=-161,l.__h.length&&u.push(l),m&&(l.__E=l.__=d)}catch(n){if(e.__v=d,s||f!=d)if(n.then){for(e.__u|=s?160:128;c&&8==c.nodeType&&c.nextSibling;)c=c.nextSibling;f[f.indexOf(c)]=d,e.__e=c}else for(T=f.length;T--;)C(f[T]);else e.__e=i.__e,e.__k=i.__k;t.__e(n,e,i)}else f==d&&e.__v==i.__v?(e.__k=i.__k,e.__e=i.__e):c=e.__e=q(i.__e,e,i,r,o,f,u,s,a);return(h=t.diffed)&&h(e),128&e.__u?void 0:c}function N(n,e,i){for(var r=0;r<i.length;r++)B(i[r],i[++r],i[++r]);t.__c&&t.__c(e,n),n.some(function(e){try{n=e.__h,e.__h=[],n.some(function(n){n.call(e)})}catch(n){t.__e(n,e.__v)}})}function R(n){return"object"!=typeof n||n==d||n.__b&&n.__b>0?n:b(n)?n.map(R):k({},n)}function q(e,i,r,o,f,u,c,s,a){var h,l,p,m,g,k,x,S=r.props,M=i.props,$=i.type;if("svg"==$?f=y:"math"==$?f="http://www.w3.org/1998/Math/MathML":f||(f=v),u!=d)for(h=0;h<u.length;h++)if((g=u[h])&&"setAttribute"in g==!!$&&($?g.localName==$:3==g.nodeType)){e=g,u[h]=d;break}if(e==d){if($==d)return document.createTextNode(M);e=document.createElementNS(f,$,M.is&&M),s&&(t.__m&&t.__m(i,u),s=!1),u=d}if($==d)S===M||s&&e.data==M||(e.data=M);else{if(u=u&&n.call(e.childNodes),S=r.props||_,!s&&u!=d)for(S={},h=0;h<e.attributes.length;h++)S[(g=e.attributes[h]).name]=g.value;for(h in S)if(g=S[h],"children"==h);else if("dangerouslySetInnerHTML"==h)p=g;else if(!(h in M)){if("value"==h&&"defaultValue"in M||"checked"==h&&"defaultChecked"in M)continue;O(e,h,d,g,f)}for(h in M)g=M[h],"children"==h?m=g:"dangerouslySetInnerHTML"==h?l=g:"value"==h?k=g:"checked"==h?x=g:s&&"function"!=typeof g||S[h]===g||O(e,h,g,S[h],f);if(l)s||p&&(l.__html==p.__html||l.__html==e.innerHTML)||(e.innerHTML=l.__html),i.__k=[];else if(p&&(e.innerHTML=""),F("template"==i.type?e.content:e,b(m)?m:[m],i,r,o,"foreignObject"==$?v:f,u,c,u?u[0]:r.__k&&A(r,0),s,a),u!=d)for(h=u.length;h--;)C(u[h]);s||(h="value","progress"==$&&k==d?e.removeAttribute("value"):k!=w&&(k!==e[h]||"progress"==$&&!k||"option"==$&&k!=S[h])&&O(e,h,k,S[h],f),h="checked",x!=w&&x!=e[h]&&O(e,h,x,S[h],f))}return e}function B(n,e,i){try{if("function"==typeof n){var r="function"==typeof n.__u;r&&n.__u(),r&&e==d||(n.__u=n(e))}else n.current=e}catch(n){t.__e(n,i)}}function D(n,e,i){var r,o;if(t.unmount&&t.unmount(n),(r=n.ref)&&(r.current&&r.current!=n.__e||B(r,d,e)),(r=n.__c)!=d){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(n){t.__e(n,e)}r.base=r.__P=d}if(r=n.__k)for(o=0;o<r.length;o++)r[o]&&D(r[o],e,i||"function"!=typeof n.type);i||C(n.__e),n.__c=n.__=n.__e=w}function G(n,t,e){return this.constructor(n,e)}function J(e,i,r){var o,f,u,c;i==document&&(i=document.documentElement),t.__&&t.__(e,i),f=(o="function"==typeof r)?d:r&&r.__k||i.__k,u=[],c=[],z(i,e=(!o&&r||i).__k=x(M,d,[e]),f||_,_,i.namespaceURI,!o&&r?[r]:f?d:i.firstChild?n.call(i.childNodes):d,u,!o&&r?r:f?f.__e:i.firstChild,o,c),N(u,e,c)}n=m.slice,t={__e:function(n,t,e,i){for(var r,o,f;t=t.__;)if((r=t.__c)&&!r.__)try{if((o=r.constructor)&&o.getDerivedStateFromError!=d&&(r.setState(o.getDerivedStateFromError(n)),f=r.__d),r.componentDidCatch!=d&&(r.componentDidCatch(n,i||{}),f=r.__d),f)return r.__E=r}catch(t){n=t}throw n}},e=0,i=function(n){return n!=d&&n.constructor==w},$.prototype.setState=function(n,t){var e;e=this.__s!=d&&this.__s!=this.state?this.__s:this.__s=k({},this.state),"function"==typeof n&&(n=n(k({},e),this.props)),n&&k(e,n),n!=d&&this.__v&&(t&&this._sb.push(t),P(this))},$.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),P(this))},$.prototype.render=M,r=[],f="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,u=function(n,t){return n.__v.__b-t.__v.__b},E.__r=0,c=/(PointerCapture)$|Capture$/i,s=0,a=V(!1),h=V(!0),l=0,p={__proto__:null,render:J,hydrate:function n(t,e){J(t,e,n)},createElement:x,h:x,Fragment:M,createRef:function(){return{current:d}},isValidElement:i,Component:$,cloneElement:function(t,e,i){var r,o,f,u,c=k({},t.props);for(f in t.type&&t.type.defaultProps&&(u=t.type.defaultProps),e)"key"==f?r=e[f]:"ref"==f?o=e[f]:c[f]=e[f]===w&&u!=w?u[f]:e[f];return arguments.length>2&&(c.children=arguments.length>3?n.call(arguments,2):i),S(t.type,c,r||t.key,o||t.ref,d)},createContext:function(n){function t(n){var e,i;return this.getChildContext||(e=new Set,(i={})[t.__c]=this,this.getChildContext=function(){return i},this.componentWillUnmount=function(){e=d},this.shouldComponentUpdate=function(n){this.props.value!=n.value&&e.forEach(function(n){n.__e=!0,P(n)})},this.sub=function(n){e.add(n);var t=n.componentWillUnmount;n.componentWillUnmount=function(){e&&e.delete(n),t&&t.call(n)}}),n.children}return t.__c="__cC"+l++,t.__=n,t.Provider=t.__l=(t.Consumer=function(n,t){return n.children(t)}).contextType=t,t},toChildArray:function n(t,e){return e=e||[],t==d||"boolean"==typeof t||(b(t)?t.some(function(t){n(t,e)}):e.push(t)),e},options:t},typeof module<"u"?module.exports=p:self.preact=p});
//# sourceMappingURL=preact.min.umd.js.map
