{"version": 3, "file": "preact.umd.js", "sources": ["../src/util.js", "../src/options.js", "../src/create-element.js", "../src/component.js", "../src/diff/props.js", "../src/create-context.js", "../src/constants.js", "../src/diff/children.js", "../src/diff/index.js", "../src/render.js", "../src/diff/catch-error.js", "../src/clone-element.js"], "sourcesContent": ["import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {import('./index').ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\nimport { NULL, UNDEFINED } from './constants';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != NULL) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === UNDEFINED) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, NULL);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {import('./internal').VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: NULL,\n\t\t_parent: NULL,\n\t\t_depth: 0,\n\t\t_dom: NULL,\n\t\t_component: NULL,\n\t\tconstructor: UNDEFINED,\n\t\t_original: original == NULL ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == NULL && options.vnode != NULL) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: NULL };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != NULL && vnode.constructor == UNDEFINED;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE, NULL } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {import('./internal').Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != NULL && this._nextState != this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == NULL) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {import('./internal').Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](https://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == NULL) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: NULL;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != NULL && sibling._dom != NULL) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : NULL;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : NULL,\n\t\t\tcommitQueue,\n\t\t\toldDom == NULL ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != NULL && vnode._component != NULL) {\n\t\tvnode._dom = vnode._component.base = NULL;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != NULL && child._dom != NULL) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce != options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {import('./internal').Component} a\n * @param {import('./internal').Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c,\n\t\tl = 1;\n\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile (rerenderQueue.length) {\n\t\t// Keep the rerender queue sorted by (depth, insertion order). The queue\n\t\t// will initially be sorted on the first iteration only if it has more than 1 item.\n\t\t//\n\t\t// New items can be added to the queue e.g. when rerendering a provider, so we want to\n\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t// single pass\n\t\tif (rerenderQueue.length > l) {\n\t\t\trerenderQueue.sort(depthSort);\n\t\t}\n\n\t\tc = rerenderQueue.shift();\n\t\tl = rerenderQueue.length;\n\n\t\tif (c._dirty) {\n\t\t\trenderComponent(c);\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL, NULL, SVG_NAMESPACE } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] == '-') {\n\t\tstyle.setProperty(key, value == NULL ? '' : value);\n\t} else if (value == NULL) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\nconst CAPTURE_REGEX = /(PointerCapture)$|Capture$/i;\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name == 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] != oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] == 'o' && name[1] == 'n') {\n\t\tuseCapture = name != (name = name.replace(CAPTURE_REGEX, '$1'));\n\t\tconst lowerCaseName = name.toLowerCase();\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (lowerCaseName in dom || name == 'onFocusOut' || name == 'onFocusIn')\n\t\t\tname = lowerCaseName.slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == SVG_NAMESPACE) {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == NULL ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != NULL && (value !== false || name[4] == '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {import('../internal').PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == NULL) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\nimport { NULL } from './constants';\n\nexport let i = 0;\n\nexport function createContext(defaultValue) {\n\tfunction Context(props) {\n\t\tif (!this.getChildContext) {\n\t\t\t/** @type {Set<import('./internal').Component> | null} */\n\t\t\tlet subs = new Set();\n\t\t\tlet ctx = {};\n\t\t\tctx[Context._id] = this;\n\n\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\tsubs = NULL;\n\t\t\t};\n\n\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t// @ts-expect-error even\n\t\t\t\tif (this.props.value != _props.value) {\n\t\t\t\t\tsubs.forEach(c => {\n\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.sub = c => {\n\t\t\t\tsubs.add(c);\n\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\tif (subs) {\n\t\t\t\t\t\tsubs.delete(c);\n\t\t\t\t\t}\n\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t};\n\t\t\t};\n\t\t}\n\n\t\treturn props.children;\n\t}\n\n\tContext._id = '__cC' + i++;\n\tContext._defaultValue = defaultValue;\n\n\t/** @type {import('./internal').FunctionComponent} */\n\tContext.Consumer = (props, contextValue) => {\n\t\treturn props.children(contextValue);\n\t};\n\n\t// we could also get rid of _contextRef entirely\n\tContext.Provider =\n\t\tContext._contextRef =\n\t\tContext.Consumer.contextType =\n\t\t\tContext;\n\n\treturn Context;\n}\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 2;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 1;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\nexport const XHTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\nexport const MATH_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n\nexport const NULL = null;\nexport const UNDEFINED = undefined;\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport {\n\tEMPTY_OBJ,\n\tEMPTY_ARR,\n\tINSERT_VNODE,\n\tMATCHED,\n\tUNDEFINED,\n\tNULL\n} from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\toldDom = constructNewChildrenArray(\n\t\tnewParentVNode,\n\t\trenderResult,\n\t\toldChildren,\n\t\toldDom,\n\t\tnewChildrenLength\n\t);\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == NULL) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index == -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tlet result = diff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, NULL, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == NULL && newDom != NULL) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (typeof childVNode.type == 'function' && result !== UNDEFINED) {\n\t\t\toldDom = result;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(\n\tnewParentVNode,\n\trenderResult,\n\toldChildren,\n\toldDom,\n\tnewChildrenLength\n) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = new Array(newChildrenLength);\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == NULL ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tnewParentVNode._children[i] = NULL;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tNULL,\n\t\t\t\tchildVNode,\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (childVNode.constructor == UNDEFINED && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : NULL,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = NULL;\n\t\tif (matchingIndex != -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original == null\n\t\tconst isMounting = oldVNode == NULL || oldVNode._original == NULL;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\t// When the array of children is growing we need to decrease the skew\n\t\t\t\t// as we are adding a new element to the array.\n\t\t\t\t// Example:\n\t\t\t\t// [1, 2, 3] --> [0, 1, 2, 3]\n\t\t\t\t// oldChildren   newChildren\n\t\t\t\t//\n\t\t\t\t// The new element is at index 0, so our skew is 0,\n\t\t\t\t// we need to decrease the skew as we are adding a new element.\n\t\t\t\t// The decrease will cause us to compare the element at position 1\n\t\t\t\t// with value 1 with the element at position 0 with value 0.\n\t\t\t\t//\n\t\t\t\t// A linear concept is applied when the array is shrinking,\n\t\t\t\t// if the length is unchanged we can assume that no skew\n\t\t\t\t// changes are needed.\n\t\t\t\tif (newChildrenLength > oldChildrenLength) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else if (newChildrenLength < oldChildrenLength) {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex != skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != NULL && (oldVNode._flags & MATCHED) == 0) {\n\t\t\t\tif (oldVNode._dom == oldDom) {\n\t\t\t\t\toldDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || NULL);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != NULL && oldDom.nodeType == 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == NULL || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\t//\n\t// If there is an unkeyed functional VNode, that isn't a built-in like our Fragment,\n\t// we should not search as we risk re-using state of an unrelated VNode. (reverted for now)\n\tlet shouldSearch =\n\t\t// (typeof type != 'function' || type === Fragment || key) &&\n\t\tremainingOldChildren >\n\t\t(oldVNode != NULL && (oldVNode._flags & MATCHED) == 0 ? 1 : 0);\n\n\tif (\n\t\t(oldVNode === NULL && childVNode.key == null) ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype == oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) == 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\tlet x = skewedIndex - 1;\n\t\tlet y = skewedIndex + 1;\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMATH_NAMESPACE,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tNULL,\n\tRESET_MODE,\n\tSVG_NAMESPACE,\n\tUNDEFINED,\n\tXHTML_NAMESPACE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * @template {any} T\n * @typedef {import('../internal').Ref<T>} Ref<T>\n */\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor != UNDEFINED) return NULL;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == NULL) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != NULL) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tc.componentWillMount != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != NULL &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original == oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original != oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != NULL) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != NULL) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != NULL) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != NULL && tmp.type === Fragment && tmp.key == NULL;\n\t\t\tlet renderResult = tmp;\n\n\t\t\tif (isTopLevelFragment) {\n\t\t\t\trenderResult = cloneNode(tmp.props.children);\n\t\t\t}\n\n\t\t\toldDom = diffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = NULL;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = NULL;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != NULL) {\n\t\t\t\tif (e.then) {\n\t\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t\t: MODE_SUSPENDED;\n\n\t\t\t\t\twhile (oldDom && oldDom.nodeType == 8 && oldDom.nextSibling) {\n\t\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t\t}\n\n\t\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = NULL;\n\t\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t\t} else {\n\t\t\t\t\tfor (let i = excessDomChildren.length; i--; ) {\n\t\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == NULL &&\n\t\tnewVNode._original == oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\toldDom = newVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n\n\treturn newVNode._flags & MODE_SUSPENDED ? undefined : oldDom;\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\nfunction cloneNode(node) {\n\tif (\n\t\ttypeof node != 'object' ||\n\t\tnode == NULL ||\n\t\t(node._depth && node._depth > 0)\n\t) {\n\t\treturn node;\n\t}\n\n\tif (isArray(node)) {\n\t\treturn node.map(cloneNode);\n\t}\n\n\treturn assign({}, node);\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType == 'svg') namespace = SVG_NAMESPACE;\n\telse if (nodeType == 'math') namespace = MATH_NAMESPACE;\n\telse if (!namespace) namespace = XHTML_NAMESPACE;\n\n\tif (excessDomChildren != NULL) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value == !!nodeType &&\n\t\t\t\t(nodeType ? value.localName == nodeType : value.nodeType == 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = NULL;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == NULL) {\n\t\tif (nodeType == NULL) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = NULL;\n\t}\n\n\tif (nodeType == NULL) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data != newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != NULL) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, NULL, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html != oldHtml.__html && newHtml.__html != dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\t// @ts-expect-error\n\t\t\t\tnewVNode.type == 'template' ? dom.content : dom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType == 'foreignObject' ? XHTML_NAMESPACE : namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != NULL) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType == 'progress' && inputValue == NULL) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue != UNDEFINED &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType == 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType == 'option' && inputValue != oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked != UNDEFINED && checked != dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != NULL) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current == vnode._dom) {\n\t\t\tapplyRef(r, NULL, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != NULL) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = NULL;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\tvnode._component = vnode._parent = vnode._dom = UNDEFINED;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ, NULL } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to render into\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\t// https://github.com/preactjs/preact/issues/3794\n\tif (parentDom == document) {\n\t\tparentDom = document.documentElement;\n\t}\n\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? NULL\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, NULL, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? NULL\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: NULL,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { NULL } from '../constants';\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {import('../internal').VNode} [oldVNode]\n * @param {import('../internal').ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {import('../internal').Component} */\n\tlet component,\n\t\t/** @type {import('../internal').ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != NULL) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != NULL) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\nimport { NULL, UNDEFINED } from './constants';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] === UNDEFINED && defaultProps != UNDEFINED) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tNULL\n\t);\n}\n"], "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "prevDebounce", "defer", "depthSort", "CAPTURE_REGEX", "eventClock", "eventProxy", "eventProxyCapture", "i", "SVG_NAMESPACE", "XHTML_NAMESPACE", "NULL", "UNDEFINED", "undefined", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "isArray", "Array", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__c", "constructor", "__v", "__i", "__u", "Fragment", "BaseComponent", "context", "this", "getDomSibling", "childIndex", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "__d", "push", "process", "__r", "debounceRendering", "component", "newVNode", "oldVNode", "oldDom", "commitQueue", "refQueue", "l", "sort", "shift", "__P", "diff", "__n", "namespaceURI", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "parentDom", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "namespace", "excessDomChildren", "isHydrating", "childVNode", "newDom", "firstChildDom", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructNewChildrenArray", "applyRef", "insert", "nextS<PERSON>ling", "skewedIndex", "matchingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingOldChildren", "skew", "String", "findMatchingIndex", "unmount", "parentVNode", "contains", "insertBefore", "nodeType", "x", "y", "setStyle", "style", "value", "setProperty", "test", "dom", "name", "oldValue", "useCapture", "lowerCaseName", "o", "cssText", "replace", "toLowerCase", "_attached", "addEventListener", "removeEventListener", "e", "removeAttribute", "setAttribute", "createEventProxy", "<PERSON><PERSON><PERSON><PERSON>", "_dispatched", "event", "tmp", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "isClassComponent", "provider", "componentContext", "renderHook", "count", "newType", "outer", "prototype", "render", "contextType", "__E", "doR<PERSON>", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "some", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "cloneNode", "then", "MODE_HYDRATE", "indexOf", "diffElementNodes", "diffed", "root", "cb", "map", "newHtml", "oldHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputValue", "checked", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "__html", "innerHTML", "content", "hasRefUnmount", "current", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "documentElement", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "Promise", "bind", "resolve", "setTimeout", "a", "b", "defaultValue", "Context", "subs", "ctx", "Set", "_props", "for<PERSON>ach", "add", "old", "delete", "Provider", "__l", "Consumer", "contextValue", "hydrate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out"], "mappings": "oOA2BaA,EChBPC,ECPFC,EA2FSC,ECmFTC,EAWAC,EAEEC,EA0BAC,EC1MAC,EAaFC,EA+IEC,EACAC,ECzKKC,ECSEC,EAAgB,6BAChBC,EAAkB,+BAGlBC,EAAO,KACPC,OAAYC,EACZC,EAAgC,CAAG,EACnCC,EAAY,GACZC,EACZ,oENnBYC,EAAUC,MAAMD,QAStB,SAASE,EAAOC,EAAKC,GAE3B,IAAK,IAAIb,KAAKa,EAAOD,EAAIZ,GAAKa,EAAMb,GACpC,OAA6BY,CAC9B,CAQgB,SAAAE,EAAWC,GACtBA,GAAQA,EAAKC,YAAYD,EAAKC,WAAWC,YAAYF,EAC1D,CEVgB,SAAAG,EAAcC,EAAMN,EAAOO,GAC1C,IACCC,EACAC,EACAtB,EAHGuB,EAAkB,CAAA,EAItB,IAAKvB,KAAKa,EACA,OAALb,EAAYqB,EAAMR,EAAMb,GACd,OAALA,EAAYsB,EAAMT,EAAMb,GAC5BuB,EAAgBvB,GAAKa,EAAMb,GAUjC,GAPIwB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIrC,EAAMsC,KAAKF,UAAW,GAAKJ,GAKjC,mBAARD,GAAsBA,EAAKQ,cAAgBxB,EACrD,IAAKH,KAAKmB,EAAKQ,aACVJ,EAAgBvB,KAAOI,IAC1BmB,EAAgBvB,GAAKmB,EAAKQ,aAAa3B,IAK1C,OAAO4B,EAAYT,EAAMI,EAAiBF,EAAKC,EAAKnB,EACrD,CAcgB,SAAAyB,EAAYT,EAAMN,EAAOQ,EAAKC,EAAKO,GAIlD,IAAMC,EAAQ,CACbX,KAAAA,EACAN,MAAAA,EACAQ,IAAAA,EACAC,IAAAA,EACAS,IAAW5B,EACX6B,GAAS7B,EACT8B,IAAQ,EACRC,IAAM/B,EACNgC,IAAYhC,EACZiC,YAAahC,EACbiC,IAAWR,GAAY1B,IAASb,EAAUuC,EAC1CS,KAAS,EACTC,IAAQ,GAMT,OAFIV,GAAY1B,GAAQd,EAAQyC,OAAS3B,GAAMd,EAAQyC,MAAMA,GAEtDA,CACR,CAMgB,SAAAU,EAAS3B,GACxB,OAAOA,EAAMO,QACd,CC3EO,SAASqB,EAAc5B,EAAO6B,GACpCC,KAAK9B,MAAQA,EACb8B,KAAKD,QAAUA,CAChB,UA0EgBE,EAAcd,EAAOe,GACpC,GAAIA,GAAc1C,EAEjB,OAAO2B,EAAKE,GACTY,EAAcd,EAAKE,GAAUF,EAAKQ,IAAU,GAC5CnC,EAIJ,IADA,IAAI2C,EACGD,EAAaf,EAAKC,IAAWN,OAAQoB,IAG3C,IAFAC,EAAUhB,EAAKC,IAAWc,KAEX1C,GAAQ2C,EAAOZ,KAAS/B,EAItC,OAAO2C,EAAOZ,IAShB,MAA4B,mBAAdJ,EAAMX,KAAqByB,EAAcd,GAAS3B,CACjE,CA2CA,SAAS4C,EAAwBjB,GAAjC,IAGW9B,EACJgD,EAHN,IAAKlB,EAAQA,EAAKE,KAAa7B,GAAQ2B,EAAKK,KAAehC,EAAM,CAEhE,IADA2B,EAAKI,IAAQJ,EAAKK,IAAYc,KAAO9C,EAC5BH,EAAI,EAAGA,EAAI8B,EAAKC,IAAWN,OAAQzB,IAE3C,IADIgD,EAAQlB,EAAKC,IAAW/B,KACfG,GAAQ6C,EAAKd,KAAS/B,EAAM,CACxC2B,EAAKI,IAAQJ,EAAKK,IAAYc,KAAOD,EAAKd,IAC1C,KACD,CAGD,OAAOa,EAAwBjB,EAChC,CACD,CA4BgB,SAAAoB,EAAcC,KAE1BA,EAACC,MACDD,EAACC,KAAU,IACZ5D,EAAc6D,KAAKF,KAClBG,EAAOC,OACT9D,GAAgBJ,EAAQmE,sBAExB/D,EAAeJ,EAAQmE,oBACN9D,GAAO4D,EAE1B,CASA,SAASA,IAMR,IALA,IAAIH,EAnGoBM,EAOjBC,EANHC,EACHC,EACAC,EACAC,EAgGAC,EAAI,EAIEvE,EAAciC,QAOhBjC,EAAciC,OAASsC,GAC1BvE,EAAcwE,KAAKrE,GAGpBwD,EAAI3D,EAAcyE,QAClBF,EAAIvE,EAAciC,OAEd0B,EAACC,MA/GCM,SALNE,GADGD,GADoBF,EAuHNN,GAtHMd,KACNH,IACjB2B,EAAc,GACdC,EAAW,GAERL,EAASS,OACNR,EAAW/C,EAAO,GAAIgD,IACpBtB,IAAasB,EAAQtB,IAAa,EACtChD,EAAQyC,OAAOzC,EAAQyC,MAAM4B,GAEjCS,EACCV,EAASS,IACTR,EACAC,EACAF,EAASW,IACTX,EAASS,IAAYG,aGzII,GH0IzBV,EAAQpB,IAAyB,CAACqB,GAAUzD,EAC5C0D,EACAD,GAAUzD,EAAOyC,EAAce,GAAYC,KG5IlB,GH6ItBD,EAAQpB,KACXuB,GAGDJ,EAAQrB,IAAasB,EAAQtB,IAC7BqB,EAAQ1B,GAAAD,IAAmB2B,EAAQpB,KAAWoB,EAC9CY,EAAWT,EAAaH,EAAUI,GAE9BJ,EAAQxB,KAAS0B,GACpBb,EAAwBW,KA6F1BJ,EAAOC,IAAkB,CAC1B,UI3MgBgB,EACfC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,OAEI9D,EAEH2D,EAEAqB,EAEAC,EAEAC,EAiCIC,EA5BDC,EAAeT,GAAkBA,EAAc5C,KAAexB,EAE9D8E,EAAoBZ,EAAahD,OAUrC,IARAmC,EAAS0B,EACRZ,EACAD,EACAW,EACAxB,EACAyB,GAGIrF,EAAI,EAAGA,EAAIqF,EAAmBrF,KAClCgF,EAAaN,EAAc3C,IAAW/B,KACpBG,IAKjBwD,GADyB,GAAtBqB,EAAU1C,IACFhC,EAEA8E,EAAYJ,EAAU1C,MAAYhC,EAI9C0E,EAAU1C,IAAUtC,EAGhBmF,EAAShB,EACZK,EACAQ,EACArB,EACAiB,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,GAIDmB,EAASD,EAAU9C,IACf8C,EAAW1D,KAAOqC,EAASrC,KAAO0D,EAAW1D,MAC5CqC,EAASrC,KACZiE,EAAS5B,EAASrC,IAAKnB,EAAM6E,GAE9BlB,EAAST,KACR2B,EAAW1D,IACX0D,EAAU7C,KAAe8C,EACzBD,IAIEE,GAAiB/E,GAAQ8E,GAAU9E,IACtC+E,EAAgBD,GDtHS,EC0HzBD,EAAUzC,KACVoB,EAAQ5B,MAAeiD,EAAUjD,IAEjC6B,EAAS4B,EAAOR,EAAYpB,EAAQY,GACA,mBAAnBQ,EAAW7D,MAAsBgE,IAAW/E,EAC7DwD,EAASuB,EACCF,IACVrB,EAASqB,EAAOQ,aAIjBT,EAAUzC,MAAW,GAKtB,OAFAmC,EAAcxC,IAAQgD,EAEftB,CACR,CAOA,SAAS0B,EACRZ,EACAD,EACAW,EACAxB,EACAyB,GALD,IAQKrF,EAEAgF,EAEArB,EA8DG+B,EAOAC,EAnEHC,EAAoBR,EAAY3D,OACnCoE,EAAuBD,EAEpBE,EAAO,EAGX,IADApB,EAAc3C,IAAa,IAAIrB,MAAM2E,GAChCrF,EAAI,EAAGA,EAAIqF,EAAmBrF,KAGlCgF,EAAaP,EAAazE,KAGXG,GACO,kBAAd6E,GACc,mBAAdA,GA8CFU,EAAc1F,EAAI8F,GA/BvBd,EAAaN,EAAc3C,IAAW/B,GANjB,iBAAdgF,GACc,iBAAdA,GAEc,iBAAdA,GACPA,EAAW5C,aAAe2D,OAEiBnE,EAC1CzB,EACA6E,EACA7E,EACAA,EACAA,GAESM,EAAQuE,GACyBpD,EAC1CY,EACA,CAAEpB,SAAU4D,GACZ7E,EACAA,EACAA,GAES6E,EAAW5C,aAAehC,GAAa4E,EAAU/C,IAAU,EAK1BL,EAC1CoD,EAAW7D,KACX6D,EAAWnE,MACXmE,EAAW3D,IACX2D,EAAW1D,IAAM0D,EAAW1D,IAAMnB,EAClC6E,EAAU3C,KAGgC2C,GAIlChD,GAAW0C,EACrBM,EAAU/C,IAAUyC,EAAczC,IAAU,EAKtC0D,EAAiBX,EAAU1C,IAAU0D,EAC1ChB,EACAI,EACAM,EACAG,GAGDlC,EAAWxD,GACW,GAAlBwF,IAEHE,KADAlC,EAAWyB,EAAYO,MAGtBhC,EAAQpB,KD7OW,ICoPFoB,GAAYxD,GAAQwD,EAAQtB,KAAclC,IAGtC,GAAlBwF,IAeCN,EAAoBO,EACvBE,IACUT,EAAoBO,GAC9BE,KAK4B,mBAAnBd,EAAW7D,OACrB6D,EAAUzC,KDjRc,ICmRfoD,GAAiBD,IAiBvBC,GAAiBD,EAAc,EAClCI,IACUH,GAAiBD,EAAc,EACzCI,KAEIH,EAAgBD,EACnBI,IAEAA,IAMDd,EAAUzC,KDlTc,KCgLzBmC,EAAc3C,IAAW/B,GAAKG,EA2IhC,GAAI0F,EACH,IAAK7F,EAAI,EAAGA,EAAI4F,EAAmB5F,KAClC2D,EAAWyB,EAAYpF,KACPG,GAAuC,ID5TnC,EC4TKwD,EAAQpB,OAC5BoB,EAAQzB,KAAS0B,IACpBA,EAAShB,EAAce,IAGxBsC,EAAQtC,EAAUA,IAKrB,OAAOC,CACR,CAQA,SAAS4B,EAAOU,EAAatC,EAAQY,GAArC,IAIMpD,EACKpB,EAFV,GAA+B,mBAApBkG,EAAY/E,KAAoB,CAE1C,IADIC,EAAW8E,EAAWnE,IACjB/B,EAAI,EAAGoB,GAAYpB,EAAIoB,EAASK,OAAQzB,IAC5CoB,EAASpB,KAKZoB,EAASpB,GAAEgC,GAAWkE,EACtBtC,EAAS4B,EAAOpE,EAASpB,GAAI4D,EAAQY,IAIvC,OAAOZ,CACR,CAAWsC,EAAWhE,KAAS0B,IAC1BA,GAAUsC,EAAY/E,OAASqD,EAAU2B,SAASvC,KACrDA,EAAShB,EAAcsD,IAExB1B,EAAU4B,aAAaF,EAAWhE,IAAO0B,GAAUzD,GACnDyD,EAASsC,EAAWhE,KAGrB,GACC0B,EAASA,GAAUA,EAAO6B,kBAClB7B,GAAUzD,GAA2B,GAAnByD,EAAOyC,UAElC,OAAOzC,CACR,CA4BA,SAASoC,EACRhB,EACAI,EACAM,EACAG,GAJD,IAmCMS,EACAC,EA9BClF,EAAM2D,EAAW3D,IACjBF,EAAO6D,EAAW7D,KACpBwC,EAAWyB,EAAYM,GAkB3B,GACE/B,IAAaxD,GAA0B,MAAlB6E,EAAW3D,KAChCsC,GACAtC,GAAOsC,EAAStC,KAChBF,GAAQwC,EAASxC,MACc,IDxaX,ECwanBwC,EAAQpB,KAEV,OAAOmD,KAVPG,GACClC,GAAYxD,GAAuC,IDja/B,ECiaCwD,EAAQpB,KAA0B,EAAI,GAa5D,IAFI+D,EAAIZ,EAAc,EAClBa,EAAIb,EAAc,EACfY,GAAK,GAAKC,EAAInB,EAAY3D,QAAQ,CACxC,GAAI6E,GAAK,EAAG,CAEX,IADA3C,EAAWyB,EAAYkB,KAGS,IDnbb,ECmbjB3C,EAAQpB,MACTlB,GAAOsC,EAAStC,KAChBF,GAAQwC,EAASxC,KAEjB,OAAOmF,EAERA,GACD,CAEA,GAAIC,EAAInB,EAAY3D,OAAQ,CAE3B,IADAkC,EAAWyB,EAAYmB,KAGS,IDhcb,ECgcjB5C,EAAQpB,MACTlB,GAAOsC,EAAStC,KAChBF,GAAQwC,EAASxC,KAEjB,OAAOoF,EAERA,GACD,CACD,CAGD,OAAQ,CACT,CHhdA,SAASC,EAASC,EAAOpF,EAAKqF,GACf,KAAVrF,EAAI,GACPoF,EAAME,YAAYtF,EAAKqF,GAASvG,EAAO,GAAKuG,GAE5CD,EAAMpF,GADIqF,GAASvG,EACN,GACa,iBAATuG,GAAqBlG,EAAmBoG,KAAKvF,GACjDqF,EAEAA,EAAQ,IAEvB,CAyBgB,SAAAC,EAAYE,EAAKC,EAAMJ,EAAOK,EAAUlC,GAAxC,IACXmC,EA8BGC,EA5BPC,EAAG,GAAY,SAARJ,EACN,GAAoB,iBAATJ,EACVG,EAAIJ,MAAMU,QAAUT,MACd,CAKN,GAJuB,iBAAZK,IACVF,EAAIJ,MAAMU,QAAUJ,EAAW,IAG5BA,EACH,IAAKD,KAAQC,EACNL,GAASI,KAAQJ,GACtBF,EAASK,EAAIJ,MAAOK,EAAM,IAK7B,GAAIJ,EACH,IAAKI,KAAQJ,EACPK,GAAYL,EAAMI,IAASC,EAASD,IACxCN,EAASK,EAAIJ,MAAOK,EAAMJ,EAAMI,GAIpC,MAGI,GAAe,KAAXA,EAAK,IAAwB,KAAXA,EAAK,GAC/BE,EAAaF,IAASA,EAAOA,EAAKM,QAAQxH,EAAe,OACnDqH,EAAgBH,EAAKO,cAI1BP,EADGG,KAAiBJ,GAAe,cAARC,GAAgC,aAARA,EAC5CG,EAAc7H,MAAM,GAChB0H,EAAK1H,MAAM,GAElByH,EAAG9C,IAAa8C,EAAG9C,EAAc,CAAE,GACxC8C,EAAG9C,EAAY+C,EAAOE,GAAcN,EAEhCA,EACEK,EAQJL,EAAMY,EAAYP,EAASO,GAP3BZ,EAAMY,EAAYzH,EAClBgH,EAAIU,iBACHT,EACAE,EAAajH,EAAoBD,EACjCkH,IAMFH,EAAIW,oBACHV,EACAE,EAAajH,EAAoBD,EACjCkH,OAGI,CACN,GAAInC,GAAa5E,EAIhB6G,EAAOA,EAAKM,QAAQ,cAAe,KAAKA,QAAQ,SAAU,UAE1DN,GAAQ,SAARA,GACQ,UAARA,GACQ,QAARA,GACQ,QAARA,GACQ,QAARA,GAGQ,YAARA,GACQ,YAARA,GACQ,WAARA,GACQ,WAARA,GACQ,QAARA,GACQ,WAARA,GACAA,KAAQD,EAER,IACCA,EAAIC,GAAQJ,GAASvG,EAAO,GAAKuG,EAEjC,MAAMQ,CAER,CADG,MAAOO,GACV,CASoB,mBAATf,IAEAA,GAASvG,IAAmB,IAAVuG,GAA8B,KAAXI,EAAK,GAGpDD,EAAIa,gBAAgBZ,GAFpBD,EAAIc,aAAab,EAAc,WAARA,GAA8B,GAATJ,EAAgB,GAAKA,GAInE,CACD,CAOA,SAASkB,EAAiBZ,GAMzB,gBAAiBS,GAChB,GAAI9E,KAAIoB,EAAa,CACpB,IAAM8D,EAAelF,KAAIoB,EAAY0D,EAAEtG,KAAO6F,GAC9C,GAAIS,EAAEK,GAAe3H,EACpBsH,EAAEK,EAAcjI,SAKV,GAAI4H,EAAEK,EAAcD,EAAaP,EACvC,OAED,OAAOO,EAAaxI,EAAQ0I,MAAQ1I,EAAQ0I,MAAMN,GAAKA,EACxD,CACD,CACD,UIzHgBtD,EACfK,EACAd,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,OAGIkE,EAkBE7E,EAAG8E,EAAOC,EAAUC,EAAUC,EAAUC,EACxCC,EACEC,EAMFC,EACAC,EAyGOzI,EA4BP0I,EACHC,EASS3I,EA6BNyE,EAgDOzE,EAtPZ4I,EAAUlF,EAASvC,KAIpB,GAAIuC,EAAStB,aAAehC,EAAW,OAAOD,EF/DjB,IEkEzBwD,EAAQpB,MACXwC,KFrE0B,GEqETpB,EAAQpB,KAEzBuC,EAAoB,CADpBlB,EAASF,EAAQxB,IAAQyB,EAAQzB,OAI7B8F,EAAM3I,EAAO4C,MAAS+F,EAAItE,GAE/BmF,EAAO,GAAsB,mBAAXD,EACjB,IAkEC,GAhEIN,EAAW5E,EAAS7C,MAClB0H,EACL,cAAeK,GAAWA,EAAQE,UAAUC,OAKzCP,GADJR,EAAMY,EAAQI,cACQpE,EAAcoD,EAAG7F,KACnCsG,EAAmBT,EACpBQ,EACCA,EAAS3H,MAAM6F,MACfsB,EAAGhG,GACJ4C,EAGCjB,EAAQxB,IAEXkG,GADAlF,EAAIO,EAAQvB,IAAcwB,EAAQxB,KACNH,GAAwBmB,EAAC8F,KAGjDV,EAEH7E,EAAQvB,IAAcgB,EAAI,IAAIyF,EAAQN,EAAUG,IAGhD/E,EAAQvB,IAAcgB,EAAI,IAAIV,EAC7B6F,EACAG,GAEDtF,EAAEf,YAAcwG,EAChBzF,EAAE4F,OAASG,GAERV,GAAUA,EAASW,IAAIhG,GAE3BA,EAAEtC,MAAQyH,EACLnF,EAAEiG,QAAOjG,EAAEiG,MAAQ,CAAA,GACxBjG,EAAET,QAAU+F,EACZtF,EAACiB,IAAkBQ,EACnBqD,EAAQ9E,EAACC,KAAU,EACnBD,EAACkG,IAAoB,GACrBlG,EAACmG,IAAmB,IAIjBf,GAAoBpF,EAACoG,KAAepJ,IACvCgD,EAACoG,IAAcpG,EAAEiG,OAGdb,GAAoBK,EAAQY,0BAA4BrJ,IACvDgD,EAACoG,KAAepG,EAAEiG,QACrBjG,EAACoG,IAAc5I,EAAO,GAAIwC,EAACoG,MAG5B5I,EACCwC,EAACoG,IACDX,EAAQY,yBAAyBlB,EAAUnF,EAACoG,OAI9CrB,EAAW/E,EAAEtC,MACbsH,EAAWhF,EAAEiG,MACbjG,EAACd,IAAUqB,EAGPuE,EAEFM,GACAK,EAAQY,0BAA4BrJ,GACpCgD,EAAEsG,oBAAsBtJ,GAExBgD,EAAEsG,qBAGClB,GAAoBpF,EAAEuG,mBAAqBvJ,GAC9CgD,EAACkG,IAAkBhG,KAAKF,EAAEuG,uBAErB,CAUN,GARCnB,GACAK,EAAQY,0BAA4BrJ,GACpCmI,IAAaJ,GACb/E,EAAEwG,2BAA6BxJ,GAE/BgD,EAAEwG,0BAA0BrB,EAAUG,IAIpCtF,EAACjB,KACFiB,EAAEyG,uBAAyBzJ,IAKrB,IAJNgD,EAAEyG,sBACDtB,EACAnF,EAACoG,IACDd,IAEF/E,EAAQrB,KAAcsB,EAAQtB,IAC7B,CAkBD,IAhBIqB,EAAQrB,KAAcsB,EAAQtB,MAKjCc,EAAEtC,MAAQyH,EACVnF,EAAEiG,MAAQjG,EAACoG,IACXpG,EAACC,KAAU,GAGZM,EAAQxB,IAAQyB,EAAQzB,IACxBwB,EAAQ3B,IAAa4B,EAAQ5B,IAC7B2B,EAAQ3B,IAAW8H,KAAK,SAAA/H,GACnBA,IAAOA,EAAKE,GAAW0B,EAC5B,GAES1D,EAAI,EAAGA,EAAImD,EAACmG,IAAiB7H,OAAQzB,IAC7CmD,EAACkG,IAAkBhG,KAAKF,EAACmG,IAAiBtJ,IAE3CmD,EAACmG,IAAmB,GAEhBnG,EAACkG,IAAkB5H,QACtBoC,EAAYR,KAAKF,GAGlB,MAAM0F,CACP,CAEI1F,EAAE2G,qBAAuB3J,GAC5BgD,EAAE2G,oBAAoBxB,EAAUnF,EAACoG,IAAad,GAG3CF,GAAoBpF,EAAE4G,oBAAsB5J,GAC/CgD,EAACkG,IAAkBhG,KAAK,WACvBF,EAAE4G,mBAAmB7B,EAAUC,EAAUC,EAC1C,EAEF,CASA,GAPAjF,EAAET,QAAU+F,EACZtF,EAAEtC,MAAQyH,EACVnF,EAACe,IAAcM,EACfrB,EAACjB,KAAU,EAEPwG,EAAarJ,EAAOkE,IACvBoF,EAAQ,EACLJ,EAAkB,CAQrB,IAPApF,EAAEiG,MAAQjG,EAACoG,IACXpG,EAACC,KAAU,EAEPsF,GAAYA,EAAWhF,GAE3BsE,EAAM7E,EAAE4F,OAAO5F,EAAEtC,MAAOsC,EAAEiG,MAAOjG,EAAET,SAE1B1C,EAAI,EAAGA,EAAImD,EAACmG,IAAiB7H,OAAQzB,IAC7CmD,EAACkG,IAAkBhG,KAAKF,EAACmG,IAAiBtJ,IAE3CmD,EAACmG,IAAmB,EACrB,MACC,GACCnG,EAACC,KAAU,EACPsF,GAAYA,EAAWhF,GAE3BsE,EAAM7E,EAAE4F,OAAO5F,EAAEtC,MAAOsC,EAAEiG,MAAOjG,EAAET,SAGnCS,EAAEiG,MAAQjG,EAACoG,UACHpG,EAACC,OAAauF,EAAQ,IAIhCxF,EAAEiG,MAAQjG,EAACoG,IAEPpG,EAAE6G,iBAAmB7J,IACxByE,EAAgBjE,EAAOA,EAAO,CAAE,EAAEiE,GAAgBzB,EAAE6G,oBAGjDzB,IAAqBN,GAAS9E,EAAE8G,yBAA2B9J,IAC9DiI,EAAWjF,EAAE8G,wBAAwB/B,EAAUC,IAK5C1D,EAAeuD,EADlBA,GAAO7H,GAAQ6H,EAAI7G,OAASqB,GAAYwF,EAAI3G,KAAOlB,IAInDsE,EAAeyF,EAAUlC,EAAInH,MAAMO,WAGpCwC,EAASW,EACRC,EACA/D,EAAQgE,GAAgBA,EAAe,CAACA,GACxCf,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,GAGDX,EAAEF,KAAOS,EAAQxB,IAGjBwB,EAAQnB,MFjRe,IEmRnBY,EAACkG,IAAkB5H,QACtBoC,EAAYR,KAAKF,GAGdkF,IACHlF,EAAC8F,IAAiB9F,EAACnB,GAAwB7B,EA2B7C,CAzBE,MAAOsH,GAGR,GAFA/D,EAAQrB,IAAalC,EAEjB4E,GAAeD,GAAqB3E,EACvC,GAAIsH,EAAE0C,KAAM,CAKX,IAJAzG,EAAQnB,KAAWwC,EAChBqF,IFvSsB,IE0SlBxG,GAA6B,GAAnBA,EAAOyC,UAAiBzC,EAAO6B,aAC/C7B,EAASA,EAAO6B,YAGjBX,EAAkBA,EAAkBuF,QAAQzG,IAAWzD,EACvDuD,EAAQxB,IAAQ0B,CACjB,MACC,IAAS5D,EAAI8E,EAAkBrD,OAAQzB,KACtCc,EAAWgE,EAAkB9E,SAI/B0D,EAAQxB,IAAQyB,EAAQzB,IACxBwB,EAAQ3B,IAAa4B,EAAQ5B,IAE9B1C,EAAO6C,IAAauF,EAAG/D,EAAUC,EAClC,MAEAmB,GAAqB3E,GACrBuD,EAAQrB,KAAcsB,EAAQtB,KAE9BqB,EAAQ3B,IAAa4B,EAAQ5B,IAC7B2B,EAAQxB,IAAQyB,EAAQzB,KAExB0B,EAASF,EAAQxB,IAAQoI,EACxB3G,EAAQzB,IACRwB,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAkB,EACAjB,GAMF,OAFKkE,EAAM3I,EAAQkL,SAASvC,EAAItE,GF/UH,IEiVtBA,EAAQnB,SAA2BlC,EAAYuD,CACvD,UAOgBU,EAAWT,EAAa2G,EAAM1G,GAC7C,IAAK,IAAI9D,EAAI,EAAGA,EAAI8D,EAASrC,OAAQzB,IACpCuF,EAASzB,EAAS9D,GAAI8D,IAAW9D,GAAI8D,IAAW9D,IAG7CX,EAAO8C,KAAU9C,EAAO8C,IAASqI,EAAM3G,GAE3CA,EAAYgG,KAAK,SAAA1G,GAChB,IAECU,EAAcV,EAACkG,IACflG,EAACkG,IAAoB,GACrBxF,EAAYgG,KAAK,SAAAY,GAEhBA,EAAG/I,KAAKyB,EACT,EAGD,CAFE,MAAOsE,GACRpI,EAAO6C,IAAauF,EAAGtE,EAACd,IACzB,CACD,EACD,CAEA,SAAS6H,EAAUnJ,GAClB,MACgB,iBAARA,GACPA,GAAQZ,GACPY,EAAIkB,KAAWlB,EAAIkB,IAAU,EAEvBlB,EAGJN,EAAQM,GACJA,EAAK2J,IAAIR,GAGVvJ,EAAO,CAAE,EAAEI,EACnB,CAiBA,SAASuJ,EACRzD,EACAnD,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAkB,EACAjB,GATD,IAeK9D,EAEA2K,EAEAC,EAEAC,EACAnE,EACAoE,EACAC,EAbA7C,EAAWvE,EAAS9C,MACpByH,EAAW5E,EAAS7C,MACpBwF,EAAkC3C,EAASvC,KAkB/C,GAJgB,OAAZkF,EAAmBxB,EAAY5E,EACd,QAAZoG,EAAoBxB,EF/ZA,qCEganBA,IAAWA,EAAY3E,GAE7B4E,GAAqB3E,EACxB,IAAKH,EAAI,EAAGA,EAAI8E,EAAkBrD,OAAQzB,IAMzC,IALA0G,EAAQ5B,EAAkB9E,KAOzB,iBAAkB0G,KAAWL,IAC5BA,EAAWK,EAAMsE,WAAa3E,EAA6B,GAAlBK,EAAML,UAC/C,CACDQ,EAAMH,EACN5B,EAAkB9E,GAAKG,EACvB,KACD,CAIF,GAAI0G,GAAO1G,EAAM,CAChB,GAAIkG,GAAYlG,EACf,OAAO8K,SAASC,eAAe5C,GAGhCzB,EAAMoE,SAASE,gBACdtG,EACAwB,EACAiC,EAAS8C,IAAM9C,GAKZvD,IACC1F,EAAOgM,KACVhM,EAAOgM,IAAoB3H,EAAUoB,GACtCC,GAAc,GAGfD,EAAoB3E,CACrB,CAEA,GAAIkG,GAAYlG,EAEX+H,IAAaI,GAAcvD,GAAe8B,EAAIyE,MAAQhD,IACzDzB,EAAIyE,KAAOhD,OAEN,CASN,GAPAxD,EAAoBA,GAAqB1F,EAAMsC,KAAKmF,EAAI0E,YAExDrD,EAAWvE,EAAS9C,OAASP,GAKxByE,GAAeD,GAAqB3E,EAExC,IADA+H,EAAW,CAAA,EACNlI,EAAI,EAAGA,EAAI6G,EAAI2E,WAAW/J,OAAQzB,IAEtCkI,GADAxB,EAAQG,EAAI2E,WAAWxL,IACR8G,MAAQJ,EAAMA,MAI/B,IAAK1G,KAAKkI,EAET,GADAxB,EAAQwB,EAASlI,GACR,YAALA,QACOA,GAAK,2BAALA,EACV4K,EAAUlE,OACJ,KAAM1G,KAAKsI,GAAW,CAC5B,GACO,SAALtI,GAAgB,iBAAkBsI,GAC7B,WAALtI,GAAkB,mBAAoBsI,EAEvC,SAED3B,EAAYE,EAAK7G,EAAGG,EAAMuG,EAAO7B,EAClC,CAKD,IAAK7E,KAAKsI,EACT5B,EAAQ4B,EAAStI,GACR,YAALA,EACH6K,EAAcnE,EACC,2BAAL1G,EACV2K,EAAUjE,EACK,SAAL1G,EACV8K,EAAapE,EACE,WAAL1G,EACV+K,EAAUrE,EAER3B,GAA+B,mBAAT2B,GACxBwB,EAASlI,KAAO0G,GAEhBC,EAAYE,EAAK7G,EAAG0G,EAAOwB,EAASlI,GAAI6E,GAK1C,GAAI8F,EAGD5F,GACC6F,IACAD,EAAOc,QAAWb,EAAOa,QAAWd,EAAOc,QAAW5E,EAAI6E,aAE5D7E,EAAI6E,UAAYf,EAAOc,QAGxB/H,EAAQ3B,IAAa,QAsBrB,GApBI6I,IAAS/D,EAAI6E,UAAY,IAE7BnH,EAEkB,YAAjBb,EAASvC,KAAqB0F,EAAI8E,QAAU9E,EAC5CpG,EAAQoK,GAAeA,EAAc,CAACA,GACtCnH,EACAC,EACAiB,EACY,iBAAZyB,EAA8BnG,EAAkB2E,EAChDC,EACAjB,EACAiB,EACGA,EAAkB,GAClBnB,EAAQ5B,KAAca,EAAce,EAAU,GACjDoB,EACAjB,GAIGgB,GAAqB3E,EACxB,IAAKH,EAAI8E,EAAkBrD,OAAQzB,KAClCc,EAAWgE,EAAkB9E,IAM3B+E,IACJ/E,EAAI,QACY,YAAZqG,GAA0ByE,GAAc3K,EAC3C0G,EAAIa,gBAAgB,SAEpBoD,GAAc1K,IAKb0K,IAAejE,EAAI7G,IACN,YAAZqG,IAA2ByE,GAIf,UAAZzE,GAAwByE,GAAc5C,EAASlI,KAEjD2G,EAAYE,EAAK7G,EAAG8K,EAAY5C,EAASlI,GAAI6E,GAG9C7E,EAAI,UACA+K,GAAW3K,GAAa2K,GAAWlE,EAAI7G,IAC1C2G,EAAYE,EAAK7G,EAAG+K,EAAS7C,EAASlI,GAAI6E,GAG7C,CAEA,OAAOgC,CACR,CAQgB,SAAAtB,EAASjE,EAAKoF,EAAO5E,GACpC,IACC,GAAkB,mBAAPR,EAAmB,CAC7B,IAAIsK,EAAuC,mBAAhBtK,EAAGiB,IAC1BqJ,GAEHtK,EAAGiB,MAGCqJ,GAAiBlF,GAASvG,IAI9BmB,EAAGiB,IAAYjB,EAAIoF,GAErB,MAAOpF,EAAIuK,QAAUnF,CAGtB,CAFE,MAAOe,GACRpI,EAAO6C,IAAauF,EAAG3F,EACxB,CACD,CASgB,SAAAmE,EAAQnE,EAAOoE,EAAa4F,GAA5B,IACXC,EAsBM/L,EAbV,GARIX,EAAQ4G,SAAS5G,EAAQ4G,QAAQnE,IAEhCiK,EAAIjK,EAAMR,OACTyK,EAAEF,SAAWE,EAAEF,SAAW/J,EAAKI,KACnCqD,EAASwG,EAAG5L,EAAM+F,KAIf6F,EAAIjK,EAAKK,MAAgBhC,EAAM,CACnC,GAAI4L,EAAEC,qBACL,IACCD,EAAEC,sBAGH,CAFE,MAAOvE,GACRpI,EAAO6C,IAAauF,EAAGvB,EACxB,CAGD6F,EAAE9I,KAAO8I,EAAC7H,IAAc/D,CACzB,CAEA,GAAK4L,EAAIjK,EAAKC,IACb,IAAS/B,EAAI,EAAGA,EAAI+L,EAAEtK,OAAQzB,IACzB+L,EAAE/L,IACLiG,EACC8F,EAAE/L,GACFkG,EACA4F,GAAmC,mBAAdhK,EAAMX,MAM1B2K,GACJhL,EAAWgB,EAAKI,KAGjBJ,EAAKK,IAAcL,EAAKE,GAAWF,EAAKI,IAAQ9B,CACjD,CAGA,SAAS8I,EAASrI,EAAOuI,EAAO1G,GAC/B,YAAYN,YAAYvB,EAAO6B,EAChC,CC3pBO,SAASqG,EAAOjH,EAAO0C,EAAWyH,GAAlC,IAWFlH,EAOApB,EAQAE,EACHC,EAzBGU,GAAayG,WAChBzG,EAAYyG,SAASiB,iBAGlB7M,EAAO2C,IAAQ3C,EAAO2C,GAAOF,EAAO0C,GAYpCb,GAPAoB,EAAoC,mBAAfkH,GAQtB9L,EACC8L,GAAeA,EAAWlK,KAAeyC,EAASzC,IAMlD8B,EAAc,GACjBC,EAAW,GACZK,EACCK,EAPD1C,IAAWiD,GAAekH,GAAgBzH,GAASzC,IAClDb,EAAcsB,EAAUrC,EAAM,CAAC2B,IAU/B6B,GAAYrD,EACZA,EACAkE,EAAUH,cACTU,GAAekH,EACb,CAACA,GACDtI,EACCxD,EACAqE,EAAU2H,WACT/M,EAAMsC,KAAK8C,EAAU+G,YACrBpL,EACL0D,GACCkB,GAAekH,EACbA,EACAtI,EACCA,EAAQzB,IACRsC,EAAU2H,WACdpH,EACAjB,GAIDQ,EAAWT,EAAa/B,EAAOgC,EAChC,CTzCa1E,EAAQmB,EAAUnB,MChBzBC,EAAU,CACf6C,ISDM,SAAqBkK,EAAOtK,EAAO6B,EAAU0I,GAQnD,IANA,IAAI5I,EAEH6I,EAEAC,EAEOzK,EAAQA,EAAKE,IACpB,IAAKyB,EAAY3B,EAAKK,OAAiBsB,EAASzB,GAC/C,IAcC,IAbAsK,EAAO7I,EAAUrB,cAELkK,EAAKE,0BAA4BrM,IAC5CsD,EAAUgJ,SAASH,EAAKE,yBAAyBJ,IACjDG,EAAU9I,EAASL,KAGhBK,EAAUiJ,mBAAqBvM,IAClCsD,EAAUiJ,kBAAkBN,EAAOC,GAAa,CAAE,GAClDE,EAAU9I,EAASL,KAIhBmJ,EACH,OAAQ9I,EAASwF,IAAiBxF,CAIpC,CAFE,MAAOgE,GACR2E,EAAQ3E,CACT,CAIF,MAAM2E,CACP,GRzCI9M,EAAU,EA2FDC,EAAiB,SAAAuC,GAAK,OAClCA,GAAS3B,GAAQ2B,EAAMM,aAAehC,CAAS,ECrEhDqC,EAAcqG,UAAU2D,SAAW,SAAUE,EAAQC,GAEpD,IAAIC,EAEHA,EADGlK,KAAI4G,KAAepJ,GAAQwC,KAAI4G,KAAe5G,KAAKyG,MAClDzG,KAAI4G,IAEJ5G,KAAI4G,IAAc5I,EAAO,CAAE,EAAEgC,KAAKyG,OAGlB,mBAAVuD,IAGVA,EAASA,EAAOhM,EAAO,GAAIkM,GAAIlK,KAAK9B,QAGjC8L,GACHhM,EAAOkM,EAAGF,GAIPA,GAAUxM,GAEVwC,KAAIN,MACHuK,GACHjK,KAAI2G,IAAiBjG,KAAKuJ,GAE3B1J,EAAcP,MAEhB,EAQAF,EAAcqG,UAAUgE,YAAc,SAAUF,GAC3CjK,KAAIN,MAIPM,KAAIT,KAAU,EACV0K,GAAUjK,KAAI0G,IAAkBhG,KAAKuJ,GACzC1J,EAAcP,MAEhB,EAYAF,EAAcqG,UAAUC,OAASvG,EA8F7BhD,EAAgB,GAadE,EACa,mBAAXqN,QACJA,QAAQjE,UAAUqB,KAAK6C,KAAKD,QAAQE,WACpCC,WAuBEvN,EAAY,SAACwN,EAAGC,UAAMD,EAAC9K,IAAAJ,IAAiBmL,EAAC/K,IAAAJ,GAAc,EA8B7DqB,EAAOC,IAAkB,ECxOnB3D,EAAgB,8BAalBC,EAAa,EA+IXC,EAAa8H,GAAiB,GAC9B7H,EAAoB6H,GAAiB,GCzKhC5H,EAAI,qDMUc8B,EAAOjB,EAAOO,OAEzCC,EACAC,EACAtB,EAEG2B,EALAJ,EAAkBZ,EAAO,CAAE,EAAEmB,EAAMjB,OAWvC,IAAKb,KAJD8B,EAAMX,MAAQW,EAAMX,KAAKQ,eAC5BA,EAAeG,EAAMX,KAAKQ,cAGjBd,EACA,OAALb,EAAYqB,EAAMR,EAAMb,GACd,OAALA,EAAYsB,EAAMT,EAAMb,GAEhCuB,EAAgBvB,GADRa,EAAMb,KAAOI,GAAauB,GAAgBvB,EAC7BuB,EAAa3B,GAEba,EAAMb,GAS7B,OALIwB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIrC,EAAMsC,KAAKF,UAAW,GAAKJ,GAG7CQ,EACNE,EAAMX,KACNI,EACAF,GAAOS,EAAMT,IACbC,GAAOQ,EAAMR,IACbnB,EAEF,kBN1CgB,SAAckN,GAC7B,SAASC,EAAQzM,GAAjB,IAGM0M,EACAC,EA+BL,OAlCK7K,KAAKqH,kBAELuD,EAAO,IAAIE,KACXD,EAAM,CAAE,GACRF,EAAOnL,KAAQQ,KAEnBA,KAAKqH,gBAAkB,WAAM,OAAAwD,CAAG,EAEhC7K,KAAKqJ,qBAAuB,WAC3BuB,EAAOpN,CACR,EAEAwC,KAAKiH,sBAAwB,SAAU8D,GAElC/K,KAAK9B,MAAM6F,OAASgH,EAAOhH,OAC9B6G,EAAKI,QAAQ,SAAAxK,GACZA,EAACjB,KAAU,EACXgB,EAAcC,EACf,EAEF,EAEAR,KAAKwG,IAAM,SAAAhG,GACVoK,EAAKK,IAAIzK,GACT,IAAI0K,EAAM1K,EAAE6I,qBACZ7I,EAAE6I,qBAAuB,WACpBuB,GACHA,EAAKO,OAAO3K,GAET0K,GAAKA,EAAInM,KAAKyB,EACnB,CACD,GAGMtC,EAAMO,QACd,CAgBA,OAdAkM,EAAOnL,IAAO,OAASnC,IACvBsN,EAAOtL,GAAiBqL,EAQxBC,EAAQS,SACPT,EAAOU,KANRV,EAAQW,SAAW,SAACpN,EAAOqN,GAC1B,OAAOrN,EAAMO,SAAS8M,EACvB,GAKkBlF,YAChBsE,EAEKA,CACR,2CHwBC,MAAO,CAAEzB,QAAS1L,EACnB,kBOTO,SAASgO,EAAQrM,EAAO0C,GAC9BuE,EAAOjH,EAAO0C,EAAW2J,EAC1B,oEF+SgBC,EAAahN,EAAUiN,GAUtC,OATAA,EAAMA,GAAO,GACTjN,GAAYjB,GAA2B,kBAAZiB,IACpBX,EAAQW,GAClBA,EAASyI,KAAK,SAAA7G,GACboL,EAAapL,EAAOqL,EACrB,GAEAA,EAAIhL,KAAKjC,IAEHiN,CACR"}