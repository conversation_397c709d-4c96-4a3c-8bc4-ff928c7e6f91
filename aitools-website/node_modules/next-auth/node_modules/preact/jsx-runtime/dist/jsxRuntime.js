var r=require("preact"),e=/["&<]/;function t(r){if(0===r.length||!1===e.test(r))return r;for(var t=0,n=0,o="",f="";n<r.length;n++){switch(r.charCodeAt(n)){case 34:f="&quot;";break;case 38:f="&amp;";break;case 60:f="&lt;";break;default:continue}n!==t&&(o+=r.slice(t,n)),o+=f,t=n+1}return n!==t&&(o+=r.slice(t,n)),o}var n=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,o=0,f=Array.isArray;function u(e,t,n,f,u,i){t||(t={});var c,a,p=t;if("ref"in p)for(a in p={},t)"ref"==a?c=t[a]:p[a]=t[a];var l={type:e,props:p,key:n,ref:c,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--o,__i:-1,__u:0,__source:u,__self:i};if("function"==typeof e&&(c=e.defaultProps))for(a in c)void 0===p[a]&&(p[a]=c[a]);return r.options.vnode&&r.options.vnode(l),l}var i={},c=/[A-Z]/g;Object.defineProperty(exports,"Fragment",{enumerable:!0,get:function(){return r.Fragment}}),exports.jsx=u,exports.jsxAttr=function(e,o){if(r.options.attr){var f=r.options.attr(e,o);if("string"==typeof f)return f}if(o=function(r){return null!==r&&"object"==typeof r&&"function"==typeof r.valueOf?r.valueOf():r}(o),"ref"===e||"key"===e)return"";if("style"===e&&"object"==typeof o){var u="";for(var a in o){var p=o[a];if(null!=p&&""!==p){var l="-"==a[0]?a:i[a]||(i[a]=a.replace(c,"-$&").toLowerCase()),s=";";"number"!=typeof p||l.startsWith("--")||n.test(l)||(s="px;"),u=u+l+":"+p+s}}return e+'="'+t(u)+'"'}return null==o||!1===o||"function"==typeof o||"object"==typeof o?"":!0===o?e:e+'="'+t(""+o)+'"'},exports.jsxDEV=u,exports.jsxEscape=function r(e){if(null==e||"boolean"==typeof e||"function"==typeof e)return null;if("object"==typeof e){if(void 0===e.constructor)return e;if(f(e)){for(var n=0;n<e.length;n++)e[n]=r(e[n]);return e}}return t(""+e)},exports.jsxTemplate=function(e){var t=u(r.Fragment,{tpl:e,exprs:[].slice.call(arguments,1)});return t.key=t.__v,t},exports.jsxs=u;
//# sourceMappingURL=jsxRuntime.js.map
