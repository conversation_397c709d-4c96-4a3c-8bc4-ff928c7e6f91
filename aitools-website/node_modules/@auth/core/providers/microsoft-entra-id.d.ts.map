{"version": 3, "file": "microsoft-entra-id.d.ts", "sourceRoot": "", "sources": ["../src/providers/microsoft-entra-id.ts"], "names": [], "mappings": "AAWA,OAAO,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,YAAY,CAAA;AAE5D;;;GAGG;AACH,MAAM,WAAW,uBAAuB;IACtC;;;;;OAKG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;;;;;;sDAOkD;IAClD,GAAG,EAAE,MAAM,CAAA;IACX,gEAAgE;IAChE,GAAG,EAAE,IAAI,CAAA;IACT;;;;;;;;;OASG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;OAEG;IACH,GAAG,EAAE,IAAI,CAAA;IACT;;;;;OAKG;IACH,GAAG,EAAE,IAAI,CAAA;IACT;;;;;;;OAOG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;;;;;;OAOG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;OAGG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;;;;;;OAOG;IACH,kBAAkB,EAAE,MAAM,CAAA;IAC1B;;;;;;;;;;;;OAYG;IACH,KAAK,EAAE,MAAM,CAAA;IACb;;;;;OAKG;IACH,IAAI,EAAE,MAAM,CAAA;IACZ;;;OAGG;IACH,KAAK,EAAE,MAAM,CAAA;IACb;;;;;;;;;;;OAWG;IACH,GAAG,EAAE,MAAM,CAAA;IACX,yEAAyE;IACzE,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,sEAAsE;IACtE,EAAE,EAAE,MAAM,CAAA;IACV;;;;;;;;OAQG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;;;;OAKG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;;OAGG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;;OAGG;IACH,GAAG,EAAE,MAAM,CAAA;IACX,6CAA6C;IAC7C,GAAG,EAAE,KAAK,CAAA;IACV;;;;;OAKG;IACH,SAAS,EAAE,OAAO,CAAA;IAClB;;;OAGG;IACH,IAAI,EAAE,CAAC,GAAG,CAAC,CAAA;IACX;;;;;OAKG;IACH,IAAI,EAAE,MAAM,CAAA;IACZ,6CAA6C;IAC7C,SAAS,EAAE,IAAI,CAAA;IACf;;;;OAIG;IACH,IAAI,EAAE,MAAM,CAAA;IACZ;;;OAGG;IACH,GAAG,EAAE,MAAM,CAAA;IACX;;;;;OAKG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;;;;;;;;;;;;;;OAeG;IACH,UAAU,EAAE,MAAM,CAAA;IAClB;;;OAGG;IACH,WAAW,EAAE,MAAM,CAAA;IACnB;;OAEG;IACH,mBAAmB,EAAE,MAAM,CAAA;IAC3B;;;;;;;;;;;;;;;;;OAiBG;IACH,GAAG,EAAE,MAAM,CAAA;IACX,wDAAwD;IACxD,sBAAsB,EAAE,MAAM,EAAE,CAAA;IAChC,0DAA0D;IAC1D,wBAAwB,EAAE,MAAM,EAAE,CAAA;IAClC,kCAAkC;IAClC,IAAI,EAAE,MAAM,CAAA;IACZ;;;;;;;;;;OAUG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;;;;;;;;OASG;IACH,QAAQ,EAAE,OAAO,CAAA;IACjB;;;;;OAKG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;;OAGG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;OAEG;IACH,KAAK,EAAE,MAAM,CAAA;IACb,4DAA4D;IAC5D,MAAM,EAAE,MAAM,CAAA;IACd,sCAAsC;IACtC,UAAU,EAAE,MAAM,CAAA;IAClB;;;;;OAKG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;;;;OAMG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;;OAIG;IACH,WAAW,EAAE,MAAM,CAAA;IACnB;;;;OAIG;IACH,UAAU,EAAE,MAAM,CAAA;CACnB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+FG;AACH,MAAM,CAAC,OAAO,UAAU,gBAAgB,CACtC,MAAM,EAAE,cAAc,CAAC,uBAAuB,CAAC,GAAG;IAChD;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;CACpE,GACA,UAAU,CAAC,uBAAuB,CAAC,CAuDrC"}