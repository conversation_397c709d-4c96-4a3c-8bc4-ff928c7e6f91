{"version": 3, "file": "index.module.js", "sources": ["../../../src/lib/util.js", "../../../src/lib/constants.js", "../../../src/index.js", "../../../src/lib/client.js", "../../../src/lib/chunked.js", "../../../src/stream-node.js"], "sourcesContent": ["export const VOID_ELEMENTS = /^(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const NAMESPACE_REPLACE_REGEX = /^(xlink|xmlns|xml)([A-Z])/;\nexport const HTML_LOWER_CASE = /^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/;\nexport const SVG_CAMEL_CASE = /^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/;\n\n// Boolean DOM properties that translate to enumerated ('true'/'false') attributes\nexport const HTML_ENUMERATED = new Set(['draggable', 'spellcheck']);\n\n// DOM properties that should NOT have \"px\" added when numeric\nconst ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out = out + str.slice(last, i);\n\t\tout = out + ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out = out + str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst IS_NON_DIMENSIONAL = new Set([\n\t'animation-iteration-count',\n\t'border-image-outset',\n\t'border-image-slice',\n\t'border-image-width',\n\t'box-flex',\n\t'box-flex-group',\n\t'box-ordinal-group',\n\t'column-count',\n\t'fill-opacity',\n\t'flex',\n\t'flex-grow',\n\t'flex-negative',\n\t'flex-order',\n\t'flex-positive',\n\t'flex-shrink',\n\t'flood-opacity',\n\t'font-weight',\n\t'grid-column',\n\t'grid-row',\n\t'line-clamp',\n\t'line-height',\n\t'opacity',\n\t'order',\n\t'orphans',\n\t'stop-opacity',\n\t'stroke-dasharray',\n\t'stroke-dashoffset',\n\t'stroke-miterlimit',\n\t'stroke-opacity',\n\t'stroke-width',\n\t'tab-size',\n\t'widows',\n\t'z-index',\n\t'zoom'\n]);\n\nconst CSS_REGEX = /[A-Z]/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tconst name =\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\tlet suffix = ';';\n\t\t\tif (\n\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t// Exclude custom-attributes\n\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t!IS_NON_DIMENSIONAL.has(name)\n\t\t\t) {\n\t\t\t\tsuffix = 'px;';\n\t\t\t}\n\t\t\tstr = str + name + ':' + val + suffix;\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: new Array(0)\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n\n/**\n * @template T\n */\nexport class Deferred {\n\tconstructor() {\n\t\t// eslint-disable-next-line lines-around-comment\n\t\t/** @type {Promise<T>} */\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n}\n", "// Options hooks\nexport const DIFF = '__b';\nexport const RENDER = '__r';\nexport const DIFFED = 'diffed';\nexport const COMMIT = '__c';\nexport const SKIP_EFFECTS = '__s';\nexport const CATCH_ERROR = '__e';\n\n// VNode properties\nexport const COMPONENT = '__c';\nexport const CHILDREN = '__k';\nexport const PARENT = '__';\nexport const MASK = '__m';\n\n// Component properties\nexport const VNODE = '__v';\nexport const DIRTY = '__d';\nexport const NEXT_STATE = '__s';\nexport const CHILD_DID_SUSPEND = '__c';\n", "import {\n\tencodeEntities,\n\tstyleObjToCss,\n\tUNSAFE_NAME,\n\tNAMESPACE_REPLACE_REGEX,\n\tHTML_LOWER_CASE,\n\tHTML_ENUMERATED,\n\tSVG_CAMEL_CASE,\n\tcreateComponent\n} from './lib/util.js';\nimport { options, h, Fragment } from 'preact';\nimport {\n\tCHILDREN,\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tPARENT,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE,\n\tCATCH_ERROR\n} from './lib/constants.js';\n\nconst EMPTY_OBJ = {};\nconst EMPTY_ARR = [];\nconst isArray = Array.isArray;\nconst assign = Object.assign;\nconst EMPTY_STR = '';\n\n// Global state for the current render pass\nlet beforeDiff, afterDiff, renderHook, ummountHook;\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @param {RendererState} [_rendererState] for internal use\n * @returns {string} serialized HTML\n */\nexport function renderToString(vnode, context, _rendererState) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\tfalse,\n\t\t\t_rendererState\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\treturn rendered.join(EMPTY_STR);\n\t\t}\n\t\treturn rendered;\n\t} catch (e) {\n\t\tif (e.then) {\n\t\t\tthrow new Error('Use \"renderToStringAsync\" for suspenseful rendering.');\n\t\t}\n\n\t\tthrow e;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @returns {string} serialized HTML\n */\nexport async function renderToStringAsync(vnode, context) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = await _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\ttrue,\n\t\t\tundefined\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\tlet count = 0;\n\t\t\tlet resolved = rendered;\n\n\t\t\t// Resolving nested Promises with a maximum depth of 25\n\t\t\twhile (\n\t\t\t\tresolved.some(\n\t\t\t\t\t(element) => element && typeof element.then === 'function'\n\t\t\t\t) &&\n\t\t\t\tcount++ < 25\n\t\t\t) {\n\t\t\t\tresolved = (await Promise.all(resolved)).flat();\n\t\t\t}\n\n\t\t\treturn resolved.join(EMPTY_STR);\n\t\t}\n\n\t\treturn rendered;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n */\nfunction renderClassComponent(vnode, context) {\n\tlet type = /** @type {import(\"preact\").ComponentClass<typeof vnode.props>} */ (vnode.type);\n\n\tlet isMounting = true;\n\tlet c;\n\tif (vnode[COMPONENT]) {\n\t\tisMounting = false;\n\t\tc = vnode[COMPONENT];\n\t\tc.state = c[NEXT_STATE];\n\t} else {\n\t\tc = new type(vnode.props, context);\n\t}\n\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\n\tc.props = vnode.props;\n\tc.context = context;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\n\tif (c.state == null) c.state = EMPTY_OBJ;\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tif (type.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\ttype.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (isMounting && c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t} else if (!isMounting && c.componentWillUpdate) {\n\t\tc.componentWillUpdate();\n\t}\n\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, context);\n}\n\n/**\n * Recursively render VNodes to HTML.\n * @param {VNode|any} vnode\n * @param {any} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode} parent\n * @param {boolean} asyncMode\n * @param {RendererState | undefined} [renderer]\n * @returns {string | Promise<string> | (string | Promise<string>)[]}\n */\nfunction _renderToString(\n\tvnode,\n\tcontext,\n\tisSvgMode,\n\tselectValue,\n\tparent,\n\tasyncMode,\n\trenderer\n) {\n\t// Ignore non-rendered VNodes/values\n\tif (\n\t\tvnode == null ||\n\t\tvnode === true ||\n\t\tvnode === false ||\n\t\tvnode === EMPTY_STR\n\t) {\n\t\treturn EMPTY_STR;\n\t}\n\n\tlet vnodeType = typeof vnode;\n\t// Text VNodes: escape as HTML\n\tif (vnodeType != 'object') {\n\t\tif (vnodeType == 'function') return EMPTY_STR;\n\t\treturn vnodeType == 'string' ? encodeEntities(vnode) : vnode + EMPTY_STR;\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = EMPTY_STR,\n\t\t\trenderArray;\n\t\tparent[CHILDREN] = vnode;\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tlet child = vnode[i];\n\t\t\tif (child == null || typeof child == 'boolean') continue;\n\n\t\t\tconst childRender = _renderToString(\n\t\t\t\tchild,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tparent,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (typeof childRender == 'string') {\n\t\t\t\trendered = rendered + childRender;\n\t\t\t} else {\n\t\t\t\tif (!renderArray) {\n\t\t\t\t\trenderArray = [];\n\t\t\t\t}\n\n\t\t\t\tif (rendered) renderArray.push(rendered);\n\n\t\t\t\trendered = EMPTY_STR;\n\n\t\t\t\tif (isArray(childRender)) {\n\t\t\t\t\trenderArray.push(...childRender);\n\t\t\t\t} else {\n\t\t\t\t\trenderArray.push(childRender);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (renderArray) {\n\t\t\tif (rendered) renderArray.push(rendered);\n\t\t\treturn renderArray;\n\t\t}\n\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return EMPTY_STR;\n\n\tvnode[PARENT] = parent;\n\tif (beforeDiff) beforeDiff(vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tif (typeof type == 'function') {\n\t\tlet cctx = context,\n\t\t\tcontextType,\n\t\t\trendered,\n\t\t\tcomponent;\n\t\tif (type === Fragment) {\n\t\t\t// Serialized precompiled JSX.\n\t\t\tif ('tpl' in props) {\n\t\t\t\tlet out = EMPTY_STR;\n\t\t\t\tfor (let i = 0; i < props.tpl.length; i++) {\n\t\t\t\t\tout = out + props.tpl[i];\n\n\t\t\t\t\tif (props.exprs && i < props.exprs.length) {\n\t\t\t\t\t\tconst value = props.exprs[i];\n\t\t\t\t\t\tif (value == null) continue;\n\n\t\t\t\t\t\t// Check if we're dealing with a vnode or an array of nodes\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\ttypeof value == 'object' &&\n\t\t\t\t\t\t\t(value.constructor === undefined || isArray(value))\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tout =\n\t\t\t\t\t\t\t\tout +\n\t\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Values are pre-escaped by the JSX transform\n\t\t\t\t\t\t\tout = out + value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn out;\n\t\t\t} else if ('UNSTABLE_comment' in props) {\n\t\t\t\t// Fragments are the least used components of core that's why\n\t\t\t\t// branching here for comments has the least effect on perf.\n\t\t\t\treturn '<!--' + encodeEntities(props.UNSTABLE_comment) + '-->';\n\t\t\t}\n\n\t\t\trendered = props.children;\n\t\t} else {\n\t\t\tcontextType = type.contextType;\n\t\t\tif (contextType != null) {\n\t\t\t\tlet provider = context[contextType.__c];\n\t\t\t\tcctx = provider ? provider.props.value : contextType.__;\n\t\t\t}\n\n\t\t\tlet isClassComponent =\n\t\t\t\ttype.prototype && typeof type.prototype.render == 'function';\n\t\t\tif (isClassComponent) {\n\t\t\t\trendered = /**#__NOINLINE__**/ renderClassComponent(vnode, cctx);\n\t\t\t\tcomponent = vnode[COMPONENT];\n\t\t\t} else {\n\t\t\t\tvnode[COMPONENT] = component = /**#__NOINLINE__**/ createComponent(\n\t\t\t\t\tvnode,\n\t\t\t\t\tcctx\n\t\t\t\t);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (component[DIRTY] && count++ < 25) {\n\t\t\t\t\tcomponent[DIRTY] = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\trendered = type.call(component, props, cctx);\n\t\t\t\t}\n\t\t\t\tcomponent[DIRTY] = true;\n\t\t\t}\n\n\t\t\tif (component.getChildContext != null) {\n\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\tisClassComponent &&\n\t\t\t\toptions.errorBoundaries &&\n\t\t\t\t(type.getDerivedStateFromError || component.componentDidCatch)\n\t\t\t) {\n\t\t\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t\t\t// need to mirror that logic here too\n\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\trendered != null &&\n\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\trendered.key == null &&\n\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (err) {\n\t\t\t\t\tif (type.getDerivedStateFromError) {\n\t\t\t\t\t\tcomponent[NEXT_STATE] = type.getDerivedStateFromError(err);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component.componentDidCatch) {\n\t\t\t\t\t\tcomponent.componentDidCatch(err, EMPTY_OBJ);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component[DIRTY]) {\n\t\t\t\t\t\trendered = renderClassComponent(vnode, context);\n\t\t\t\t\t\tcomponent = vnode[COMPONENT];\n\n\t\t\t\t\t\tif (component.getChildContext != null) {\n\t\t\t\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\t\t\trendered != null &&\n\t\t\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\t\t\trendered.key == null &&\n\t\t\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn EMPTY_STR;\n\t\t\t\t} finally {\n\t\t\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t\t\tvnode[PARENT] = null;\n\n\t\t\t\t\tif (ummountHook) ummountHook(vnode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t// need to mirror that logic here too\n\t\tlet isTopLevelFragment =\n\t\t\trendered != null &&\n\t\t\trendered.type === Fragment &&\n\t\t\trendered.key == null &&\n\t\t\trendered.props.tpl == null;\n\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\ttry {\n\t\t\t// Recurse into children before invoking the after-diff hook\n\t\t\tconst str = _renderToString(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tvnode,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t// when we are dealing with suspense we can't do this...\n\t\t\tvnode[PARENT] = null;\n\n\t\t\tif (options.unmount) options.unmount(vnode);\n\n\t\t\treturn str;\n\t\t} catch (error) {\n\t\t\tif (!asyncMode && renderer && renderer.onError) {\n\t\t\t\tlet res = renderer.onError(error, vnode, (child) =>\n\t\t\t\t\t_renderToString(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\tif (res !== undefined) return res;\n\n\t\t\t\tlet errorHook = options[CATCH_ERROR];\n\t\t\t\tif (errorHook) errorHook(error, vnode);\n\t\t\t\treturn EMPTY_STR;\n\t\t\t}\n\n\t\t\tif (!asyncMode) throw error;\n\n\t\t\tif (!error || typeof error.then != 'function') throw error;\n\n\t\t\tconst renderNestedChildren = () => {\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tif (!e || typeof e.then != 'function') throw e;\n\n\t\t\t\t\treturn e.then(\n\t\t\t\t\t\t() =>\n\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\trenderNestedChildren\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\treturn error.then(renderNestedChildren);\n\t\t}\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<' + type,\n\t\thtml = EMPTY_STR,\n\t\tchildren;\n\n\tfor (let name in props) {\n\t\tlet v = props[name];\n\n\t\tif (typeof v == 'function' && name !== 'class' && name !== 'className') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tswitch (name) {\n\t\t\tcase 'children':\n\t\t\t\tchildren = v;\n\t\t\t\tcontinue;\n\n\t\t\t// VDOM-specific props\n\t\t\tcase 'key':\n\t\t\tcase 'ref':\n\t\t\tcase '__self':\n\t\t\tcase '__source':\n\t\t\t\tcontinue;\n\n\t\t\t// prefer for/class over htmlFor/className\n\t\t\tcase 'htmlFor':\n\t\t\t\tif ('for' in props) continue;\n\t\t\t\tname = 'for';\n\t\t\t\tbreak;\n\t\t\tcase 'className':\n\t\t\t\tif ('class' in props) continue;\n\t\t\t\tname = 'class';\n\t\t\t\tbreak;\n\n\t\t\t// Form element reflected properties\n\t\t\tcase 'defaultChecked':\n\t\t\t\tname = 'checked';\n\t\t\t\tbreak;\n\t\t\tcase 'defaultSelected':\n\t\t\t\tname = 'selected';\n\t\t\t\tbreak;\n\n\t\t\t// Special value attribute handling\n\t\t\tcase 'defaultValue':\n\t\t\tcase 'value':\n\t\t\t\tname = 'value';\n\t\t\t\tswitch (type) {\n\t\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\t\tcase 'textarea':\n\t\t\t\t\t\tchildren = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// <select value> is serialized as a selected attribute on the matching option child\n\t\t\t\t\tcase 'select':\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// Add a selected attribute to <option> if its value matches the parent <select> value\n\t\t\t\t\tcase 'option':\n\t\t\t\t\t\tif (selectValue == v && !('selected' in props)) {\n\t\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\tcase 'dangerouslySetInnerHTML':\n\t\t\t\thtml = v && v.__html;\n\t\t\t\tcontinue;\n\n\t\t\t// serialize object styles to a CSS string\n\t\t\tcase 'style':\n\t\t\t\tif (typeof v === 'object') {\n\t\t\t\t\tv = styleObjToCss(v);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'acceptCharset':\n\t\t\t\tname = 'accept-charset';\n\t\t\t\tbreak;\n\t\t\tcase 'httpEquiv':\n\t\t\t\tname = 'http-equiv';\n\t\t\t\tbreak;\n\n\t\t\tdefault: {\n\t\t\t\tif (NAMESPACE_REPLACE_REGEX.test(name)) {\n\t\t\t\t\tname = name.replace(NAMESPACE_REPLACE_REGEX, '$1:$2').toLowerCase();\n\t\t\t\t} else if (UNSAFE_NAME.test(name)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t} else if (\n\t\t\t\t\t(name[4] === '-' || HTML_ENUMERATED.has(name)) &&\n\t\t\t\t\tv != null\n\t\t\t\t) {\n\t\t\t\t\t// serialize boolean aria-xyz or enumerated attribute values as strings\n\t\t\t\t\tv = v + EMPTY_STR;\n\t\t\t\t} else if (isSvgMode) {\n\t\t\t\t\tif (SVG_CAMEL_CASE.test(name)) {\n\t\t\t\t\t\tname =\n\t\t\t\t\t\t\tname === 'panose1'\n\t\t\t\t\t\t\t\t? 'panose-1'\n\t\t\t\t\t\t\t\t: name.replace(/([A-Z])/g, '-$1').toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t} else if (HTML_LOWER_CASE.test(name)) {\n\t\t\t\t\tname = name.toLowerCase();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// write this attribute to the buffer\n\t\tif (v != null && v !== false) {\n\t\t\tif (v === true || v === EMPTY_STR) {\n\t\t\t\ts = s + ' ' + name;\n\t\t\t} else {\n\t\t\t\ts =\n\t\t\t\t\ts +\n\t\t\t\t\t' ' +\n\t\t\t\t\tname +\n\t\t\t\t\t'=\"' +\n\t\t\t\t\t(typeof v == 'string' ? encodeEntities(v) : v + EMPTY_STR) +\n\t\t\t\t\t'\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\t// this seems to performs a lot better than throwing\n\t\t// return '<!-- -->';\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}>`);\n\t}\n\n\tif (html) {\n\t\t// dangerouslySetInnerHTML defined this node's contents\n\t} else if (typeof children === 'string') {\n\t\t// single text child\n\t\thtml = encodeEntities(children);\n\t} else if (children != null && children !== false && children !== true) {\n\t\t// recurse into this element VNode's children\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\thtml = _renderToString(\n\t\t\tchildren,\n\t\t\tcontext,\n\t\t\tchildSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode,\n\t\t\tasyncMode,\n\t\t\trenderer\n\t\t);\n\t}\n\n\tif (afterDiff) afterDiff(vnode);\n\n\t// TODO: this was commented before\n\tvnode[PARENT] = null;\n\n\tif (ummountHook) ummountHook(vnode);\n\n\t// Emit self-closing tag for empty void elements:\n\tif (!html && SELF_CLOSING.has(type)) {\n\t\treturn s + '/>';\n\t}\n\n\tconst endTag = '</' + type + '>';\n\tconst startTag = s + '>';\n\n\tif (isArray(html)) return [startTag, ...html, endTag];\n\telse if (typeof html != 'string') return [startTag, html, endTag];\n\treturn startTag + html + endTag;\n}\n\nconst SELF_CLOSING = new Set([\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'command',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr'\n]);\n\nexport default renderToString;\nexport const render = renderToString;\nexport const renderToStaticMarkup = renderToString;\n", "/* eslint-disable no-var, key-spacing, object-curly-spacing, prefer-arrow-callback, semi, keyword-spacing */\n\n// function initPreactIslandElement() {\n// \tclass PreactIslandElement extends HTMLElement {\n// \t\tconnectedCallback() {\n// \t\t\tvar d = this;\n// \t\t\tif (!d.isConnected) return;\n\n// \t\t\tlet i = this.getAttribute('data-target');\n// \t\t\tif (!i) return;\n\n// \t\t\tvar s,\n// \t\t\t\te,\n// \t\t\t\tc = document.createNodeIterator(document, 128);\n// \t\t\twhile (c.nextNode()) {\n// \t\t\t\tlet n = c.referenceNode;\n\n// \t\t\t\tif (n.data == 'preact-island:' + i) s = n;\n// \t\t\t\telse if (n.data == '/preact-island:' + i) e = n;\n// \t\t\t\tif (s && e) break;\n// \t\t\t}\n// \t\t\tif (s && e) {\n// \t\t\t\trequestAnimationFrame(() => {\n// \t\t\t\t\tvar p = e.previousSibling;\n// \t\t\t\t\twhile (p != s) {\n// \t\t\t\t\t\tif (!p || p == s) break;\n// \t\t\t\t\t\te.parentNode.removeChild(p);\n// \t\t\t\t\t\tp = e.previousSibling;\n// \t\t\t\t\t}\n\n// \t\t\t\t\tc = s;\n// \t\t\t\t\twhile (d.firstChild) {\n// \t\t\t\t\t\ts = d.firstChild;\n// \t\t\t\t\t\td.removeChild(s);\n// \t\t\t\t\t\tc.after(s);\n// \t\t\t\t\t\tc = s;\n// \t\t\t\t\t}\n\n// \t\t\t\t\td.parentNode.removeChild(d);\n// \t\t\t\t});\n// \t\t\t}\n// \t\t}\n// \t}\n\n// \tcustomElements.define('preact-island', PreactIslandElement);\n// }\n\n// To modify the INIT_SCRIPT, uncomment the above code, modify it, and paste it into https://try.terser.org/.\nconst INIT_SCRIPT = `class e extends HTMLElement{connectedCallback(){var e=this;if(!e.isConnected)return;let t=this.getAttribute(\"data-target\");if(t){for(var r,a,i=document.createNodeIterator(document,128);i.nextNode();){let e=i.referenceNode;if(e.data==\"preact-island:\"+t?r=e:e.data==\"/preact-island:\"+t&&(a=e),r&&a)break}r&&a&&requestAnimationFrame((()=>{for(var t=a.previousSibling;t!=r&&t&&t!=r;)a.parentNode.removeChild(t),t=a.previousSibling;for(i=r;e.firstChild;)r=e.firstChild,e.removeChild(r),i.after(r),i=r;e.parentNode.removeChild(e)}))}}}customElements.define(\"preact-island\",e);`;\n\nexport function createInitScript() {\n\treturn `<script>(function(){${INIT_SCRIPT}}())</script>`;\n}\n\n/**\n * @param {string} id\n * @param {string} content\n * @returns {string}\n */\nexport function createSubtree(id, content) {\n\treturn `<preact-island hidden data-target=\"${id}\">${content}</preact-island>`;\n}\n", "import { renderToString } from '../index.js';\nimport { CHILD_DID_SUSPEND, COMPONENT, PARENT } from './constants.js';\nimport { Deferred } from './util.js';\nimport { createInitScript, createSubtree } from './client.js';\n\n/**\n * @param {VNode} vnode\n * @param {RenderToChunksOptions} options\n * @returns {Promise<void>}\n */\nexport async function renderToChunks(vnode, { context, onWrite, abortSignal }) {\n\tcontext = context || {};\n\n\t/** @type {RendererState} */\n\tconst renderer = {\n\t\tstart: Date.now(),\n\t\tabortSignal,\n\t\tonWrite,\n\t\tonError: handleError,\n\t\tsuspended: []\n\t};\n\n\t// Synchronously render the shell\n\t// @ts-ignore - using third internal RendererState argument\n\tconst shell = renderToString(vnode, context, renderer);\n\tonWrite(shell);\n\n\t// Wait for any suspended sub-trees if there are any\n\tconst len = renderer.suspended.length;\n\tif (len > 0) {\n\t\tonWrite('<div hidden>');\n\t\tonWrite(createInitScript(len));\n\t\t// We should keep checking all promises\n\t\tawait forkPromises(renderer);\n\t\tonWrite('</div>');\n\t}\n}\n\nasync function forkPromises(renderer) {\n\tif (renderer.suspended.length > 0) {\n\t\tconst suspensions = [...renderer.suspended];\n\t\tawait Promise.all(renderer.suspended.map((s) => s.promise));\n\t\trenderer.suspended = renderer.suspended.filter(\n\t\t\t(s) => !suspensions.includes(s)\n\t\t);\n\t\tawait forkPromises(renderer);\n\t}\n}\n\n/** @type {RendererErrorHandler} */\nfunction handleError(error, vnode, renderChild) {\n\tif (!error || !error.then) return;\n\n\t// walk up to the Suspense boundary\n\twhile ((vnode = vnode[PARENT])) {\n\t\tlet component = vnode[COMPONENT];\n\t\tif (component && component[CHILD_DID_SUSPEND]) {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\tif (!vnode) return;\n\n\tconst id = vnode.__v;\n\tconst found = this.suspended.find((x) => x.id === id);\n\tconst race = new Deferred();\n\n\tconst abortSignal = this.abortSignal;\n\tif (abortSignal) {\n\t\t// @ts-ignore 2554 - implicit undefined arg\n\t\tif (abortSignal.aborted) race.resolve();\n\t\telse abortSignal.addEventListener('abort', race.resolve);\n\t}\n\n\tconst promise = error.then(\n\t\t() => {\n\t\t\tif (abortSignal && abortSignal.aborted) return;\n\t\t\tconst child = renderChild(vnode.props.children);\n\t\t\tif (child) this.onWrite(createSubtree(id, child));\n\t\t},\n\t\t// TODO: Abort and send hydration code snippet to client\n\t\t// to attempt to recover during hydration\n\t\tthis.onError\n\t);\n\n\tthis.suspended.push({\n\t\tid,\n\t\tvnode,\n\t\tpromise: Promise.race([promise, race.promise])\n\t});\n\n\tconst fallback = renderChild(vnode.props.fallback);\n\n\treturn found\n\t\t? ''\n\t\t: `<!--preact-island:${id}-->${fallback}<!--/preact-island:${id}-->`;\n}\n", "import { PassThrough } from 'node:stream';\nimport { renderToChunks } from './lib/chunked.js';\n\n/**\n * @typedef {object} RenderToPipeableStreamOptions\n * @property {() => void} [onShellReady]\n * @property {() => void} [onAllReady]\n * @property {(error) => void} [onError]\n */\n\n/**\n * @typedef {object} PipeableStream\n * @property {() => void} abort\n * @property {(writable: import('stream').Writable) => void} pipe\n */\n\n/**\n * @param {import('preact').VNode} vnode\n * @param {RenderToPipeableStreamOptions} options\n * @param {any} [context]\n * @returns {PipeableStream}\n */\nexport function renderToPipeableStream(vnode, options, context) {\n\tconst encoder = new TextEncoder('utf-8');\n\n\tconst controller = new AbortController();\n\tconst stream = new PassThrough();\n\n\trenderToChunks(vnode, {\n\t\tcontext,\n\t\tabortSignal: controller.signal,\n\t\tonError: (error) => {\n\t\t\tif (options.onError) {\n\t\t\t\toptions.onError(error);\n\t\t\t}\n\t\t\tcontroller.abort(error);\n\t\t},\n\t\tonWrite(s) {\n\t\t\tstream.write(encoder.encode(s));\n\t\t}\n\t})\n\t\t.then(() => {\n\t\t\toptions.onAllReady && options.onAllReady();\n\t\t\tstream.end();\n\t\t})\n\t\t.catch((error) => {\n\t\t\tstream.destroy(error);\n\t\t});\n\n\tPromise.resolve().then(() => {\n\t\toptions.onShellReady && options.onShellReady();\n\t});\n\n\treturn {\n\t\tabort() {\n\t\t\tcontroller.abort();\n\t\t\tstream.destroy(new Error('aborted'));\n\t\t},\n\t\t/**\n\t\t * @param {import(\"stream\").Writable} writable\n\t\t */\n\t\tpipe(writable) {\n\t\t\tstream.pipe(writable, { end: true });\n\t\t}\n\t};\n}\n"], "names": ["UNSAFE_NAME", "NAMESPACE_REPLACE_REGEX", "HTML_LOWER_CASE", "SVG_CAMEL_CASE", "HTML_ENUMERATED", "Set", "ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "JS_TO_CSS", "IS_NON_DIMENSIONAL", "CSS_REGEX", "styleObjToCss", "s", "prop", "val", "name", "replace", "toLowerCase", "suffix", "startsWith", "has", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "Array", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "DIFF", "RENDER", "DIFFED", "COMMIT", "SKIP_EFFECTS", "CATCH_ERROR", "COMPONENT", "CHILDREN", "PARENT", "VNODE", "DIRTY", "NEXT_STATE", "CHILD_DID_SUSPEND", "EMPTY_OBJ", "EMPTY_ARR", "isArray", "assign", "Object", "EMPTY_STR", "beforeDiff", "afterDiff", "renderHook", "ummountHook", "renderToString", "_rendererState", "previousSkipEffects", "options", "unmount", "parent", "h", "Fragment", "rendered", "_renderToString", "join", "e", "then", "Error", "renderClassComponent", "type", "isMounting", "c", "state", "getDerivedStateFromProps", "componentWillMount", "componentWillUpdate", "render", "isSvgMode", "selectValue", "asyncMode", "renderer", "vnodeType", "renderArray", "child", "childRender", "push", "cctx", "contextType", "component", "tpl", "exprs", "value", "UNSTABLE_comment", "children", "provider", "__c", "__", "isClassComponent", "prototype", "count", "call", "getChildContext", "errorBoundaries", "getDerivedStateFromError", "componentDidCatch", "isTopLevelFragment", "key", "err", "error", "onError", "res", "errorHook", "renderNestedChildren", "html", "v", "__html", "childSvgMode", "SELF_CLOSING", "endTag", "startTag", "INIT_SCRIPT", "createInitScript", "createSubtree", "id", "content", "renderToChunks", "onWrite", "abortSignal", "start", "Date", "now", "handleError", "suspended", "shell", "len", "forkPromises", "suspensions", "all", "map", "filter", "includes", "<PERSON><PERSON><PERSON><PERSON>", "found", "find", "x", "race", "aborted", "addEventListener", "fallback", "renderToPipeableStream", "encoder", "TextEncoder", "controller", "AbortController", "stream", "PassThrough", "signal", "abort", "write", "encode", "onAllReady", "end", "catch", "destroy", "onShellReady", "pipe", "writable"], "mappings": ";;;AACO,MAAMA,WAAW,GAAG,kBAApB;AACA,MAAMC,uBAAuB,GAAG,2BAAhC;AACA,MAAMC,eAAe,GAAG,6JAAxB;AACA,MAAMC,cAAc,GAAG,wQAAvB;;AAGA,MAAMC,eAAe,GAAG,IAAIC,GAAJ,CAAQ,CAAC,WAAD,EAAc,YAAd,CAAR,CAAxB;;AAGP,MAAMC,gBAAgB,GAAG,OAAzB;AAEA;;AACO,SAASC,cAAT,CAAwBC,GAAxB,EAA6B;AACnC;AACA,MAAIA,GAAG,CAACC,MAAJ,KAAe,CAAf,IAAoBH,gBAAgB,CAACI,IAAjB,CAAsBF,GAAtB,MAA+B,KAAvD,EAA8D,OAAOA,GAAP;AAE9D,MAAIG,IAAI,GAAG,CAAX;AAAA,MACCC,CAAC,GAAG,CADL;AAAA,MAECC,GAAG,GAAG,EAFP;AAAA,MAGCC,EAAE,GAAG,EAHN,CAJmC;;AAUnC,SAAOF,CAAC,GAAGJ,GAAG,CAACC,MAAf,EAAuBG,CAAC,EAAxB,EAA4B;AAC3B,YAAQJ,GAAG,CAACO,UAAJ,CAAeH,CAAf,CAAR;AACC,WAAK,EAAL;AACCE,QAAAA,EAAE,GAAG,QAAL;AACA;;AACD,WAAK,EAAL;AACCA,QAAAA,EAAE,GAAG,OAAL;AACA;;AACD,WAAK,EAAL;AACCA,QAAAA,EAAE,GAAG,MAAL;AACA;;AACD;AACC;AAXF,KAD2B;;;AAe3B,QAAIF,CAAC,KAAKD,IAAV,EAAgBE,GAAG,GAAGA,GAAG,GAAGL,GAAG,CAACQ,KAAJ,CAAUL,IAAV,EAAgBC,CAAhB,CAAZ;AAChBC,IAAAA,GAAG,GAAGA,GAAG,GAAGC,EAAZ,CAhB2B;;AAkB3BH,IAAAA,IAAI,GAAGC,CAAC,GAAG,CAAX;AACA;;AACD,MAAIA,CAAC,KAAKD,IAAV,EAAgBE,GAAG,GAAGA,GAAG,GAAGL,GAAG,CAACQ,KAAJ,CAAUL,IAAV,EAAgBC,CAAhB,CAAZ;AAChB,SAAOC,GAAP;AACA;AAUD,MAAMI,SAAS,GAAG,EAAlB;AAEA,MAAMC,kBAAkB,GAAG,IAAIb,GAAJ,CAAQ,CAClC,2BADkC,EAElC,qBAFkC,EAGlC,oBAHkC,EAIlC,oBAJkC,EAKlC,UALkC,EAMlC,gBANkC,EAOlC,mBAPkC,EAQlC,cARkC,EASlC,cATkC,EAUlC,MAVkC,EAWlC,WAXkC,EAYlC,eAZkC,EAalC,YAbkC,EAclC,eAdkC,EAelC,aAfkC,EAgBlC,eAhBkC,EAiBlC,aAjBkC,EAkBlC,aAlBkC,EAmBlC,UAnBkC,EAoBlC,YApBkC,EAqBlC,aArBkC,EAsBlC,SAtBkC,EAuBlC,OAvBkC,EAwBlC,SAxBkC,EAyBlC,cAzBkC,EA0BlC,kBA1BkC,EA2BlC,mBA3BkC,EA4BlC,mBA5BkC,EA6BlC,gBA7BkC,EA8BlC,cA9BkC,EA+BlC,UA/BkC,EAgClC,QAhCkC,EAiClC,SAjCkC,EAkClC,MAlCkC,CAAR,CAA3B;AAqCA,MAAMc,SAAS,GAAG,QAAlB;;AAEO,SAASC,aAAT,CAAuBC,CAAvB,EAA0B;AAChC,MAAIb,GAAG,GAAG,EAAV;;AACA,OAAK,IAAIc,IAAT,IAAiBD,CAAjB,EAAoB;AACnB,QAAIE,GAAG,GAAGF,CAAC,CAACC,IAAD,CAAX;;AACA,QAAIC,GAAG,IAAI,IAAP,IAAeA,GAAG,KAAK,EAA3B,EAA+B;AAC9B,YAAMC,IAAI,GACTF,IAAI,CAAC,CAAD,CAAJ,IAAW,GAAX,GACGA,IADH,GAEGL,SAAS,CAACK,IAAD,CAAT,KACCL,SAAS,CAACK,IAAD,CAAT,GAAkBA,IAAI,CAACG,OAAL,CAAaN,SAAb,EAAwB,KAAxB,EAA+BO,WAA/B,EADnB,CAHJ;AAMA,UAAIC,MAAM,GAAG,GAAb;;AACA,UACC,OAAOJ,GAAP,KAAe,QAAf;AAEA,OAACC,IAAI,CAACI,UAAL,CAAgB,IAAhB,CAFD,IAGA,CAACV,kBAAkB,CAACW,GAAnB,CAAuBL,IAAvB,CAJF,EAKE;AACDG,QAAAA,MAAM,GAAG,KAAT;AACA;;AACDnB,MAAAA,GAAG,GAAGA,GAAG,GAAGgB,IAAN,GAAa,GAAb,GAAmBD,GAAnB,GAAyBI,MAA/B;AACA;AACD;;AACD,SAAOnB,GAAG,IAAIsB,SAAd;AACA;;AAkBD,SAASC,WAAT,GAAuB;AACtB,OAAKC,GAAL,GAAW,IAAX;AACA;;AAEM,SAASC,eAAT,CAAyBC,KAAzB,EAAgCC,OAAhC,EAAyC;AAC/C,SAAO;AACNC,IAAAA,GAAG,EAAEF,KADC;AAENC,IAAAA,OAFM;AAGNE,IAAAA,KAAK,EAAEH,KAAK,CAACG,KAHP;AAIN;AACAC,IAAAA,QAAQ,EAAEP,WALJ;AAMNQ,IAAAA,WAAW,EAAER,WANP;AAONC,IAAAA,GAAG,EAAE,IAPC;AAQN;AACAQ,IAAAA,GAAG,EAAE,IAAIC,KAAJ,CAAU,CAAV;AATC,GAAP;AAWA;AAcD;AACA;AACA;;AACO,MAAMC,QAAN,CAAe;AACrBC,EAAAA,WAAW,GAAG;AACb;;AACA;AACA,SAAKC,OAAL,GAAe,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AAC/C,WAAKD,OAAL,GAAeA,OAAf;AACA,WAAKC,MAAL,GAAcA,MAAd;AACA,KAHc,CAAf;AAIA;;AARoB;;AC3KtB;AACO,MAAMC,IAAI,GAAG,KAAb;AACA,MAAMC,MAAM,GAAG,KAAf;AACA,MAAMC,MAAM,GAAG,QAAf;AACA,MAAMC,MAAM,GAAG,KAAf;AACA,MAAMC,YAAY,GAAG,KAArB;AACA,MAAMC,WAAW,GAAG,KAApB;;AAGA,MAAMC,SAAS,GAAG,KAAlB;AACA,MAAMC,QAAQ,GAAG,KAAjB;AACA,MAAMC,MAAM,GAAG,IAAf;;AAIA,MAAMC,KAAK,GAAG,KAAd;AACA,MAAMC,KAAK,GAAG,KAAd;AACA,MAAMC,UAAU,GAAG,KAAnB;AACA,MAAMC,iBAAiB,GAAG,KAA1B;;ACQP,MAAMC,SAAS,GAAG,EAAlB;AACA,MAAMC,SAAS,GAAG,EAAlB;AACA,MAAMC,OAAO,GAAGtB,KAAK,CAACsB,OAAtB;AACA,MAAMC,MAAM,GAAGC,MAAM,CAACD,MAAtB;AACA,MAAME,SAAS,GAAG,EAAlB;;AAGA,IAAIC,UAAJ,EAAgBC,SAAhB,EAA2BC,UAA3B,EAAuCC,WAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,cAAT,CAAwBrC,KAAxB,EAA+BC,OAA/B,EAAwCqC,cAAxC,EAAwD;AAC9D;AACA;AACA;AACA;AACA;AACA,QAAMC,mBAAmB,GAAGC,OAAO,CAACtB,YAAD,CAAnC;AACAsB,EAAAA,OAAO,CAACtB,YAAD,CAAP,GAAwB,IAAxB,CAP8D;;AAU9De,EAAAA,UAAU,GAAGO,OAAO,CAAC1B,IAAD,CAApB;AACAoB,EAAAA,SAAS,GAAGM,OAAO,CAACxB,MAAD,CAAnB;AACAmB,EAAAA,UAAU,GAAGK,OAAO,CAACzB,MAAD,CAApB;AACAqB,EAAAA,WAAW,GAAGI,OAAO,CAACC,OAAtB;AAEA,QAAMC,MAAM,GAAGC,CAAC,CAACC,QAAD,EAAW,IAAX,CAAhB;AACAF,EAAAA,MAAM,CAACrB,QAAD,CAAN,GAAmB,CAACrB,KAAD,CAAnB;;AAEA,MAAI;AACH,UAAM6C,QAAQ,GAAGC,eAAe,CAC/B9C,KAD+B,EAE/BC,OAAO,IAAI0B,SAFoB,EAG/B,KAH+B,EAI/B/B,SAJ+B,EAK/B8C,MAL+B,EAM/B,KAN+B,EAO/BJ,cAP+B,CAAhC;;AAUA,QAAIT,OAAO,CAACgB,QAAD,CAAX,EAAuB;AACtB,aAAOA,QAAQ,CAACE,IAAT,CAAcf,SAAd,CAAP;AACA;;AACD,WAAOa,QAAP;AACA,GAfD,CAeE,OAAOG,CAAP,EAAU;AACX,QAAIA,CAAC,CAACC,IAAN,EAAY;AACX,YAAM,IAAIC,KAAJ,CAAU,sDAAV,CAAN;AACA;;AAED,UAAMF,CAAN;AACA,GArBD,SAqBU;AACT;AACA;AACA,QAAIR,OAAO,CAACvB,MAAD,CAAX,EAAqBuB,OAAO,CAACvB,MAAD,CAAP,CAAgBjB,KAAhB,EAAuB4B,SAAvB;AACrBY,IAAAA,OAAO,CAACtB,YAAD,CAAP,GAAwBqB,mBAAxB;AACAX,IAAAA,SAAS,CAACrD,MAAV,GAAmB,CAAnB;AACA;AACD;AAgED;AACA;AACA;AACA;;AACA,SAAS4E,oBAAT,CAA8BnD,KAA9B,EAAqCC,OAArC,EAA8C;AAC7C,MAAImD,IAAI;AAAG;AAAoEpD,EAAAA,KAAK,CAACoD,IAArF;AAEA,MAAIC,UAAU,GAAG,IAAjB;AACA,MAAIC,CAAJ;;AACA,MAAItD,KAAK,CAACoB,SAAD,CAAT,EAAsB;AACrBiC,IAAAA,UAAU,GAAG,KAAb;AACAC,IAAAA,CAAC,GAAGtD,KAAK,CAACoB,SAAD,CAAT;AACAkC,IAAAA,CAAC,CAACC,KAAF,GAAUD,CAAC,CAAC7B,UAAD,CAAX;AACA,GAJD,MAIO;AACN6B,IAAAA,CAAC,GAAG,IAAIF,IAAJ,CAASpD,KAAK,CAACG,KAAf,EAAsBF,OAAtB,CAAJ;AACA;;AAEDD,EAAAA,KAAK,CAACoB,SAAD,CAAL,GAAmBkC,CAAnB;AACAA,EAAAA,CAAC,CAAC/B,KAAD,CAAD,GAAWvB,KAAX;AAEAsD,EAAAA,CAAC,CAACnD,KAAF,GAAUH,KAAK,CAACG,KAAhB;AACAmD,EAAAA,CAAC,CAACrD,OAAF,GAAYA,OAAZ,CAjB6C;;AAmB7CqD,EAAAA,CAAC,CAAC9B,KAAD,CAAD,GAAW,IAAX;AAEA,MAAI8B,CAAC,CAACC,KAAF,IAAW,IAAf,EAAqBD,CAAC,CAACC,KAAF,GAAU5B,SAAV;;AAErB,MAAI2B,CAAC,CAAC7B,UAAD,CAAD,IAAiB,IAArB,EAA2B;AAC1B6B,IAAAA,CAAC,CAAC7B,UAAD,CAAD,GAAgB6B,CAAC,CAACC,KAAlB;AACA;;AAED,MAAIH,IAAI,CAACI,wBAAT,EAAmC;AAClCF,IAAAA,CAAC,CAACC,KAAF,GAAUzB,MAAM,CACf,EADe,EAEfwB,CAAC,CAACC,KAFa,EAGfH,IAAI,CAACI,wBAAL,CAA8BF,CAAC,CAACnD,KAAhC,EAAuCmD,CAAC,CAACC,KAAzC,CAHe,CAAhB;AAKA,GAND,MAMO,IAAIF,UAAU,IAAIC,CAAC,CAACG,kBAApB,EAAwC;AAC9CH,IAAAA,CAAC,CAACG,kBAAF,GAD8C;AAI9C;;AACAH,IAAAA,CAAC,CAACC,KAAF,GAAUD,CAAC,CAAC7B,UAAD,CAAD,KAAkB6B,CAAC,CAACC,KAApB,GAA4BD,CAAC,CAAC7B,UAAD,CAA7B,GAA4C6B,CAAC,CAACC,KAAxD;AACA,GANM,MAMA,IAAI,CAACF,UAAD,IAAeC,CAAC,CAACI,mBAArB,EAA0C;AAChDJ,IAAAA,CAAC,CAACI,mBAAF;AACA;;AAED,MAAIvB,UAAJ,EAAgBA,UAAU,CAACnC,KAAD,CAAV;AAEhB,SAAOsD,CAAC,CAACK,MAAF,CAASL,CAAC,CAACnD,KAAX,EAAkBmD,CAAC,CAACC,KAApB,EAA2BtD,OAA3B,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS6C,eAAT,CACC9C,KADD,EAECC,OAFD,EAGC2D,SAHD,EAICC,WAJD,EAKCnB,MALD,EAMCoB,SAND,EAOCC,QAPD,EAQE;AACD;AACA,MACC/D,KAAK,IAAI,IAAT,IACAA,KAAK,KAAK,IADV,IAEAA,KAAK,KAAK,KAFV,IAGAA,KAAK,KAAKgC,SAJX,EAKE;AACD,WAAOA,SAAP;AACA;;AAED,MAAIgC,SAAS,GAAG,OAAOhE,KAAvB,CAXC;;AAaD,MAAIgE,SAAS,IAAI,QAAjB,EAA2B;AAC1B,QAAIA,SAAS,IAAI,UAAjB,EAA6B,OAAOhC,SAAP;AAC7B,WAAOgC,SAAS,IAAI,QAAb,GAAwB3F,cAAc,CAAC2B,KAAD,CAAtC,GAAgDA,KAAK,GAAGgC,SAA/D;AACA,GAhBA;;;AAmBD,MAAIH,OAAO,CAAC7B,KAAD,CAAX,EAAoB;AACnB,QAAI6C,QAAQ,GAAGb,SAAf;AAAA,QACCiC,WADD;AAEAvB,IAAAA,MAAM,CAACrB,QAAD,CAAN,GAAmBrB,KAAnB;;AACA,SAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsB,KAAK,CAACzB,MAA1B,EAAkCG,CAAC,EAAnC,EAAuC;AACtC,UAAIwF,KAAK,GAAGlE,KAAK,CAACtB,CAAD,CAAjB;AACA,UAAIwF,KAAK,IAAI,IAAT,IAAiB,OAAOA,KAAP,IAAgB,SAArC,EAAgD;;AAEhD,YAAMC,WAAW,GAAGrB,eAAe,CAClCoB,KADkC,EAElCjE,OAFkC,EAGlC2D,SAHkC,EAIlCC,WAJkC,EAKlCnB,MALkC,EAMlCoB,SANkC,EAOlCC,QAPkC,CAAnC;;AAUA,UAAI,OAAOI,WAAP,IAAsB,QAA1B,EAAoC;AACnCtB,QAAAA,QAAQ,GAAGA,QAAQ,GAAGsB,WAAtB;AACA,OAFD,MAEO;AACN,YAAI,CAACF,WAAL,EAAkB;AACjBA,UAAAA,WAAW,GAAG,EAAd;AACA;;AAED,YAAIpB,QAAJ,EAAcoB,WAAW,CAACG,IAAZ,CAAiBvB,QAAjB;AAEdA,QAAAA,QAAQ,GAAGb,SAAX;;AAEA,YAAIH,OAAO,CAACsC,WAAD,CAAX,EAA0B;AACzBF,UAAAA,WAAW,CAACG,IAAZ,CAAiB,GAAGD,WAApB;AACA,SAFD,MAEO;AACNF,UAAAA,WAAW,CAACG,IAAZ,CAAiBD,WAAjB;AACA;AACD;AACD;;AAED,QAAIF,WAAJ,EAAiB;AAChB,UAAIpB,QAAJ,EAAcoB,WAAW,CAACG,IAAZ,CAAiBvB,QAAjB;AACd,aAAOoB,WAAP;AACA;;AAED,WAAOpB,QAAP;AACA,GA9DA;;;AAiED,MAAI7C,KAAK,CAACS,WAAN,KAAsBb,SAA1B,EAAqC,OAAOoC,SAAP;AAErChC,EAAAA,KAAK,CAACsB,MAAD,CAAL,GAAgBoB,MAAhB;AACA,MAAIT,UAAJ,EAAgBA,UAAU,CAACjC,KAAD,CAAV;AAEhB,MAAIoD,IAAI,GAAGpD,KAAK,CAACoD,IAAjB;AAAA,MACCjD,KAAK,GAAGH,KAAK,CAACG,KADf,CAtEC;;AA0ED,MAAI,OAAOiD,IAAP,IAAe,UAAnB,EAA+B;AAC9B,QAAIiB,IAAI,GAAGpE,OAAX;AAAA,QACCqE,WADD;AAAA,QAECzB,QAFD;AAAA,QAGC0B,SAHD;;AAIA,QAAInB,IAAI,KAAKR,QAAb,EAAuB;AACtB;AACA,UAAI,SAASzC,KAAb,EAAoB;AACnB,YAAIxB,GAAG,GAAGqD,SAAV;;AACA,aAAK,IAAItD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyB,KAAK,CAACqE,GAAN,CAAUjG,MAA9B,EAAsCG,CAAC,EAAvC,EAA2C;AAC1CC,UAAAA,GAAG,GAAGA,GAAG,GAAGwB,KAAK,CAACqE,GAAN,CAAU9F,CAAV,CAAZ;;AAEA,cAAIyB,KAAK,CAACsE,KAAN,IAAe/F,CAAC,GAAGyB,KAAK,CAACsE,KAAN,CAAYlG,MAAnC,EAA2C;AAC1C,kBAAMmG,KAAK,GAAGvE,KAAK,CAACsE,KAAN,CAAY/F,CAAZ,CAAd;AACA,gBAAIgG,KAAK,IAAI,IAAb,EAAmB,SAFuB;;AAK1C,gBACC,OAAOA,KAAP,IAAgB,QAAhB,KACCA,KAAK,CAACjE,WAAN,KAAsBb,SAAtB,IAAmCiC,OAAO,CAAC6C,KAAD,CAD3C,CADD,EAGE;AACD/F,cAAAA,GAAG,GACFA,GAAG,GACHmE,eAAe,CACd4B,KADc,EAEdzE,OAFc,EAGd2D,SAHc,EAIdC,WAJc,EAKd7D,KALc,EAMd8D,SANc,EAOdC,QAPc,CAFhB;AAWA,aAfD,MAeO;AACN;AACApF,cAAAA,GAAG,GAAGA,GAAG,GAAG+F,KAAZ;AACA;AACD;AACD;;AAED,eAAO/F,GAAP;AACA,OAjCD,MAiCO,IAAI,sBAAsBwB,KAA1B,EAAiC;AACvC;AACA;AACA,eAAO,SAAS9B,cAAc,CAAC8B,KAAK,CAACwE,gBAAP,CAAvB,GAAkD,KAAzD;AACA;;AAED9B,MAAAA,QAAQ,GAAG1C,KAAK,CAACyE,QAAjB;AACA,KA1CD,MA0CO;AACNN,MAAAA,WAAW,GAAGlB,IAAI,CAACkB,WAAnB;;AACA,UAAIA,WAAW,IAAI,IAAnB,EAAyB;AACxB,YAAIO,QAAQ,GAAG5E,OAAO,CAACqE,WAAW,CAACQ,GAAb,CAAtB;AACAT,QAAAA,IAAI,GAAGQ,QAAQ,GAAGA,QAAQ,CAAC1E,KAAT,CAAeuE,KAAlB,GAA0BJ,WAAW,CAACS,EAArD;AACA;;AAED,UAAIC,gBAAgB,GACnB5B,IAAI,CAAC6B,SAAL,IAAkB,OAAO7B,IAAI,CAAC6B,SAAL,CAAetB,MAAtB,IAAgC,UADnD;;AAEA,UAAIqB,gBAAJ,EAAsB;AACrBnC,QAAAA,QAAQ;AAAG;AAAoBM,QAAAA,oBAAoB,CAACnD,KAAD,EAAQqE,IAAR,CAAnD;AACAE,QAAAA,SAAS,GAAGvE,KAAK,CAACoB,SAAD,CAAjB;AACA,OAHD,MAGO;AACNpB,QAAAA,KAAK,CAACoB,SAAD,CAAL,GAAmBmD,SAAS;AAAG;AAAoBxE,QAAAA,eAAe,CACjEC,KADiE,EAEjEqE,IAFiE,CAAlE,CADM;AAON;AACA;AACA;AACA;;AACA,YAAIa,KAAK,GAAG,CAAZ;;AACA,eAAOX,SAAS,CAAC/C,KAAD,CAAT,IAAoB0D,KAAK,KAAK,EAArC,EAAyC;AACxCX,UAAAA,SAAS,CAAC/C,KAAD,CAAT,GAAmB,KAAnB;AAEA,cAAIW,UAAJ,EAAgBA,UAAU,CAACnC,KAAD,CAAV;AAEhB6C,UAAAA,QAAQ,GAAGO,IAAI,CAAC+B,IAAL,CAAUZ,SAAV,EAAqBpE,KAArB,EAA4BkE,IAA5B,CAAX;AACA;;AACDE,QAAAA,SAAS,CAAC/C,KAAD,CAAT,GAAmB,IAAnB;AACA;;AAED,UAAI+C,SAAS,CAACa,eAAV,IAA6B,IAAjC,EAAuC;AACtCnF,QAAAA,OAAO,GAAG6B,MAAM,CAAC,EAAD,EAAK7B,OAAL,EAAcsE,SAAS,CAACa,eAAV,EAAd,CAAhB;AACA;;AAED,UACCJ,gBAAgB,IAChBxC,OAAO,CAAC6C,eADR,KAECjC,IAAI,CAACkC,wBAAL,IAAiCf,SAAS,CAACgB,iBAF5C,CADD,EAIE;AACD;AACA;AACA,YAAIC,kBAAkB,GACrB3C,QAAQ,IAAI,IAAZ,IACAA,QAAQ,CAACO,IAAT,KAAkBR,QADlB,IAEAC,QAAQ,CAAC4C,GAAT,IAAgB,IAFhB,IAGA5C,QAAQ,CAAC1C,KAAT,CAAeqE,GAAf,IAAsB,IAJvB;AAKA3B,QAAAA,QAAQ,GAAG2C,kBAAkB,GAAG3C,QAAQ,CAAC1C,KAAT,CAAeyE,QAAlB,GAA6B/B,QAA1D;;AAEA,YAAI;AACH,iBAAOC,eAAe,CACrBD,QADqB,EAErB5C,OAFqB,EAGrB2D,SAHqB,EAIrBC,WAJqB,EAKrB7D,KALqB,EAMrB8D,SANqB,EAOrBC,QAPqB,CAAtB;AASA,SAVD,CAUE,OAAO2B,GAAP,EAAY;AACb,cAAItC,IAAI,CAACkC,wBAAT,EAAmC;AAClCf,YAAAA,SAAS,CAAC9C,UAAD,CAAT,GAAwB2B,IAAI,CAACkC,wBAAL,CAA8BI,GAA9B,CAAxB;AACA;;AAED,cAAInB,SAAS,CAACgB,iBAAd,EAAiC;AAChChB,YAAAA,SAAS,CAACgB,iBAAV,CAA4BG,GAA5B,EAAiC/D,SAAjC;AACA;;AAED,cAAI4C,SAAS,CAAC/C,KAAD,CAAb,EAAsB;AACrBqB,YAAAA,QAAQ,GAAGM,oBAAoB,CAACnD,KAAD,EAAQC,OAAR,CAA/B;AACAsE,YAAAA,SAAS,GAAGvE,KAAK,CAACoB,SAAD,CAAjB;;AAEA,gBAAImD,SAAS,CAACa,eAAV,IAA6B,IAAjC,EAAuC;AACtCnF,cAAAA,OAAO,GAAG6B,MAAM,CAAC,EAAD,EAAK7B,OAAL,EAAcsE,SAAS,CAACa,eAAV,EAAd,CAAhB;AACA;;AAED,gBAAII,kBAAkB,GACrB3C,QAAQ,IAAI,IAAZ,IACAA,QAAQ,CAACO,IAAT,KAAkBR,QADlB,IAEAC,QAAQ,CAAC4C,GAAT,IAAgB,IAFhB,IAGA5C,QAAQ,CAAC1C,KAAT,CAAeqE,GAAf,IAAsB,IAJvB;AAKA3B,YAAAA,QAAQ,GAAG2C,kBAAkB,GAAG3C,QAAQ,CAAC1C,KAAT,CAAeyE,QAAlB,GAA6B/B,QAA1D;AAEA,mBAAOC,eAAe,CACrBD,QADqB,EAErB5C,OAFqB,EAGrB2D,SAHqB,EAIrBC,WAJqB,EAKrB7D,KALqB,EAMrB8D,SANqB,EAOrBC,QAPqB,CAAtB;AASA;;AAED,iBAAO/B,SAAP;AACA,SA9CD,SA8CU;AACT,cAAIE,SAAJ,EAAeA,SAAS,CAAClC,KAAD,CAAT;AACfA,UAAAA,KAAK,CAACsB,MAAD,CAAL,GAAgB,IAAhB;AAEA,cAAIc,WAAJ,EAAiBA,WAAW,CAACpC,KAAD,CAAX;AACjB;AACD;AACD,KAxJ6B;AA2J9B;;;AACA,QAAIwF,kBAAkB,GACrB3C,QAAQ,IAAI,IAAZ,IACAA,QAAQ,CAACO,IAAT,KAAkBR,QADlB,IAEAC,QAAQ,CAAC4C,GAAT,IAAgB,IAFhB,IAGA5C,QAAQ,CAAC1C,KAAT,CAAeqE,GAAf,IAAsB,IAJvB;AAKA3B,IAAAA,QAAQ,GAAG2C,kBAAkB,GAAG3C,QAAQ,CAAC1C,KAAT,CAAeyE,QAAlB,GAA6B/B,QAA1D;;AAEA,QAAI;AACH;AACA,YAAMvE,GAAG,GAAGwE,eAAe,CAC1BD,QAD0B,EAE1B5C,OAF0B,EAG1B2D,SAH0B,EAI1BC,WAJ0B,EAK1B7D,KAL0B,EAM1B8D,SAN0B,EAO1BC,QAP0B,CAA3B;;AAUA,UAAI7B,SAAJ,EAAeA,SAAS,CAAClC,KAAD,CAAT,CAZZ;;AAcHA,MAAAA,KAAK,CAACsB,MAAD,CAAL,GAAgB,IAAhB;AAEA,UAAIkB,OAAO,CAACC,OAAZ,EAAqBD,OAAO,CAACC,OAAR,CAAgBzC,KAAhB;AAErB,aAAO1B,GAAP;AACA,KAnBD,CAmBE,OAAOqH,KAAP,EAAc;AACf,UAAI,CAAC7B,SAAD,IAAcC,QAAd,IAA0BA,QAAQ,CAAC6B,OAAvC,EAAgD;AAC/C,YAAIC,GAAG,GAAG9B,QAAQ,CAAC6B,OAAT,CAAiBD,KAAjB,EAAwB3F,KAAxB,EAAgCkE,KAAD,IACxCpB,eAAe,CACdoB,KADc,EAEdjE,OAFc,EAGd2D,SAHc,EAIdC,WAJc,EAKd7D,KALc,EAMd8D,SANc,EAOdC,QAPc,CADN,CAAV;AAYA,YAAI8B,GAAG,KAAKjG,SAAZ,EAAuB,OAAOiG,GAAP;AAEvB,YAAIC,SAAS,GAAGtD,OAAO,CAACrB,WAAD,CAAvB;AACA,YAAI2E,SAAJ,EAAeA,SAAS,CAACH,KAAD,EAAQ3F,KAAR,CAAT;AACf,eAAOgC,SAAP;AACA;;AAED,UAAI,CAAC8B,SAAL,EAAgB,MAAM6B,KAAN;AAEhB,UAAI,CAACA,KAAD,IAAU,OAAOA,KAAK,CAAC1C,IAAb,IAAqB,UAAnC,EAA+C,MAAM0C,KAAN;;AAE/C,YAAMI,oBAAoB,GAAG,MAAM;AAClC,YAAI;AACH,iBAAOjD,eAAe,CACrBD,QADqB,EAErB5C,OAFqB,EAGrB2D,SAHqB,EAIrBC,WAJqB,EAKrB7D,KALqB,EAMrB8D,SANqB,EAOrBC,QAPqB,CAAtB;AASA,SAVD,CAUE,OAAOf,CAAP,EAAU;AACX,cAAI,CAACA,CAAD,IAAM,OAAOA,CAAC,CAACC,IAAT,IAAiB,UAA3B,EAAuC,MAAMD,CAAN;AAEvC,iBAAOA,CAAC,CAACC,IAAF,CACN,MACCH,eAAe,CACdD,QADc,EAEd5C,OAFc,EAGd2D,SAHc,EAIdC,WAJc,EAKd7D,KALc,EAMd8D,SANc,EAOdC,QAPc,CAFV,EAWNgC,oBAXM,CAAP;AAaA;AACD,OA5BD;;AA8BA,aAAOJ,KAAK,CAAC1C,IAAN,CAAW8C,oBAAX,CAAP;AACA;AACD,GAzTA;;;AA4TD,MAAI5G,CAAC,GAAG,MAAMiE,IAAd;AAAA,MACC4C,IAAI,GAAGhE,SADR;AAAA,MAEC4C,QAFD;;AAIA,OAAK,IAAItF,IAAT,IAAiBa,KAAjB,EAAwB;AACvB,QAAI8F,CAAC,GAAG9F,KAAK,CAACb,IAAD,CAAb;;AAEA,QAAI,OAAO2G,CAAP,IAAY,UAAZ,IAA0B3G,IAAI,KAAK,OAAnC,IAA8CA,IAAI,KAAK,WAA3D,EAAwE;AACvE;AACA;;AAED,YAAQA,IAAR;AACC,WAAK,UAAL;AACCsF,QAAAA,QAAQ,GAAGqB,CAAX;AACA;AAED;;AACA,WAAK,KAAL;AACA,WAAK,KAAL;AACA,WAAK,QAAL;AACA,WAAK,UAAL;AACC;AAED;;AACA,WAAK,SAAL;AACC,YAAI,SAAS9F,KAAb,EAAoB;AACpBb,QAAAA,IAAI,GAAG,KAAP;AACA;;AACD,WAAK,WAAL;AACC,YAAI,WAAWa,KAAf,EAAsB;AACtBb,QAAAA,IAAI,GAAG,OAAP;AACA;AAED;;AACA,WAAK,gBAAL;AACCA,QAAAA,IAAI,GAAG,SAAP;AACA;;AACD,WAAK,iBAAL;AACCA,QAAAA,IAAI,GAAG,UAAP;AACA;AAED;;AACA,WAAK,cAAL;AACA,WAAK,OAAL;AACCA,QAAAA,IAAI,GAAG,OAAP;;AACA,gBAAQ8D,IAAR;AACC;AACA,eAAK,UAAL;AACCwB,YAAAA,QAAQ,GAAGqB,CAAX;AACA;AAED;;AACA,eAAK,QAAL;AACCpC,YAAAA,WAAW,GAAGoC,CAAd;AACA;AAED;;AACA,eAAK,QAAL;AACC,gBAAIpC,WAAW,IAAIoC,CAAf,IAAoB,EAAE,cAAc9F,KAAhB,CAAxB,EAAgD;AAC/ChB,cAAAA,CAAC,GAAGA,CAAC,GAAG,WAAR;AACA;;AACD;AAhBF;;AAkBA;;AAED,WAAK,yBAAL;AACC6G,QAAAA,IAAI,GAAGC,CAAC,IAAIA,CAAC,CAACC,MAAd;AACA;AAED;;AACA,WAAK,OAAL;AACC,YAAI,OAAOD,CAAP,KAAa,QAAjB,EAA2B;AAC1BA,UAAAA,CAAC,GAAG/G,aAAa,CAAC+G,CAAD,CAAjB;AACA;;AACD;;AACD,WAAK,eAAL;AACC3G,QAAAA,IAAI,GAAG,gBAAP;AACA;;AACD,WAAK,WAAL;AACCA,QAAAA,IAAI,GAAG,YAAP;AACA;;AAED;AAAS;AACR,cAAIvB,uBAAuB,CAACS,IAAxB,CAA6Bc,IAA7B,CAAJ,EAAwC;AACvCA,YAAAA,IAAI,GAAGA,IAAI,CAACC,OAAL,CAAaxB,uBAAb,EAAsC,OAAtC,EAA+CyB,WAA/C,EAAP;AACA,WAFD,MAEO,IAAI1B,WAAW,CAACU,IAAZ,CAAiBc,IAAjB,CAAJ,EAA4B;AAClC;AACA,WAFM,MAEA,IACN,CAACA,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmBpB,eAAe,CAACyB,GAAhB,CAAoBL,IAApB,CAApB,KACA2G,CAAC,IAAI,IAFC,EAGL;AACD;AACAA,YAAAA,CAAC,GAAGA,CAAC,GAAGjE,SAAR;AACA,WANM,MAMA,IAAI4B,SAAJ,EAAe;AACrB,gBAAI3F,cAAc,CAACO,IAAf,CAAoBc,IAApB,CAAJ,EAA+B;AAC9BA,cAAAA,IAAI,GACHA,IAAI,KAAK,SAAT,GACG,UADH,GAEGA,IAAI,CAACC,OAAL,CAAa,UAAb,EAAyB,KAAzB,EAAgCC,WAAhC,EAHJ;AAIA;AACD,WAPM,MAOA,IAAIxB,eAAe,CAACQ,IAAhB,CAAqBc,IAArB,CAAJ,EAAgC;AACtCA,YAAAA,IAAI,GAAGA,IAAI,CAACE,WAAL,EAAP;AACA;AACD;AA5FF,KAPuB;;;AAuGvB,QAAIyG,CAAC,IAAI,IAAL,IAAaA,CAAC,KAAK,KAAvB,EAA8B;AAC7B,UAAIA,CAAC,KAAK,IAAN,IAAcA,CAAC,KAAKjE,SAAxB,EAAmC;AAClC7C,QAAAA,CAAC,GAAGA,CAAC,GAAG,GAAJ,GAAUG,IAAd;AACA,OAFD,MAEO;AACNH,QAAAA,CAAC,GACAA,CAAC,GACD,GADA,GAEAG,IAFA,GAGA,IAHA,IAIC,OAAO2G,CAAP,IAAY,QAAZ,GAAuB5H,cAAc,CAAC4H,CAAD,CAArC,GAA2CA,CAAC,GAAGjE,SAJhD,IAKA,GAND;AAOA;AACD;AACD;;AAED,MAAIlE,WAAW,CAACU,IAAZ,CAAiB4E,IAAjB,CAAJ,EAA4B;AAC3B;AACA;AACA,UAAM,IAAIF,KAAJ,CAAW,GAAEE,IAAK,oCAAmCjE,CAAE,GAAvD,CAAN;AACA;;AAED,MAAI6G,IAAJ,EAAU,CAAV,MAEO,IAAI,OAAOpB,QAAP,KAAoB,QAAxB,EAAkC;AACxC;AACAoB,IAAAA,IAAI,GAAG3H,cAAc,CAACuG,QAAD,CAArB;AACA,GAHM,MAGA,IAAIA,QAAQ,IAAI,IAAZ,IAAoBA,QAAQ,KAAK,KAAjC,IAA0CA,QAAQ,KAAK,IAA3D,EAAiE;AACvE;AACA,QAAIuB,YAAY,GACf/C,IAAI,KAAK,KAAT,IAAmBA,IAAI,KAAK,eAAT,IAA4BQ,SADhD;AAEAoC,IAAAA,IAAI,GAAGlD,eAAe,CACrB8B,QADqB,EAErB3E,OAFqB,EAGrBkG,YAHqB,EAIrBtC,WAJqB,EAKrB7D,KALqB,EAMrB8D,SANqB,EAOrBC,QAPqB,CAAtB;AASA;;AAED,MAAI7B,SAAJ,EAAeA,SAAS,CAAClC,KAAD,CAAT,CAhdd;;AAmdDA,EAAAA,KAAK,CAACsB,MAAD,CAAL,GAAgB,IAAhB;AAEA,MAAIc,WAAJ,EAAiBA,WAAW,CAACpC,KAAD,CAAX,CArdhB;;AAwdD,MAAI,CAACgG,IAAD,IAASI,YAAY,CAACzG,GAAb,CAAiByD,IAAjB,CAAb,EAAqC;AACpC,WAAOjE,CAAC,GAAG,IAAX;AACA;;AAED,QAAMkH,MAAM,GAAG,OAAOjD,IAAP,GAAc,GAA7B;AACA,QAAMkD,QAAQ,GAAGnH,CAAC,GAAG,GAArB;AAEA,MAAI0C,OAAO,CAACmE,IAAD,CAAX,EAAmB,OAAO,CAACM,QAAD,EAAW,GAAGN,IAAd,EAAoBK,MAApB,CAAP,CAAnB,KACK,IAAI,OAAOL,IAAP,IAAe,QAAnB,EAA6B,OAAO,CAACM,QAAD,EAAWN,IAAX,EAAiBK,MAAjB,CAAP;AAClC,SAAOC,QAAQ,GAAGN,IAAX,GAAkBK,MAAzB;AACA;;AAED,MAAMD,YAAY,GAAG,IAAIjI,GAAJ,CAAQ,CAC5B,MAD4B,EAE5B,MAF4B,EAG5B,IAH4B,EAI5B,KAJ4B,EAK5B,SAL4B,EAM5B,OAN4B,EAO5B,IAP4B,EAQ5B,KAR4B,EAS5B,OAT4B,EAU5B,QAV4B,EAW5B,MAX4B,EAY5B,MAZ4B,EAa5B,OAb4B,EAc5B,QAd4B,EAe5B,OAf4B,EAgB5B,KAhB4B,CAAR,CAArB;;ACnsBA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA,MAAMoI,WAAW,GAAI,4jBAArB;AAEO,SAASC,gBAAT,GAA4B;AAClC,SAAQ,uBAAsBD,WAAY,eAA1C;AACA;AAED;AACA;AACA;AACA;AACA;;AACO,SAASE,aAAT,CAAuBC,EAAvB,EAA2BC,OAA3B,EAAoC;AAC1C,SAAQ,sCAAqCD,EAAG,KAAIC,OAAQ,kBAA5D;AACA;;ACxDD;AACA;AACA;AACA;AACA;;AACO,eAAeC,cAAf,CAA8B5G,KAA9B,EAAqC;AAAEC,EAAAA,OAAF;AAAW4G,EAAAA,OAAX;AAAoBC,EAAAA;AAApB,CAArC,EAAwE;AAC9E7G,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;AAEA;;AACA,QAAM8D,QAAQ,GAAG;AAChBgD,IAAAA,KAAK,EAAEC,IAAI,CAACC,GAAL,EADS;AAEhBH,IAAAA,WAFgB;AAGhBD,IAAAA,OAHgB;AAIhBjB,IAAAA,OAAO,EAAEsB,WAJO;AAKhBC,IAAAA,SAAS,EAAE;AALK,GAAjB,CAJ8E;AAa9E;;AACA,QAAMC,KAAK,GAAG/E,cAAc,CAACrC,KAAD,EAAQC,OAAR,EAAiB8D,QAAjB,CAA5B;AACA8C,EAAAA,OAAO,CAACO,KAAD,CAAP,CAf8E;;AAkB9E,QAAMC,GAAG,GAAGtD,QAAQ,CAACoD,SAAT,CAAmB5I,MAA/B;;AACA,MAAI8I,GAAG,GAAG,CAAV,EAAa;AACZR,IAAAA,OAAO,CAAC,cAAD,CAAP;AACAA,IAAAA,OAAO,CAACL,gBAAgB,CAAA,CAAjB,CAAP,CAFY;;AAIZ,UAAMc,YAAY,CAACvD,QAAD,CAAlB;AACA8C,IAAAA,OAAO,CAAC,QAAD,CAAP;AACA;AACD;;AAED,eAAeS,YAAf,CAA4BvD,QAA5B,EAAsC;AACrC,MAAIA,QAAQ,CAACoD,SAAT,CAAmB5I,MAAnB,GAA4B,CAAhC,EAAmC;AAClC,UAAMgJ,WAAW,GAAG,CAAC,GAAGxD,QAAQ,CAACoD,SAAb,CAApB;AACA,UAAMxG,OAAO,CAAC6G,GAAR,CAAYzD,QAAQ,CAACoD,SAAT,CAAmBM,GAAnB,CAAwBtI,CAAD,IAAOA,CAAC,CAACuB,OAAhC,CAAZ,CAAN;AACAqD,IAAAA,QAAQ,CAACoD,SAAT,GAAqBpD,QAAQ,CAACoD,SAAT,CAAmBO,MAAnB,CACnBvI,CAAD,IAAO,CAACoI,WAAW,CAACI,QAAZ,CAAqBxI,CAArB,CADY,CAArB;AAGA,UAAMmI,YAAY,CAACvD,QAAD,CAAlB;AACA;AACD;AAED;;;AACA,SAASmD,WAAT,CAAqBvB,KAArB,EAA4B3F,KAA5B,EAAmC4H,WAAnC,EAAgD;AAC/C,MAAI,CAACjC,KAAD,IAAU,CAACA,KAAK,CAAC1C,IAArB,EAA2B,OADoB;;AAI/C,SAAQjD,KAAK,GAAGA,KAAK,CAACsB,MAAD,CAArB,EAAgC;AAC/B,QAAIiD,SAAS,GAAGvE,KAAK,CAACoB,SAAD,CAArB;;AACA,QAAImD,SAAS,IAAIA,SAAS,CAAC7C,iBAAD,CAA1B,EAA+C;AAC9C;AACA;AACD;;AAED,MAAI,CAAC1B,KAAL,EAAY;AAEZ,QAAM0G,EAAE,GAAG1G,KAAK,CAACE,GAAjB;AACA,QAAM2H,KAAK,GAAG,KAAKV,SAAL,CAAeW,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACrB,EAAF,KAASA,EAApC,CAAd;AACA,QAAMsB,IAAI,GAAG,IAAIxH,QAAJ,EAAb;AAEA,QAAMsG,WAAW,GAAG,KAAKA,WAAzB;;AACA,MAAIA,WAAJ,EAAiB;AAChB;AACA,QAAIA,WAAW,CAACmB,OAAhB,EAAyBD,IAAI,CAACpH,OAAL,GAAzB,KACKkG,WAAW,CAACoB,gBAAZ,CAA6B,OAA7B,EAAsCF,IAAI,CAACpH,OAA3C;AACL;;AAED,QAAMF,OAAO,GAAGiF,KAAK,CAAC1C,IAAN,CACf,MAAM;AACL,QAAI6D,WAAW,IAAIA,WAAW,CAACmB,OAA/B,EAAwC;AACxC,UAAM/D,KAAK,GAAG0D,WAAW,CAAC5H,KAAK,CAACG,KAAN,CAAYyE,QAAb,CAAzB;AACA,QAAIV,KAAJ,EAAW,KAAK2C,OAAL,CAAaJ,aAAa,CAACC,EAAD,EAAKxC,KAAL,CAA1B;AACX,GALc;AAOf;AACA,OAAK0B,OARU,CAAhB;AAWA,OAAKuB,SAAL,CAAe/C,IAAf,CAAoB;AACnBsC,IAAAA,EADmB;AAEnB1G,IAAAA,KAFmB;AAGnBU,IAAAA,OAAO,EAAEC,OAAO,CAACqH,IAAR,CAAa,CAACtH,OAAD,EAAUsH,IAAI,CAACtH,OAAf,CAAb;AAHU,GAApB;AAMA,QAAMyH,QAAQ,GAAGP,WAAW,CAAC5H,KAAK,CAACG,KAAN,CAAYgI,QAAb,CAA5B;AAEA,SAAON,KAAK,GACT,EADS,GAER,qBAAoBnB,EAAG,MAAKyB,QAAS,sBAAqBzB,EAAG,KAFjE;AAGA;;AC7FD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AACO,SAAS0B,sBAAT,CAAgCpI,KAAhC,EAAuCwC,OAAvC,EAAgDvC,OAAhD,EAAyD;AAC/D,QAAMoI,OAAO,GAAG,IAAIC,WAAJ,CAAgB,OAAhB,CAAhB;AAEA,QAAMC,UAAU,GAAG,IAAIC,eAAJ,EAAnB;AACA,QAAMC,MAAM,GAAG,IAAIC,WAAJ,EAAf;AAEA9B,EAAAA,cAAc,CAAC5G,KAAD,EAAQ;AACrBC,IAAAA,OADqB;AAErB6G,IAAAA,WAAW,EAAEyB,UAAU,CAACI,MAFH;AAGrB/C,IAAAA,OAAO,EAAGD,KAAD,IAAW;AACnB,UAAInD,OAAO,CAACoD,OAAZ,EAAqB;AACpBpD,QAAAA,OAAO,CAACoD,OAAR,CAAgBD,KAAhB;AACA;;AACD4C,MAAAA,UAAU,CAACK,KAAX,CAAiBjD,KAAjB;AACA,KARoB;;AASrBkB,IAAAA,OAAO,CAAC1H,CAAD,EAAI;AACVsJ,MAAAA,MAAM,CAACI,KAAP,CAAaR,OAAO,CAACS,MAAR,CAAe3J,CAAf,CAAb;AACA;;AAXoB,GAAR,CAAd,CAaE8D,IAbF,CAaO,MAAM;AACXT,IAAAA,OAAO,CAACuG,UAAR,IAAsBvG,OAAO,CAACuG,UAAR,EAAtB;AACAN,IAAAA,MAAM,CAACO,GAAP;AACA,GAhBF,EAiBEC,KAjBF,CAiBStD,KAAD,IAAW;AACjB8C,IAAAA,MAAM,CAACS,OAAP,CAAevD,KAAf;AACA,GAnBF;AAqBAhF,EAAAA,OAAO,CAACC,OAAR,GAAkBqC,IAAlB,CAAuB,MAAM;AAC5BT,IAAAA,OAAO,CAAC2G,YAAR,IAAwB3G,OAAO,CAAC2G,YAAR,EAAxB;AACA,GAFD;AAIA,SAAO;AACNP,IAAAA,KAAK,GAAG;AACPL,MAAAA,UAAU,CAACK,KAAX;AACAH,MAAAA,MAAM,CAACS,OAAP,CAAe,IAAIhG,KAAJ,CAAU,SAAV,CAAf;AACA,KAJK;;AAKN;AACF;AACA;AACEkG,IAAAA,IAAI,CAACC,QAAD,EAAW;AACdZ,MAAAA,MAAM,CAACW,IAAP,CAAYC,QAAZ,EAAsB;AAAEL,QAAAA,GAAG,EAAE;AAAP,OAAtB;AACA;;AAVK,GAAP;AAYA;;;;"}