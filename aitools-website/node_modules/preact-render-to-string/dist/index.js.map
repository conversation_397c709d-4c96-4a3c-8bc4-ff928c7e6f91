{"version": 3, "file": "index.js", "sources": ["../src/lib/util.js", "../src/index.js"], "sourcesContent": ["export const VOID_ELEMENTS = /^(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const NAMESPACE_REPLACE_REGEX = /^(xlink|xmlns|xml)([A-Z])/;\nexport const HTML_LOWER_CASE = /^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/;\nexport const SVG_CAMEL_CASE = /^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/;\n\n// Boolean DOM properties that translate to enumerated ('true'/'false') attributes\nexport const HTML_ENUMERATED = new Set(['draggable', 'spellcheck']);\n\n// DOM properties that should NOT have \"px\" added when numeric\nconst ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out = out + str.slice(last, i);\n\t\tout = out + ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out = out + str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst IS_NON_DIMENSIONAL = new Set([\n\t'animation-iteration-count',\n\t'border-image-outset',\n\t'border-image-slice',\n\t'border-image-width',\n\t'box-flex',\n\t'box-flex-group',\n\t'box-ordinal-group',\n\t'column-count',\n\t'fill-opacity',\n\t'flex',\n\t'flex-grow',\n\t'flex-negative',\n\t'flex-order',\n\t'flex-positive',\n\t'flex-shrink',\n\t'flood-opacity',\n\t'font-weight',\n\t'grid-column',\n\t'grid-row',\n\t'line-clamp',\n\t'line-height',\n\t'opacity',\n\t'order',\n\t'orphans',\n\t'stop-opacity',\n\t'stroke-dasharray',\n\t'stroke-dashoffset',\n\t'stroke-miterlimit',\n\t'stroke-opacity',\n\t'stroke-width',\n\t'tab-size',\n\t'widows',\n\t'z-index',\n\t'zoom'\n]);\n\nconst CSS_REGEX = /[A-Z]/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tconst name =\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\tlet suffix = ';';\n\t\t\tif (\n\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t// Exclude custom-attributes\n\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t!IS_NON_DIMENSIONAL.has(name)\n\t\t\t) {\n\t\t\t\tsuffix = 'px;';\n\t\t\t}\n\t\t\tstr = str + name + ':' + val + suffix;\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: new Array(0)\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n\n/**\n * @template T\n */\nexport class Deferred {\n\tconstructor() {\n\t\t// eslint-disable-next-line lines-around-comment\n\t\t/** @type {Promise<T>} */\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n}\n", "import {\n\tencodeEntities,\n\tstyleObjToCss,\n\tUNSAFE_NAME,\n\tNAMESPACE_REPLACE_REGEX,\n\tHTML_LOWER_CASE,\n\tHTML_ENUMERATED,\n\tSVG_CAMEL_CASE,\n\tcreateComponent\n} from './lib/util.js';\nimport { options, h, Fragment } from 'preact';\nimport {\n\tCHILDREN,\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tPARENT,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE,\n\tCATCH_ERROR\n} from './lib/constants.js';\n\nconst EMPTY_OBJ = {};\nconst EMPTY_ARR = [];\nconst isArray = Array.isArray;\nconst assign = Object.assign;\nconst EMPTY_STR = '';\n\n// Global state for the current render pass\nlet beforeDiff, afterDiff, renderHook, ummountHook;\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @param {RendererState} [_rendererState] for internal use\n * @returns {string} serialized HTML\n */\nexport function renderToString(vnode, context, _rendererState) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\tfalse,\n\t\t\t_rendererState\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\treturn rendered.join(EMPTY_STR);\n\t\t}\n\t\treturn rendered;\n\t} catch (e) {\n\t\tif (e.then) {\n\t\t\tthrow new Error('Use \"renderToStringAsync\" for suspenseful rendering.');\n\t\t}\n\n\t\tthrow e;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @returns {string} serialized HTML\n */\nexport async function renderToStringAsync(vnode, context) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = await _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\ttrue,\n\t\t\tundefined\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\tlet count = 0;\n\t\t\tlet resolved = rendered;\n\n\t\t\t// Resolving nested Promises with a maximum depth of 25\n\t\t\twhile (\n\t\t\t\tresolved.some(\n\t\t\t\t\t(element) => element && typeof element.then === 'function'\n\t\t\t\t) &&\n\t\t\t\tcount++ < 25\n\t\t\t) {\n\t\t\t\tresolved = (await Promise.all(resolved)).flat();\n\t\t\t}\n\n\t\t\treturn resolved.join(EMPTY_STR);\n\t\t}\n\n\t\treturn rendered;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n */\nfunction renderClassComponent(vnode, context) {\n\tlet type = /** @type {import(\"preact\").ComponentClass<typeof vnode.props>} */ (vnode.type);\n\n\tlet isMounting = true;\n\tlet c;\n\tif (vnode[COMPONENT]) {\n\t\tisMounting = false;\n\t\tc = vnode[COMPONENT];\n\t\tc.state = c[NEXT_STATE];\n\t} else {\n\t\tc = new type(vnode.props, context);\n\t}\n\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\n\tc.props = vnode.props;\n\tc.context = context;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\n\tif (c.state == null) c.state = EMPTY_OBJ;\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tif (type.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\ttype.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (isMounting && c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t} else if (!isMounting && c.componentWillUpdate) {\n\t\tc.componentWillUpdate();\n\t}\n\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, context);\n}\n\n/**\n * Recursively render VNodes to HTML.\n * @param {VNode|any} vnode\n * @param {any} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode} parent\n * @param {boolean} asyncMode\n * @param {RendererState | undefined} [renderer]\n * @returns {string | Promise<string> | (string | Promise<string>)[]}\n */\nfunction _renderToString(\n\tvnode,\n\tcontext,\n\tisSvgMode,\n\tselectValue,\n\tparent,\n\tasyncMode,\n\trenderer\n) {\n\t// Ignore non-rendered VNodes/values\n\tif (\n\t\tvnode == null ||\n\t\tvnode === true ||\n\t\tvnode === false ||\n\t\tvnode === EMPTY_STR\n\t) {\n\t\treturn EMPTY_STR;\n\t}\n\n\tlet vnodeType = typeof vnode;\n\t// Text VNodes: escape as HTML\n\tif (vnodeType != 'object') {\n\t\tif (vnodeType == 'function') return EMPTY_STR;\n\t\treturn vnodeType == 'string' ? encodeEntities(vnode) : vnode + EMPTY_STR;\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = EMPTY_STR,\n\t\t\trenderArray;\n\t\tparent[CHILDREN] = vnode;\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tlet child = vnode[i];\n\t\t\tif (child == null || typeof child == 'boolean') continue;\n\n\t\t\tconst childRender = _renderToString(\n\t\t\t\tchild,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tparent,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (typeof childRender == 'string') {\n\t\t\t\trendered = rendered + childRender;\n\t\t\t} else {\n\t\t\t\tif (!renderArray) {\n\t\t\t\t\trenderArray = [];\n\t\t\t\t}\n\n\t\t\t\tif (rendered) renderArray.push(rendered);\n\n\t\t\t\trendered = EMPTY_STR;\n\n\t\t\t\tif (isArray(childRender)) {\n\t\t\t\t\trenderArray.push(...childRender);\n\t\t\t\t} else {\n\t\t\t\t\trenderArray.push(childRender);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (renderArray) {\n\t\t\tif (rendered) renderArray.push(rendered);\n\t\t\treturn renderArray;\n\t\t}\n\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return EMPTY_STR;\n\n\tvnode[PARENT] = parent;\n\tif (beforeDiff) beforeDiff(vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tif (typeof type == 'function') {\n\t\tlet cctx = context,\n\t\t\tcontextType,\n\t\t\trendered,\n\t\t\tcomponent;\n\t\tif (type === Fragment) {\n\t\t\t// Serialized precompiled JSX.\n\t\t\tif ('tpl' in props) {\n\t\t\t\tlet out = EMPTY_STR;\n\t\t\t\tfor (let i = 0; i < props.tpl.length; i++) {\n\t\t\t\t\tout = out + props.tpl[i];\n\n\t\t\t\t\tif (props.exprs && i < props.exprs.length) {\n\t\t\t\t\t\tconst value = props.exprs[i];\n\t\t\t\t\t\tif (value == null) continue;\n\n\t\t\t\t\t\t// Check if we're dealing with a vnode or an array of nodes\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\ttypeof value == 'object' &&\n\t\t\t\t\t\t\t(value.constructor === undefined || isArray(value))\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tout =\n\t\t\t\t\t\t\t\tout +\n\t\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Values are pre-escaped by the JSX transform\n\t\t\t\t\t\t\tout = out + value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn out;\n\t\t\t} else if ('UNSTABLE_comment' in props) {\n\t\t\t\t// Fragments are the least used components of core that's why\n\t\t\t\t// branching here for comments has the least effect on perf.\n\t\t\t\treturn '<!--' + encodeEntities(props.UNSTABLE_comment) + '-->';\n\t\t\t}\n\n\t\t\trendered = props.children;\n\t\t} else {\n\t\t\tcontextType = type.contextType;\n\t\t\tif (contextType != null) {\n\t\t\t\tlet provider = context[contextType.__c];\n\t\t\t\tcctx = provider ? provider.props.value : contextType.__;\n\t\t\t}\n\n\t\t\tlet isClassComponent =\n\t\t\t\ttype.prototype && typeof type.prototype.render == 'function';\n\t\t\tif (isClassComponent) {\n\t\t\t\trendered = /**#__NOINLINE__**/ renderClassComponent(vnode, cctx);\n\t\t\t\tcomponent = vnode[COMPONENT];\n\t\t\t} else {\n\t\t\t\tvnode[COMPONENT] = component = /**#__NOINLINE__**/ createComponent(\n\t\t\t\t\tvnode,\n\t\t\t\t\tcctx\n\t\t\t\t);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (component[DIRTY] && count++ < 25) {\n\t\t\t\t\tcomponent[DIRTY] = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\trendered = type.call(component, props, cctx);\n\t\t\t\t}\n\t\t\t\tcomponent[DIRTY] = true;\n\t\t\t}\n\n\t\t\tif (component.getChildContext != null) {\n\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\tisClassComponent &&\n\t\t\t\toptions.errorBoundaries &&\n\t\t\t\t(type.getDerivedStateFromError || component.componentDidCatch)\n\t\t\t) {\n\t\t\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t\t\t// need to mirror that logic here too\n\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\trendered != null &&\n\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\trendered.key == null &&\n\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (err) {\n\t\t\t\t\tif (type.getDerivedStateFromError) {\n\t\t\t\t\t\tcomponent[NEXT_STATE] = type.getDerivedStateFromError(err);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component.componentDidCatch) {\n\t\t\t\t\t\tcomponent.componentDidCatch(err, EMPTY_OBJ);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component[DIRTY]) {\n\t\t\t\t\t\trendered = renderClassComponent(vnode, context);\n\t\t\t\t\t\tcomponent = vnode[COMPONENT];\n\n\t\t\t\t\t\tif (component.getChildContext != null) {\n\t\t\t\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\t\t\trendered != null &&\n\t\t\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\t\t\trendered.key == null &&\n\t\t\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn EMPTY_STR;\n\t\t\t\t} finally {\n\t\t\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t\t\tvnode[PARENT] = null;\n\n\t\t\t\t\tif (ummountHook) ummountHook(vnode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t// need to mirror that logic here too\n\t\tlet isTopLevelFragment =\n\t\t\trendered != null &&\n\t\t\trendered.type === Fragment &&\n\t\t\trendered.key == null &&\n\t\t\trendered.props.tpl == null;\n\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\ttry {\n\t\t\t// Recurse into children before invoking the after-diff hook\n\t\t\tconst str = _renderToString(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tvnode,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t// when we are dealing with suspense we can't do this...\n\t\t\tvnode[PARENT] = null;\n\n\t\t\tif (options.unmount) options.unmount(vnode);\n\n\t\t\treturn str;\n\t\t} catch (error) {\n\t\t\tif (!asyncMode && renderer && renderer.onError) {\n\t\t\t\tlet res = renderer.onError(error, vnode, (child) =>\n\t\t\t\t\t_renderToString(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\tif (res !== undefined) return res;\n\n\t\t\t\tlet errorHook = options[CATCH_ERROR];\n\t\t\t\tif (errorHook) errorHook(error, vnode);\n\t\t\t\treturn EMPTY_STR;\n\t\t\t}\n\n\t\t\tif (!asyncMode) throw error;\n\n\t\t\tif (!error || typeof error.then != 'function') throw error;\n\n\t\t\tconst renderNestedChildren = () => {\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tif (!e || typeof e.then != 'function') throw e;\n\n\t\t\t\t\treturn e.then(\n\t\t\t\t\t\t() =>\n\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\trenderNestedChildren\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\treturn error.then(renderNestedChildren);\n\t\t}\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<' + type,\n\t\thtml = EMPTY_STR,\n\t\tchildren;\n\n\tfor (let name in props) {\n\t\tlet v = props[name];\n\n\t\tif (typeof v == 'function' && name !== 'class' && name !== 'className') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tswitch (name) {\n\t\t\tcase 'children':\n\t\t\t\tchildren = v;\n\t\t\t\tcontinue;\n\n\t\t\t// VDOM-specific props\n\t\t\tcase 'key':\n\t\t\tcase 'ref':\n\t\t\tcase '__self':\n\t\t\tcase '__source':\n\t\t\t\tcontinue;\n\n\t\t\t// prefer for/class over htmlFor/className\n\t\t\tcase 'htmlFor':\n\t\t\t\tif ('for' in props) continue;\n\t\t\t\tname = 'for';\n\t\t\t\tbreak;\n\t\t\tcase 'className':\n\t\t\t\tif ('class' in props) continue;\n\t\t\t\tname = 'class';\n\t\t\t\tbreak;\n\n\t\t\t// Form element reflected properties\n\t\t\tcase 'defaultChecked':\n\t\t\t\tname = 'checked';\n\t\t\t\tbreak;\n\t\t\tcase 'defaultSelected':\n\t\t\t\tname = 'selected';\n\t\t\t\tbreak;\n\n\t\t\t// Special value attribute handling\n\t\t\tcase 'defaultValue':\n\t\t\tcase 'value':\n\t\t\t\tname = 'value';\n\t\t\t\tswitch (type) {\n\t\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\t\tcase 'textarea':\n\t\t\t\t\t\tchildren = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// <select value> is serialized as a selected attribute on the matching option child\n\t\t\t\t\tcase 'select':\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// Add a selected attribute to <option> if its value matches the parent <select> value\n\t\t\t\t\tcase 'option':\n\t\t\t\t\t\tif (selectValue == v && !('selected' in props)) {\n\t\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\tcase 'dangerouslySetInnerHTML':\n\t\t\t\thtml = v && v.__html;\n\t\t\t\tcontinue;\n\n\t\t\t// serialize object styles to a CSS string\n\t\t\tcase 'style':\n\t\t\t\tif (typeof v === 'object') {\n\t\t\t\t\tv = styleObjToCss(v);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'acceptCharset':\n\t\t\t\tname = 'accept-charset';\n\t\t\t\tbreak;\n\t\t\tcase 'httpEquiv':\n\t\t\t\tname = 'http-equiv';\n\t\t\t\tbreak;\n\n\t\t\tdefault: {\n\t\t\t\tif (NAMESPACE_REPLACE_REGEX.test(name)) {\n\t\t\t\t\tname = name.replace(NAMESPACE_REPLACE_REGEX, '$1:$2').toLowerCase();\n\t\t\t\t} else if (UNSAFE_NAME.test(name)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t} else if (\n\t\t\t\t\t(name[4] === '-' || HTML_ENUMERATED.has(name)) &&\n\t\t\t\t\tv != null\n\t\t\t\t) {\n\t\t\t\t\t// serialize boolean aria-xyz or enumerated attribute values as strings\n\t\t\t\t\tv = v + EMPTY_STR;\n\t\t\t\t} else if (isSvgMode) {\n\t\t\t\t\tif (SVG_CAMEL_CASE.test(name)) {\n\t\t\t\t\t\tname =\n\t\t\t\t\t\t\tname === 'panose1'\n\t\t\t\t\t\t\t\t? 'panose-1'\n\t\t\t\t\t\t\t\t: name.replace(/([A-Z])/g, '-$1').toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t} else if (HTML_LOWER_CASE.test(name)) {\n\t\t\t\t\tname = name.toLowerCase();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// write this attribute to the buffer\n\t\tif (v != null && v !== false) {\n\t\t\tif (v === true || v === EMPTY_STR) {\n\t\t\t\ts = s + ' ' + name;\n\t\t\t} else {\n\t\t\t\ts =\n\t\t\t\t\ts +\n\t\t\t\t\t' ' +\n\t\t\t\t\tname +\n\t\t\t\t\t'=\"' +\n\t\t\t\t\t(typeof v == 'string' ? encodeEntities(v) : v + EMPTY_STR) +\n\t\t\t\t\t'\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\t// this seems to performs a lot better than throwing\n\t\t// return '<!-- -->';\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}>`);\n\t}\n\n\tif (html) {\n\t\t// dangerouslySetInnerHTML defined this node's contents\n\t} else if (typeof children === 'string') {\n\t\t// single text child\n\t\thtml = encodeEntities(children);\n\t} else if (children != null && children !== false && children !== true) {\n\t\t// recurse into this element VNode's children\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\thtml = _renderToString(\n\t\t\tchildren,\n\t\t\tcontext,\n\t\t\tchildSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode,\n\t\t\tasyncMode,\n\t\t\trenderer\n\t\t);\n\t}\n\n\tif (afterDiff) afterDiff(vnode);\n\n\t// TODO: this was commented before\n\tvnode[PARENT] = null;\n\n\tif (ummountHook) ummountHook(vnode);\n\n\t// Emit self-closing tag for empty void elements:\n\tif (!html && SELF_CLOSING.has(type)) {\n\t\treturn s + '/>';\n\t}\n\n\tconst endTag = '</' + type + '>';\n\tconst startTag = s + '>';\n\n\tif (isArray(html)) return [startTag, ...html, endTag];\n\telse if (typeof html != 'string') return [startTag, html, endTag];\n\treturn startTag + html + endTag;\n}\n\nconst SELF_CLOSING = new Set([\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'command',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr'\n]);\n\nexport default renderToString;\nexport const render = renderToString;\nexport const renderToStaticMarkup = renderToString;\n"], "names": ["UNSAFE_NAME", "NAMESPACE_REPLACE_REGEX", "HTML_LOWER_CASE", "SVG_CAMEL_CASE", "HTML_ENUMERATED", "Set", "ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "JS_TO_CSS", "IS_NON_DIMENSIONAL", "CSS_REGEX", "styleObjToCss", "s", "prop", "val", "name", "replace", "toLowerCase", "suffix", "startsWith", "has", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "Array", "pact", "state", "value", "o", "_settle", "bind", "v", "then", "observer", "_Pact", "prototype", "onFulfilled", "onRejected", "result", "callback", "e", "_this", "thenable", "update", "body", "stage", "shouldC<PERSON><PERSON>ue", "_isSettledPact", "updateValue", "reject", "_resumeAfterTest", "_resumeAfterBody", "_resumeAfterUpdate", "finalizer", "renderToStringAsync", "beforeDiff", "afterDiff", "renderHook", "ummountHook", "EMPTY_OBJ", "EMPTY_ARR", "isArray", "assign", "Object", "renderToString", "_rendererState", "previousSkipEffects", "options", "unmount", "parent", "h", "Fragment", "rendered", "_renderToString", "join", "Error", "renderClassComponent", "c", "type", "isMounting", "getDerivedStateFromProps", "componentWillMount", "componentWillUpdate", "render", "isSvgMode", "selectValue", "asyncMode", "renderer", "vnodeType", "renderArray", "child", "childRender", "push", "constructor", "contextType", "component", "cctx", "tpl", "exprs", "UNSTABLE_comment", "children", "provider", "__c", "__", "isClassComponent", "count", "call", "getChildContext", "errorBoundaries", "getDerivedStateFromError", "componentDidCatch", "key", "err", "error", "onError", "res", "errorHook", "renderNestedChildren", "html", "__html", "SELF_CLOSING", "endTag", "startTag", "renderToStaticMarkup", "resolved", "some", "element", "Promise", "all", "_Promise$all", "flat"], "mappings": "wBACaA,EAAc,mBACdC,EAA0B,4BAC1BC,EAAkB,8JAClBC,EAAiB,yQAGjBC,EAAkB,IAAIC,IAAI,CAAC,YAAa,eAG/CC,EAAmB,iBAGTC,EAAeC,GAE9B,GAAmB,IAAfA,EAAIC,SAA+C,IAA/BH,EAAiBI,KAAKF,GAAgB,OAAOA,EAQrE,IANA,IAAIG,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGCF,EAAIJ,EAAIC,OAAQG,IAAK,CAC3B,OAAQJ,EAAIO,WAAWH,IACtB,QACCE,EAAK,SACL,MACD,QACCA,EAAK,QACL,MACD,QACCA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAYL,EAAIQ,MAAML,EAAMC,IAC5CC,GAAYC,EAEZH,EAAOC,EAAI,CACX,CAED,OADIA,IAAMD,IAAME,GAAYL,EAAIQ,MAAML,EAAMC,IACrCC,CACP,CAUD,IAAMI,EAAY,GAEZC,EAAqB,IAAIb,IAAI,CAClC,4BACA,sBACA,qBACA,qBACA,WACA,iBACA,oBACA,eACA,eACA,OACA,YACA,gBACA,aACA,gBACA,cACA,gBACA,cACA,cACA,WACA,aACA,cACA,UACA,QACA,UACA,eACA,mBACA,oBACA,oBACA,iBACA,eACA,WACA,SACA,UACA,SAGKc,EAAY,kBAEFC,EAAcC,GAC7B,IAAIb,EAAM,GACV,IAAK,IAAIc,KAAQD,EAAG,CACnB,IAAIE,EAAMF,EAAEC,GACZ,GAAW,MAAPC,GAAuB,KAARA,EAAY,CAC9B,IAAMC,EACM,KAAXF,EAAK,GACFA,EACAL,EAAUK,KACTL,EAAUK,GAAQA,EAAKG,QAAQN,EAAW,OAAOO,eAElDC,EAAS,IAEG,iBAARJ,GAENC,EAAKI,WAAW,OAChBV,EAAmBW,IAAIL,KAExBG,EAAS,OAEVnB,EAAMA,EAAMgB,EAAO,IAAMD,EAAMI,CAC/B,CACD,CACD,OAAOnB,QAAOsB,CACd,CAkBD,SAASC,IACRC,KAAKC,KAAM,CACX,UAEeC,EAAgBC,EAAOC,GACtC,MAAO,CACNC,IAAKF,EACLC,QAAAA,EACAE,MAAOH,EAAMG,MAEbC,SAAUR,EACVS,YAAaT,EACbE,KAAK,EAELQ,IAAK,IAAIC,MAAM,GAEhB,CCnHM,WAAiBC,EAAMC,EAAOC,GACpC,IAAKF,EAAKtB,EAAG,CACZ,GAAIwB,eAAwB,CAC3B,IAAIA,EAAMxB,EAOT,YADAwB,EAAMC,EAAIC,EAAQC,KAAK,KAAML,EAAMC,IALvB,EAARA,IACHA,EAAQC,EAAMxB,GAEfwB,EAAQA,EAAMI,CAKf,CACD,GAAIJ,GAASA,EAAMK,KAElB,YADAL,EAAMK,KAAKH,EAAQC,KAAK,KAAML,EAAMC,GAAQG,EAAQC,KAAK,KAAML,EAAM,IAGtEA,EAAKtB,EAAIuB,EACTD,EAAKM,EAAIJ,EACT,MAAMM,EAAWR,EAAKG,EAClBK,GACHA,EAASR,EAEV,CACD,CA9DM,mBAA4B,WAClC,cAiCA,OAhCAS,EAAMC,UAAUH,KAAO,SAASI,EAAaC,GAC5C,IAAMC,EAAS,MACTZ,EAAQZ,KAAKX,EACnB,GAAIuB,EAAO,CACV,IAAMa,EAAmB,EAARb,EAAYU,EAAcC,EAC3C,GAAIE,EAAU,CACb,IACCV,EAAQS,EAAQ,EAAGC,EAASzB,KAAKiB,GAGjC,CAFC,MAAOS,GACRX,EAAQS,EAAQ,EAAGE,EACnB,CACD,OAAOF,CACP,CACA,WAED,CAeD,OAdAxB,KAAKc,EAAI,SAASa,GACjB,IACC,IAAMd,EAAQc,EAAMV,EACN,EAAVU,EAAMtC,EACT0B,EAAQS,EAAQ,EAAGF,EAAcA,EAAYT,GAASA,GAC5CU,EACVR,EAAQS,EAAQ,EAAGD,EAAWV,IAE9BE,EAAQS,EAAQ,EAAGX,EAIpB,CAFC,MAAOa,GACRX,EAAQS,EAAQ,EAAGE,EACnB,CACD,EACMF,CACP,GAED,CAnCkC,GAgE5B,WAAwBI,GAC9B,OAAOA,gBAA0C,EAAbA,EAASvC,CAC7C,CA4LM,WAAcX,EAAMmD,EAAQC,GAElC,IADA,IAAIC,IACK,CACR,IAAIC,EAAiBtD,IAIrB,GAHIuD,EAAeD,KAClBA,EAAiBA,EAAef,IAE5Be,EACJ,OAAOR,EAER,GAAIQ,EAAed,KAAM,CACxBa,EAAQ,EACR,KACA,CACD,IAAIP,EAASM,IACb,GAAIN,GAAUA,EAAON,KAAM,CAC1B,IAAIe,EAAeT,GAEZ,CACNO,EAAQ,EACR,KACA,CAJAP,EAASA,EAAOnC,CAKjB,CACD,GAAIwC,EAAQ,CACX,IAAIK,EAAcL,IAClB,GAAIK,GAAeA,EAAYhB,OAASe,EAAeC,GAAc,CACpEH,EAAQ,EACR,KACA,CACD,CACD,CACD,IAAIpB,EAAO,MACPwB,EAASpB,EAAQC,KAAK,KAAML,EAAM,GAEtC,OADW,IAAVoB,EAAcC,EAAed,KAAKkB,GAA8B,IAAVL,EAAcP,EAAON,KAAKmB,GAAoBH,EAAYhB,KAAKoB,IAAqBpB,UAAK,EAAQiB,GACjJxB,EACP,SAAS0B,EAAiBxB,GACzBW,EAASX,EACT,EAAG,CACF,GAAIgB,IACHK,EAAcL,MACKK,EAAYhB,OAASe,EAAeC,GAEtD,YADAA,EAAYhB,KAAKoB,GAAoBpB,UAAK,EAAQiB,GAKpD,KADAH,EAAiBtD,MACOuD,EAAeD,KAAoBA,EAAef,EAEzE,YADAF,EAAQJ,EAAM,EAAGa,GAGlB,GAAIQ,EAAed,KAElB,YADAc,EAAed,KAAKkB,GAAkBlB,UAAK,EAAQiB,GAIhDF,EADJT,EAASM,OAERN,EAASA,EAAOP,EAEjB,QAASO,IAAWA,EAAON,MAC5BM,EAAON,KAAKmB,GAAkBnB,UAAK,EAAQiB,EAC3C,CACD,SAASC,EAAiBJ,GACrBA,GACHR,EAASM,MACKN,EAAON,KACpBM,EAAON,KAAKmB,GAAkBnB,UAAK,EAAQiB,GAE3CE,EAAiBb,GAGlBT,EAAQJ,EAAM,EAAGa,EAElB,CACD,SAASc,KACJN,EAAiBtD,KAChBsD,EAAed,KAClBc,EAAed,KAAKkB,GAAkBlB,UAAK,EAAQiB,GAEnDC,EAAiBJ,GAGlBjB,EAAQJ,EAAM,EAAGa,EAElB,CACD,CA4OM,WAA0BM,EAAMS,GACtC,IACC,IAAIf,EAASM,GAGb,CAFC,MAAOJ,GACR,OAAOa,GAAU,EAAMb,EACvB,CACD,OAAIF,GAAUA,EAAON,KACbM,EAAON,KAAKqB,EAAUvB,KAAK,MAAM,GAAQuB,EAAUvB,KAAK,MAAM,IAE/DuB,GAAU,EAAOf,EACxB,CAzeqBgB,IA/DlBC,EAAYC,EAAWC,EAAYC,EAPjCC,EAAY,GACZC,EAAY,GACZC,EAAUrC,MAAMqC,QAChBC,EAASC,OAAOD,gBAaNE,EAAe/C,EAAOC,EAAS+C,GAM9C,IAAMC,EAAsBC,UAAO,IACnCA,UAAO,KAAiB,EAGxBZ,EAAaY,UAAO,IACpBX,EAAYW,UAAO,OACnBV,EAAaU,UAAO,IACpBT,EAAcS,UAAQC,QAEtB,IAAMC,EAASC,IAAEC,WAAU,MAC3BF,EAAM,IAAa,CAACpD,GAEpB,IACC,IAAMuD,EAAWC,EAChBxD,EACAC,GAAWyC,GACX,OACA/C,EACAyD,GACA,EACAJ,GAGD,OAAIJ,EAAQW,GACJA,EAASE,KA1CD,IA4CTF,CAaP,CAZC,MAAOhC,GACR,GAAIA,EAAER,KACL,UAAU2C,MAAM,wDAGjB,MAAMnC,CACN,CArBD,QAwBK2B,UAAO,KAAUA,UAAO,IAASlD,EAAO2C,GAC5CO,UAAO,IAAiBD,EACxBN,EAAUrE,OAAS,CACnB,CACD,CAoED,SAASqF,EAAqB3D,EAAOC,GACpC,IAGI2D,EAHAC,EAA2E7D,EAAM6D,KAEjFC,GAAa,EA0CjB,OAxCI9D,EAAK,KACR8D,GAAa,GACbF,EAAI5D,EAAK,KACPS,MAAQmD,EAAC,KAEXA,EAAI,IAAIC,EAAK7D,EAAMG,MAAOF,GAG3BD,EAAK,IAAc4D,EACnBA,EAAC,IAAU5D,EAEX4D,EAAEzD,MAAQH,EAAMG,MAChByD,EAAE3D,QAAUA,EAEZ2D,EAAC,KAAU,EAEI,MAAXA,EAAEnD,QAAemD,EAAEnD,MAAQiC,GAEV,MAAjBkB,EAAC,MACJA,EAAC,IAAeA,EAAEnD,OAGfoD,EAAKE,yBACRH,EAAEnD,MAAQoC,EACT,GACAe,EAAEnD,MACFoD,EAAKE,yBAAyBH,EAAEzD,MAAOyD,EAAEnD,QAEhCqD,GAAcF,EAAEI,oBAC1BJ,EAAEI,qBAIFJ,EAAEnD,MAAQmD,EAAC,MAAiBA,EAAEnD,MAAQmD,EAAC,IAAeA,EAAEnD,QAC7CqD,GAAcF,EAAEK,qBAC3BL,EAAEK,sBAGCzB,GAAYA,EAAWxC,GAEpB4D,EAAEM,OAAON,EAAEzD,MAAOyD,EAAEnD,MAAOR,EAClC,CAaD,SAASuD,EACRxD,EACAC,EACAkE,EACAC,EACAhB,EACAiB,EACAC,GAGA,GACU,MAATtE,IACU,IAAVA,IACU,IAAVA,GAtMgB,KAuMhBA,EAEA,MAzMgB,GA4MjB,IAAIuE,SAAmBvE,EAEvB,GAAiB,UAAbuE,EACH,MAAiB,YAAbA,EA/MY,GAgNI,UAAbA,EAAwBnG,EAAe4B,GAASA,EAhNvC,GAoNjB,GAAI4C,EAAQ5C,GAAQ,CACnB,IACCwE,EADGjB,EArNY,GAuNhBH,EAAM,IAAapD,EACnB,IAAK,IAAIvB,EAAI,EAAGA,EAAIuB,EAAM1B,OAAQG,IAAK,CACtC,IAAIgG,EAAQzE,EAAMvB,GAClB,GAAa,MAATgG,GAAiC,kBAATA,EAA5B,CAEA,MAAMC,EAAclB,EACnBiB,EACAxE,EACAkE,EACAC,EACAhB,EACAiB,EACAC,GAGyB,iBAAfI,EACVnB,GAAsBmB,GAEjBF,IACJA,EAAc,IAGXjB,GAAUiB,EAAYG,KAAKpB,GAE/BA,EA/Oc,GAiPVX,EAAQ8B,MACXF,GAAYG,aAAQD,GAEpBF,EAAYG,KAAKD,IAGnB,CAED,OAAIF,GACCjB,GAAUiB,EAAYG,KAAKpB,GACxBiB,GAGDjB,CACP,CAGD,QAA0B5D,IAAtBK,EAAM4E,YAA2B,MAlQpB,GAoQjB5E,EAAK,GAAWoD,EACZd,GAAYA,EAAWtC,GAE3B,IAAI6D,EAAO7D,EAAM6D,KAChB1D,EAAQH,EAAMG,MAGf,GAAmB,mBAAR0D,EAAoB,CAC9B,IACCgB,EACAtB,EACAuB,EAHGC,EAAO9E,EAIX,GAAI4D,IAASP,WAAU,CAEtB,GAAI,QAASnD,EAAO,CAEnB,IADA,IAAIzB,EAnRU,GAoRLD,EAAI,EAAGA,EAAI0B,EAAM6E,IAAI1G,OAAQG,IAGrC,GAFAC,GAAYyB,EAAM6E,IAAIvG,GAElB0B,EAAM8E,OAASxG,EAAI0B,EAAM8E,MAAM3G,OAAQ,CAC1C,IAAMoC,EAAQP,EAAM8E,MAAMxG,GAC1B,GAAa,MAATiC,EAAe,SAIF,iBAATA,QACgBf,IAAtBe,EAAMkE,cAA6BhC,EAAQlC,GAe5ChC,GAAYgC,EAbZhC,GAEC8E,EACC9C,EACAT,EACAkE,EACAC,EACApE,EACAqE,EACAC,EAMH,CAGF,OAAO5F,CACP,IAAU,qBAAsByB,EAGhC,MAAO,UAAS/B,EAAe+B,EAAM+E,kBAAoB,SAG1D3B,EAAWpD,EAAMgF,QACjB,KAAM,CAEN,GAAmB,OADnBN,EAAchB,EAAKgB,aACM,CACxB,IAAIO,EAAWnF,EAAQ4E,EAAYQ,KACnCN,EAAOK,EAAWA,EAASjF,MAAMO,MAAQmE,EAAYS,EACrD,CAED,IAAIC,EACH1B,EAAK3C,WAA6C,mBAAzB2C,EAAK3C,UAAUgD,OACzC,GAAIqB,EACHhC,EAA+BI,EAAqB3D,EAAO+E,GAC3DD,EAAY9E,EAAK,QACX,CACNA,EAAK,IAAc8E,EAAgC/E,EAClDC,EACA+E,GASD,IADA,IAAIS,EAAQ,EACLV,EAAS,KAAWU,IAAU,IACpCV,EAAS,KAAU,EAEftC,GAAYA,EAAWxC,GAE3BuD,EAAWM,EAAK4B,KAAKX,EAAW3E,EAAO4E,GAExCD,EAAS,KAAU,CACnB,CAMD,GAJiC,MAA7BA,EAAUY,kBACbzF,EAAU4C,EAAO,GAAI5C,EAAS6E,EAAUY,oBAIxCH,GACArC,UAAQyC,kBACP9B,EAAK+B,0BAA4Bd,EAAUe,mBAC3C,CAQDtC,EAJa,MAAZA,GACAA,EAASM,OAASP,YACF,MAAhBC,EAASuC,KACa,MAAtBvC,EAASpD,MAAM6E,IACgBzB,EAASpD,MAAMgF,SAAW5B,EAE1D,IACC,OAAOC,EACND,EACAtD,EACAkE,EACAC,EACApE,EACAqE,EACAC,EA2CD,CAzCC,MAAOyB,GASR,OARIlC,EAAK+B,2BACRd,EAAS,IAAejB,EAAK+B,yBAAyBG,IAGnDjB,EAAUe,mBACbf,EAAUe,kBAAkBE,EAAKrD,GAG9BoC,EAAS,KACZvB,EAAWI,EAAqB3D,EAAOC,GAGN,OAFjC6E,EAAY9E,EAAK,KAEH0F,kBACbzF,EAAU4C,EAAO,GAAI5C,EAAS6E,EAAUY,oBAUlClC,EAFPD,EAJa,MAAZA,GACAA,EAASM,OAASP,YACF,MAAhBC,EAASuC,KACa,MAAtBvC,EAASpD,MAAM6E,IACgBzB,EAASpD,MAAMgF,SAAW5B,EAIzDtD,EACAkE,EACAC,EACApE,EACAqE,EACAC,IAvZW,EA4Zb,CA9CD,QA+CK/B,GAAWA,EAAUvC,GACzBA,EAAK,GAAW,KAEZyC,GAAaA,EAAYzC,EAC7B,CACD,CACD,CASDuD,EAJa,MAAZA,GACAA,EAASM,OAASP,YACF,MAAhBC,EAASuC,KACa,MAAtBvC,EAASpD,MAAM6E,IACgBzB,EAASpD,MAAMgF,SAAW5B,EAE1D,IAEC,IAAMlF,EAAMmF,EACXD,EACAtD,EACAkE,EACAC,EACApE,EACAqE,EACAC,GASD,OANI/B,GAAWA,EAAUvC,GAEzBA,EAAK,GAAW,KAEZkD,UAAQC,SAASD,UAAQC,QAAQnD,GAE9B3B,CAyDP,CAxDC,MAAO2H,GACR,IAAK3B,GAAaC,GAAYA,EAAS2B,QAAS,CAC/C,IAAIC,EAAM5B,EAAS2B,QAAQD,EAAOhG,EAAO,SAACyE,UACzCjB,EACCiB,EACAxE,EACAkE,EACAC,EACApE,EACAqE,EACAC,EARuC,GAYzC,QAAY3E,IAARuG,EAAmB,OAAOA,EAE9B,IAAIC,EAAYjD,UAAO,IAEvB,OADIiD,GAAWA,EAAUH,EAAOhG,GAldlB,EAodd,CAED,IAAKqE,EAAW,MAAM2B,EAEtB,IAAKA,GAA8B,mBAAdA,EAAMjF,KAAoB,MAAMiF,EAgCrD,OAAOA,EAAMjF,KA9BgB,SAAvBqF,IACL,IACC,OAAO5C,EACND,EACAtD,EACAkE,EACAC,EACApE,EACAqE,EACAC,EAkBD,CAhBC,MAAO/C,GACR,IAAKA,GAAsB,mBAAVA,EAAER,KAAoB,MAAMQ,EAE7C,OAAOA,EAAER,KACR,kBACCyC,EACCD,EACAtD,EACAkE,EACAC,EACApE,EACAqE,EACAC,EARF,EAUA8B,EAED,CACD,EAGD,CACD,CAGD,IAECjB,EAFGjG,EAAI,IAAM2E,EACbwC,EA9fgB,GAigBjB,IAAK,IAAIhH,KAAQc,EAAO,CACvB,IAAIW,EAAIX,EAAMd,GAEd,GAAgB,mBAALyB,GAA4B,UAATzB,GAA6B,cAATA,EAAlD,CAIA,OAAQA,GACP,IAAK,WACJ8F,EAAWrE,EACX,SAGD,IAAK,MACL,IAAK,MACL,IAAK,SACL,IAAK,WACJ,SAGD,IAAK,UACJ,GAAI,QAASX,EAAO,SACpBd,EAAO,MACP,MACD,IAAK,YACJ,GAAI,UAAWc,EAAO,SACtBd,EAAO,QACP,MAGD,IAAK,iBACJA,EAAO,UACP,MACD,IAAK,kBACJA,EAAO,WACP,MAGD,IAAK,eACL,IAAK,QAEJ,OADAA,EAAO,QACCwE,GAEP,IAAK,WACJsB,EAAWrE,EACX,SAGD,IAAK,SACJsD,EAActD,EACd,SAGD,IAAK,SACAsD,GAAetD,GAAO,aAAcX,IACvCjB,GAAQ,aAIX,MAED,IAAK,0BACJmH,EAAOvF,GAAKA,EAAEwF,OACd,SAGD,IAAK,QACa,iBAANxF,IACVA,EAAI7B,EAAc6B,IAEnB,MACD,IAAK,gBACJzB,EAAO,iBACP,MACD,IAAK,YACJA,EAAO,aACP,MAED,QACC,GAAIvB,EAAwBS,KAAKc,GAChCA,EAAOA,EAAKC,QAAQxB,EAAyB,SAASyB,sBAC5C1B,EAAYU,KAAKc,GAC3B,SAEa,MAAZA,EAAK,KAAcpB,EAAgByB,IAAIL,IACnC,MAALyB,EAIUqD,EACNnG,EAAeO,KAAKc,KACvBA,EACU,YAATA,EACG,WACAA,EAAKC,QAAQ,WAAY,OAAOC,eAE3BxB,EAAgBQ,KAAKc,KAC/BA,EAAOA,EAAKE,eATZuB,GAzlBa,EAmmBb,EAKM,MAALA,IAAmB,IAANA,IAEf5B,GADS,IAAN4B,GAzmBW,KAymBGA,EACb5B,EAAI,IAAMG,EAGbH,EACA,IACAG,EACA,MACa,iBAALyB,EAAgB1C,EAAe0C,GAAKA,EAjnB/B,IAknBb,IA5GF,CA+GD,CAED,GAAIjD,EAAYU,KAAKsF,GAGpB,UAAUH,MAASG,sCAAwC3E,OA+B5D,GA5BImH,IAE2B,iBAAblB,EAEjBkB,EAAOjI,EAAe+G,GACA,MAAZA,IAAiC,IAAbA,IAAmC,IAAbA,IAIpDkB,EAAO7C,EACN2B,EACAlF,EAHS,QAAT4D,GAA4B,kBAATA,GAA4BM,EAK/CC,EACApE,EACAqE,EACAC,KAIE/B,GAAWA,EAAUvC,GAGzBA,EAAK,GAAW,KAEZyC,GAAaA,EAAYzC,IAGxBqG,GAAQE,EAAa7G,IAAImE,GAC7B,OAAO3E,EAAI,KAGZ,IAAMsH,EAAS,KAAO3C,EAAO,IACvB4C,GAAWvH,EAAI,IAErB,OAAI0D,EAAQyD,IAAeI,WAAaJ,GAAMG,IACtB,iBAARH,EAAyB,CAACI,GAAUJ,EAAMG,GACnDC,GAAWJ,EAAOG,CACzB,CAED,IAAMD,EAAe,IAAIrI,IAAI,CAC5B,OACA,OACA,KACA,MACA,UACA,QACA,KACA,MACA,QACA,SACA,OACA,OACA,QACA,SACA,QACA,QAIYgG,EAASnB,EACT2D,EAAuB3D,kIAxnBM/C,EAAOC,OAMhD,IAAMgD,EAAsBC,UAAO,IACnCA,UAAO,KAAiB,EAGxBZ,EAAaY,UAAO,IACpBX,EAAYW,UAAO,OACnBV,EAAaU,UAAO,IACpBT,EAAcS,UAAQC,QAEtB,IAAMC,EAASC,IAAEC,WAAU,MAf8B,OAgBzDF,EAAM,IAAa,CAACpD,uDAGIwD,EACtBxD,EACAC,GAAWyC,GACX,OACA/C,EACAyD,GACA,OACAzD,kBAPK4D,yBAUFX,EAAQW,2BAcJoD,EAASlD,KA7GD,kBAgGX+B,EAAQ,EACRmB,EAAWpD,yBAIdoD,EAASC,KACR,SAACC,UAAYA,GAAmC,mBAAjBA,EAAQ9F,IAAvC,IAEDyE,IAAU,EApBT,2CAsBiBsB,QAAQC,IAAIJ,qBAA9BA,EAAWK,EAA8BC,MADxC,EAED,qFAKK1D,CA5BJ,OA4BIA,CA5BJ,EA6BH,gBA/CwD,GAkDpDL,UAAO,KAAUA,UAAO,IAASlD,EAAO2C,GAC5CO,UAAO,IAAiBD,EACxBN,EAAUrE,OAAS,wBApDrB"}