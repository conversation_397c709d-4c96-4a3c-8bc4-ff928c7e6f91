[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "71"}, {"size": 12560, "mtime": 1750914757080, "results": "72", "hashOfConfig": "73"}, {"size": 17223, "mtime": 1750914371854, "results": "74", "hashOfConfig": "73"}, {"size": 14673, "mtime": 1750906110924, "results": "75", "hashOfConfig": "73"}, {"size": 5321, "mtime": 1750906802986, "results": "76", "hashOfConfig": "73"}, {"size": 1971, "mtime": 1750918481209, "results": "77", "hashOfConfig": "73"}, {"size": 2074, "mtime": 1750918572984, "results": "78", "hashOfConfig": "73"}, {"size": 2713, "mtime": 1750952924070, "results": "79", "hashOfConfig": "73"}, {"size": 171, "mtime": 1750921851894, "results": "80", "hashOfConfig": "73"}, {"size": 3714, "mtime": 1750921931408, "results": "81", "hashOfConfig": "73"}, {"size": 4489, "mtime": 1750930430193, "results": "82", "hashOfConfig": "73"}, {"size": 2376, "mtime": 1750906822203, "results": "83", "hashOfConfig": "73"}, {"size": 4403, "mtime": 1750924179468, "results": "84", "hashOfConfig": "73"}, {"size": 5614, "mtime": 1750951694652, "results": "85", "hashOfConfig": "73"}, {"size": 5593, "mtime": 1750952890101, "results": "86", "hashOfConfig": "73"}, {"size": 4878, "mtime": 1750944795050, "results": "87", "hashOfConfig": "73"}, {"size": 5538, "mtime": 1750952541605, "results": "88", "hashOfConfig": "73"}, {"size": 2484, "mtime": 1750938669998, "results": "89", "hashOfConfig": "73"}, {"size": 14489, "mtime": 1750918356378, "results": "90", "hashOfConfig": "73"}, {"size": 8836, "mtime": 1750919626197, "results": "91", "hashOfConfig": "73"}, {"size": 13839, "mtime": 1750908773390, "results": "92", "hashOfConfig": "73"}, {"size": 915, "mtime": 1750923464798, "results": "93", "hashOfConfig": "73"}, {"size": 11754, "mtime": 1750944837061, "results": "94", "hashOfConfig": "73"}, {"size": 7642, "mtime": 1750951120332, "results": "95", "hashOfConfig": "73"}, {"size": 10389, "mtime": 1750945315721, "results": "96", "hashOfConfig": "73"}, {"size": 20983, "mtime": 1751006626024, "results": "97", "hashOfConfig": "73"}, {"size": 21004, "mtime": 1750945519465, "results": "98", "hashOfConfig": "73"}, {"size": 16612, "mtime": 1750952813935, "results": "99", "hashOfConfig": "73"}, {"size": 4543, "mtime": 1750930937103, "results": "100", "hashOfConfig": "73"}, {"size": 12354, "mtime": 1750939410243, "results": "101", "hashOfConfig": "73"}, {"size": 9803, "mtime": 1750916566176, "results": "102", "hashOfConfig": "73"}, {"size": 1425, "mtime": 1750903550616, "results": "103", "hashOfConfig": "73"}, {"size": 845, "mtime": 1750908285683, "results": "104", "hashOfConfig": "73"}, {"size": 3063, "mtime": 1750925211983, "results": "105", "hashOfConfig": "73"}, {"size": 505, "mtime": 1750908273441, "results": "106", "hashOfConfig": "73"}, {"size": 863, "mtime": 1750908296528, "results": "107", "hashOfConfig": "73"}, {"size": 5768, "mtime": 1750942157899, "results": "108", "hashOfConfig": "73"}, {"size": 4732, "mtime": 1750951108798, "results": "109", "hashOfConfig": "73"}, {"size": 9109, "mtime": 1750930558601, "results": "110", "hashOfConfig": "73"}, {"size": 6661, "mtime": 1750945557905, "results": "111", "hashOfConfig": "73"}, {"size": 4438, "mtime": 1750923424688, "results": "112", "hashOfConfig": "73"}, {"size": 867, "mtime": 1750922283437, "results": "113", "hashOfConfig": "73"}, {"size": 362, "mtime": 1750922147686, "results": "114", "hashOfConfig": "73"}, {"size": 8935, "mtime": 1750924218629, "results": "115", "hashOfConfig": "73"}, {"size": 3198, "mtime": 1750951009317, "results": "116", "hashOfConfig": "73"}, {"size": 2449, "mtime": 1750942881883, "results": "117", "hashOfConfig": "73"}, {"size": 7038, "mtime": 1751006553710, "results": "118", "hashOfConfig": "73"}, {"size": 5059, "mtime": 1750930729612, "results": "119", "hashOfConfig": "73"}, {"size": 921, "mtime": 1750903252798, "results": "120", "hashOfConfig": "73"}, {"size": 6818, "mtime": 1750903357994, "results": "121", "hashOfConfig": "73"}, {"size": 1667, "mtime": 1750903308052, "results": "122", "hashOfConfig": "73"}, {"size": 2141, "mtime": 1750921803605, "results": "123", "hashOfConfig": "73"}, {"size": 4994, "mtime": 1750952472858, "results": "124", "hashOfConfig": "73"}, {"size": 3406, "mtime": 1750921782108, "results": "125", "hashOfConfig": "73"}, {"size": 720, "mtime": 1750903327281, "results": "126", "hashOfConfig": "73"}, {"size": 3866, "mtime": 1750984404444, "results": "127", "hashOfConfig": "73"}, {"size": 2238, "mtime": 1750995712168, "results": "128", "hashOfConfig": "73"}, {"size": 4072, "mtime": 1751001257292, "results": "129", "hashOfConfig": "73"}, {"size": 5143, "mtime": 1750984193831, "results": "130", "hashOfConfig": "73"}, {"size": 1022, "mtime": 1750984456438, "results": "131", "hashOfConfig": "73"}, {"size": 11751, "mtime": 1751005153199, "results": "132", "hashOfConfig": "73"}, {"size": 2237, "mtime": 1750949131424, "results": "133", "hashOfConfig": "73"}, {"size": 3202, "mtime": 1750953105864, "results": "134", "hashOfConfig": "73"}, {"size": 7991, "mtime": 1750984356171, "results": "135", "hashOfConfig": "73"}, {"size": 6764, "mtime": 1751005731451, "results": "136", "hashOfConfig": "73"}, {"size": 4621, "mtime": 1751005744888, "results": "137", "hashOfConfig": "73"}, {"size": 12431, "mtime": 1751004268664, "results": "138", "hashOfConfig": "73"}, {"size": 3687, "mtime": 1750984442753, "results": "139", "hashOfConfig": "73"}, {"size": 8199, "mtime": 1751005585384, "results": "140", "hashOfConfig": "73"}, {"size": 3470, "mtime": 1750984382081, "results": "141", "hashOfConfig": "73"}, {"size": 3931, "mtime": 1751002578968, "results": "142", "hashOfConfig": "73"}, {"size": 3989, "mtime": 1750984256539, "results": "143", "hashOfConfig": "73"}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "eypjqx", {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["357", "358", "359", "360", "361", "362", "363", "364", "365", "366"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["367", "368", "369", "370", "371", "372"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["373", "374", "375", "376", "377"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["378", "379"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["380", "381"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["382"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["383", "384"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["385", "386", "387"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["388"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["389"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["390"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["391"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["392"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["393", "394"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["395", "396", "397"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["398", "399", "400"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["401", "402", "403", "404", "405", "406"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["407", "408", "409", "410"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["411", "412", "413", "414"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["415", "416", "417", "418"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", ["419"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["420", "421", "422"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["423", "424"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["425", "426", "427", "428"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["429"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["430", "431", "432", "433"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["434"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["435"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["436"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", ["437"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["438", "439"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["440", "441", "442", "443"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["444", "445", "446", "447", "448"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["449", "450"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["451", "452"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["453", "454", "455"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["456"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["457"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], {"ruleId": "458", "severity": 2, "message": "459", "line": 11, "column": 3, "nodeType": null, "messageId": "460", "endLine": 11, "endColumn": 8}, {"ruleId": "458", "severity": 2, "message": "461", "line": 16, "column": 3, "nodeType": null, "messageId": "460", "endLine": 16, "endColumn": 8}, {"ruleId": "458", "severity": 2, "message": "462", "line": 17, "column": 3, "nodeType": null, "messageId": "460", "endLine": 17, "endColumn": 11}, {"ruleId": "458", "severity": 2, "message": "463", "line": 20, "column": 3, "nodeType": null, "messageId": "460", "endLine": 20, "endColumn": 7}, {"ruleId": "458", "severity": 2, "message": "464", "line": 25, "column": 7, "nodeType": null, "messageId": "460", "endLine": 25, "endColumn": 21}, {"ruleId": "465", "severity": 1, "message": "466", "line": 48, "column": 6, "nodeType": "467", "endLine": 48, "endColumn": 17, "suggestions": "468"}, {"ruleId": "458", "severity": 2, "message": "469", "line": 62, "column": 14, "nodeType": null, "messageId": "460", "endLine": 62, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "470", "line": 69, "column": 9, "nodeType": null, "messageId": "460", "endLine": 69, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "471", "line": 78, "column": 9, "nodeType": null, "messageId": "460", "endLine": 78, "endColumn": 24}, {"ruleId": "458", "severity": 2, "message": "472", "line": 91, "column": 9, "nodeType": null, "messageId": "460", "endLine": 91, "endColumn": 27}, {"ruleId": "458", "severity": 2, "message": "473", "line": 17, "column": 3, "nodeType": null, "messageId": "460", "endLine": 17, "endColumn": 17}, {"ruleId": "465", "severity": 1, "message": "474", "line": 61, "column": 6, "nodeType": "467", "endLine": 61, "endColumn": 20, "suggestions": "475"}, {"ruleId": "458", "severity": 2, "message": "469", "line": 78, "column": 14, "nodeType": null, "messageId": "460", "endLine": 78, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "469", "line": 111, "column": 14, "nodeType": null, "messageId": "460", "endLine": 111, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "469", "line": 137, "column": 14, "nodeType": null, "messageId": "460", "endLine": 137, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "477", "line": 304, "column": 27, "nodeType": "478", "endLine": 308, "endColumn": 29}, {"ruleId": "458", "severity": 2, "message": "479", "line": 74, "column": 9, "nodeType": null, "messageId": "460", "endLine": 74, "endColumn": 15}, {"ruleId": "458", "severity": 2, "message": "480", "line": 88, "column": 14, "nodeType": null, "messageId": "460", "endLine": 88, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "480", "line": 104, "column": 14, "nodeType": null, "messageId": "460", "endLine": 104, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "477", "line": 166, "column": 15, "nodeType": "478", "endLine": 170, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "477", "line": 260, "column": 19, "nodeType": "478", "endLine": 265, "endColumn": 21}, {"ruleId": "481", "severity": 2, "message": "482", "line": 19, "column": 18, "nodeType": "483", "messageId": "484", "endLine": 19, "endColumn": 21, "suggestions": "485"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 39, "column": 22, "nodeType": "483", "messageId": "484", "endLine": 39, "endColumn": 25, "suggestions": "486"}, {"ruleId": "458", "severity": 2, "message": "487", "line": 8, "column": 27, "nodeType": null, "messageId": "460", "endLine": 8, "endColumn": 34}, {"ruleId": "481", "severity": 2, "message": "482", "line": 96, "column": 23, "nodeType": "483", "messageId": "484", "endLine": 96, "endColumn": 26, "suggestions": "488"}, {"ruleId": "458", "severity": 2, "message": "487", "line": 6, "column": 27, "nodeType": null, "messageId": "460", "endLine": 6, "endColumn": 34}, {"ruleId": "481", "severity": 2, "message": "482", "line": 125, "column": 20, "nodeType": "483", "messageId": "484", "endLine": 125, "endColumn": 23, "suggestions": "489"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 163, "column": 70, "nodeType": "483", "messageId": "484", "endLine": 163, "endColumn": 73, "suggestions": "490"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 22, "column": 18, "nodeType": "483", "messageId": "484", "endLine": 22, "endColumn": 21, "suggestions": "491"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 59, "column": 22, "nodeType": "483", "messageId": "484", "endLine": 59, "endColumn": 25, "suggestions": "492"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 164, "column": 70, "nodeType": "483", "messageId": "484", "endLine": 164, "endColumn": 73, "suggestions": "493"}, {"ruleId": "458", "severity": 2, "message": "480", "line": 56, "column": 14, "nodeType": null, "messageId": "460", "endLine": 56, "endColumn": 19}, {"ruleId": "465", "severity": 1, "message": "494", "line": 54, "column": 6, "nodeType": "467", "endLine": 54, "endColumn": 19, "suggestions": "495"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 41, "column": 82, "nodeType": "483", "messageId": "484", "endLine": 41, "endColumn": 85, "suggestions": "496"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "497", "line": 251, "column": 26}, {"ruleId": "458", "severity": 2, "message": "469", "line": 122, "column": 14, "nodeType": null, "messageId": "460", "endLine": 122, "endColumn": 17}, {"ruleId": "465", "severity": 1, "message": "498", "line": 55, "column": 6, "nodeType": "467", "endLine": 55, "endColumn": 49, "suggestions": "499"}, {"ruleId": "458", "severity": 2, "message": "469", "line": 70, "column": 14, "nodeType": null, "messageId": "460", "endLine": 70, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "500", "line": 11, "column": 3, "nodeType": null, "messageId": "460", "endLine": 11, "endColumn": 7}, {"ruleId": "458", "severity": 2, "message": "501", "line": 18, "column": 3, "nodeType": null, "messageId": "460", "endLine": 18, "endColumn": 7}, {"ruleId": "476", "severity": 1, "message": "477", "line": 97, "column": 19, "nodeType": "478", "endLine": 101, "endColumn": 21}, {"ruleId": "458", "severity": 2, "message": "502", "line": 10, "column": 10, "nodeType": null, "messageId": "460", "endLine": 10, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "464", "line": 25, "column": 7, "nodeType": null, "messageId": "460", "endLine": 25, "endColumn": 21}, {"ruleId": "458", "severity": 2, "message": "469", "line": 115, "column": 14, "nodeType": null, "messageId": "460", "endLine": 115, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "503", "line": 12, "column": 3, "nodeType": null, "messageId": "460", "endLine": 12, "endColumn": 7}, {"ruleId": "458", "severity": 2, "message": "504", "line": 15, "column": 3, "nodeType": null, "messageId": "460", "endLine": 15, "endColumn": 6}, {"ruleId": "458", "severity": 2, "message": "469", "line": 94, "column": 14, "nodeType": null, "messageId": "460", "endLine": 94, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "469", "line": 111, "column": 14, "nodeType": null, "messageId": "460", "endLine": 111, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "469", "line": 128, "column": 14, "nodeType": null, "messageId": "460", "endLine": 128, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "477", "line": 197, "column": 23, "nodeType": "478", "endLine": 201, "endColumn": 25}, {"ruleId": "458", "severity": 2, "message": "505", "line": 55, "column": 26, "nodeType": null, "messageId": "460", "endLine": 55, "endColumn": 32}, {"ruleId": "458", "severity": 2, "message": "506", "line": 74, "column": 10, "nodeType": null, "messageId": "460", "endLine": 74, "endColumn": 27}, {"ruleId": "458", "severity": 2, "message": "507", "line": 74, "column": 29, "nodeType": null, "messageId": "460", "endLine": 74, "endColumn": 49}, {"ruleId": "476", "severity": 1, "message": "477", "line": 311, "column": 21, "nodeType": "478", "endLine": 315, "endColumn": 23}, {"ruleId": "458", "severity": 2, "message": "480", "line": 27, "column": 14, "nodeType": null, "messageId": "460", "endLine": 27, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "480", "line": 46, "column": 14, "nodeType": null, "messageId": "460", "endLine": 46, "endColumn": 19}, {"ruleId": "481", "severity": 2, "message": "482", "line": 63, "column": 42, "nodeType": "483", "messageId": "484", "endLine": 63, "endColumn": 45, "suggestions": "508"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 64, "column": 42, "nodeType": "483", "messageId": "484", "endLine": 64, "endColumn": 45, "suggestions": "509"}, {"ruleId": "465", "severity": 1, "message": "510", "line": 37, "column": 6, "nodeType": "467", "endLine": 37, "endColumn": 17, "suggestions": "511"}, {"ruleId": "458", "severity": 2, "message": "469", "line": 53, "column": 14, "nodeType": null, "messageId": "460", "endLine": 53, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "469", "line": 73, "column": 14, "nodeType": null, "messageId": "460", "endLine": 73, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "477", "line": 167, "column": 21, "nodeType": "478", "endLine": 171, "endColumn": 23}, {"ruleId": "476", "severity": 1, "message": "477", "line": 60, "column": 15, "nodeType": "478", "endLine": 64, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "480", "line": 44, "column": 14, "nodeType": null, "messageId": "460", "endLine": 44, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "480", "line": 84, "column": 14, "nodeType": null, "messageId": "460", "endLine": 84, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "480", "line": 111, "column": 14, "nodeType": null, "messageId": "460", "endLine": 111, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "477", "line": 61, "column": 13, "nodeType": "478", "endLine": 65, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "477", "line": 93, "column": 21, "nodeType": "478", "endLine": 97, "endColumn": 23}, {"ruleId": "458", "severity": 2, "message": "512", "line": 5, "column": 27, "nodeType": null, "messageId": "460", "endLine": 5, "endColumn": 34}, {"ruleId": "458", "severity": 2, "message": "513", "line": 5, "column": 36, "nodeType": null, "messageId": "460", "endLine": 5, "endColumn": 46}, {"ruleId": "465", "severity": 1, "message": "514", "line": 55, "column": 6, "nodeType": "467", "endLine": 55, "endColumn": 14, "suggestions": "515"}, {"ruleId": "476", "severity": 1, "message": "477", "line": 156, "column": 13, "nodeType": "478", "endLine": 160, "endColumn": 15}, {"ruleId": "481", "severity": 2, "message": "482", "line": 4, "column": 34, "nodeType": "483", "messageId": "484", "endLine": 4, "endColumn": 37, "suggestions": "516"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 65, "column": 60, "nodeType": "483", "messageId": "484", "endLine": 65, "endColumn": 63, "suggestions": "517"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 145, "column": 31, "nodeType": "483", "messageId": "484", "endLine": 145, "endColumn": 34, "suggestions": "518"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 151, "column": 26, "nodeType": "483", "messageId": "484", "endLine": 151, "endColumn": 29, "suggestions": "519"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 152, "column": 26, "nodeType": "483", "messageId": "484", "endLine": 152, "endColumn": 29, "suggestions": "520"}, {"ruleId": "458", "severity": 2, "message": "521", "line": 1, "column": 8, "nodeType": null, "messageId": "460", "endLine": 1, "endColumn": 16}, {"ruleId": "458", "severity": 2, "message": "522", "line": 27, "column": 13, "nodeType": null, "messageId": "460", "endLine": 27, "endColumn": 26}, {"ruleId": "458", "severity": 2, "message": "523", "line": 5, "column": 8, "nodeType": null, "messageId": "460", "endLine": 5, "endColumn": 12}, {"ruleId": "458", "severity": 2, "message": "524", "line": 7, "column": 58, "nodeType": null, "messageId": "460", "endLine": 7, "endColumn": 71}, {"ruleId": "458", "severity": 2, "message": "525", "line": 88, "column": 11, "nodeType": null, "messageId": "460", "endLine": 88, "endColumn": 14}, {"ruleId": "481", "severity": 2, "message": "482", "line": 157, "column": 25, "nodeType": "483", "messageId": "484", "endLine": 157, "endColumn": 28, "suggestions": "526"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 38, "column": 18, "nodeType": "483", "messageId": "484", "endLine": 38, "endColumn": 21, "suggestions": "527"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 51, "column": 22, "nodeType": "483", "messageId": "484", "endLine": 51, "endColumn": 25, "suggestions": "528"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 88, "column": 52, "nodeType": "483", "messageId": "484", "endLine": 88, "endColumn": 55, "suggestions": "529"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 89, "column": 52, "nodeType": "483", "messageId": "484", "endLine": 89, "endColumn": 55, "suggestions": "530"}, {"ruleId": "458", "severity": 2, "message": "531", "line": 18, "column": 17, "nodeType": null, "messageId": "460", "endLine": 18, "endColumn": 24}, {"ruleId": "481", "severity": 2, "message": "482", "line": 19, "column": 38, "nodeType": "483", "messageId": "484", "endLine": 19, "endColumn": 41, "suggestions": "532"}, {"ruleId": "465", "severity": 1, "message": "533", "line": 35, "column": 6, "nodeType": "467", "endLine": 35, "endColumn": 23, "suggestions": "534"}, {"ruleId": "458", "severity": 2, "message": "469", "line": 61, "column": 14, "nodeType": null, "messageId": "460", "endLine": 61, "endColumn": 17}, {"ruleId": "458", "severity": 2, "message": "469", "line": 85, "column": 14, "nodeType": null, "messageId": "460", "endLine": 85, "endColumn": 17}, {"ruleId": "481", "severity": 2, "message": "482", "line": 16, "column": 36, "nodeType": "483", "messageId": "484", "endLine": 16, "endColumn": 39, "suggestions": "535"}, {"ruleId": "465", "severity": 1, "message": "536", "line": 32, "column": 6, "nodeType": "467", "endLine": 32, "endColumn": 22, "suggestions": "537"}, {"ruleId": "481", "severity": 2, "message": "482", "line": 18, "column": 36, "nodeType": "483", "messageId": "484", "endLine": 18, "endColumn": 39, "suggestions": "538"}, {"ruleId": "465", "severity": 1, "message": "536", "line": 32, "column": 6, "nodeType": "467", "endLine": 32, "endColumn": 22, "suggestions": "539"}, {"ruleId": "458", "severity": 2, "message": "531", "line": 12, "column": 17, "nodeType": null, "messageId": "460", "endLine": 12, "endColumn": 24}, {"ruleId": "481", "severity": 2, "message": "482", "line": 13, "column": 36, "nodeType": "483", "messageId": "484", "endLine": 13, "endColumn": 39, "suggestions": "540"}, {"ruleId": "465", "severity": 1, "message": "536", "line": 28, "column": 6, "nodeType": "467", "endLine": 28, "endColumn": 22, "suggestions": "541"}, {"ruleId": "458", "severity": 2, "message": "480", "line": 80, "column": 14, "nodeType": null, "messageId": "460", "endLine": 80, "endColumn": 19}, {"ruleId": "458", "severity": 2, "message": "469", "line": 54, "column": 14, "nodeType": null, "messageId": "460", "endLine": 54, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["542"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["543"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["544", "545"], ["546", "547"], "'request' is defined but never used.", ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], "React Hook useEffect has a missing dependency: 'fetchCategoryData'. Either include it or remove the dependency array.", ["560"], ["561", "562"], "Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["563"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'apiClient' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", "'status' is assigned a value but never used.", "'currentScreenshot' is assigned a value but never used.", "'setCurrentScreenshot' is assigned a value but never used.", ["564", "565"], ["566", "567"], "React Hook useEffect has a missing dependency: 'fetchToolDetails'. Either include it or remove the dependency array.", ["568"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["569"], ["570", "571"], ["572", "573"], ["574", "575"], ["576", "577"], ["578", "579"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'STRIPE_CONFIG' is defined but never used.", "'now' is assigned a value but never used.", ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], "'session' is assigned a value but never used.", ["590", "591"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["592"], ["593", "594"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["595"], ["596", "597"], ["598"], ["599", "600"], ["601"], {"desc": "602", "fix": "603"}, {"desc": "604", "fix": "605"}, {"messageId": "606", "fix": "607", "desc": "608"}, {"messageId": "609", "fix": "610", "desc": "611"}, {"messageId": "606", "fix": "612", "desc": "608"}, {"messageId": "609", "fix": "613", "desc": "611"}, {"messageId": "606", "fix": "614", "desc": "608"}, {"messageId": "609", "fix": "615", "desc": "611"}, {"messageId": "606", "fix": "616", "desc": "608"}, {"messageId": "609", "fix": "617", "desc": "611"}, {"messageId": "606", "fix": "618", "desc": "608"}, {"messageId": "609", "fix": "619", "desc": "611"}, {"messageId": "606", "fix": "620", "desc": "608"}, {"messageId": "609", "fix": "621", "desc": "611"}, {"messageId": "606", "fix": "622", "desc": "608"}, {"messageId": "609", "fix": "623", "desc": "611"}, {"messageId": "606", "fix": "624", "desc": "608"}, {"messageId": "609", "fix": "625", "desc": "611"}, {"desc": "626", "fix": "627"}, {"messageId": "606", "fix": "628", "desc": "608"}, {"messageId": "609", "fix": "629", "desc": "611"}, {"desc": "630", "fix": "631"}, {"messageId": "606", "fix": "632", "desc": "608"}, {"messageId": "609", "fix": "633", "desc": "611"}, {"messageId": "606", "fix": "634", "desc": "608"}, {"messageId": "609", "fix": "635", "desc": "611"}, {"desc": "636", "fix": "637"}, {"desc": "638", "fix": "639"}, {"messageId": "606", "fix": "640", "desc": "608"}, {"messageId": "609", "fix": "641", "desc": "611"}, {"messageId": "606", "fix": "642", "desc": "608"}, {"messageId": "609", "fix": "643", "desc": "611"}, {"messageId": "606", "fix": "644", "desc": "608"}, {"messageId": "609", "fix": "645", "desc": "611"}, {"messageId": "606", "fix": "646", "desc": "608"}, {"messageId": "609", "fix": "647", "desc": "611"}, {"messageId": "606", "fix": "648", "desc": "608"}, {"messageId": "609", "fix": "649", "desc": "611"}, {"messageId": "606", "fix": "650", "desc": "608"}, {"messageId": "609", "fix": "651", "desc": "611"}, {"messageId": "606", "fix": "652", "desc": "608"}, {"messageId": "609", "fix": "653", "desc": "611"}, {"messageId": "606", "fix": "654", "desc": "608"}, {"messageId": "609", "fix": "655", "desc": "611"}, {"messageId": "606", "fix": "656", "desc": "608"}, {"messageId": "609", "fix": "657", "desc": "611"}, {"messageId": "606", "fix": "658", "desc": "608"}, {"messageId": "609", "fix": "659", "desc": "611"}, {"messageId": "606", "fix": "660", "desc": "608"}, {"messageId": "609", "fix": "661", "desc": "611"}, {"desc": "662", "fix": "663"}, {"messageId": "606", "fix": "664", "desc": "608"}, {"messageId": "609", "fix": "665", "desc": "611"}, {"desc": "666", "fix": "667"}, {"messageId": "606", "fix": "668", "desc": "608"}, {"messageId": "609", "fix": "669", "desc": "611"}, {"desc": "666", "fix": "670"}, {"messageId": "606", "fix": "671", "desc": "608"}, {"messageId": "609", "fix": "672", "desc": "611"}, {"desc": "666", "fix": "673"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "674", "text": "675"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "676", "text": "677"}, "suggestUnknown", {"range": "678", "text": "679"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "680", "text": "681"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "682", "text": "679"}, {"range": "683", "text": "681"}, {"range": "684", "text": "679"}, {"range": "685", "text": "681"}, {"range": "686", "text": "679"}, {"range": "687", "text": "681"}, {"range": "688", "text": "679"}, {"range": "689", "text": "681"}, {"range": "690", "text": "679"}, {"range": "691", "text": "681"}, {"range": "692", "text": "679"}, {"range": "693", "text": "681"}, {"range": "694", "text": "679"}, {"range": "695", "text": "681"}, "Update the dependencies array to be: [fetchCategoryData, params.slug]", {"range": "696", "text": "697"}, {"range": "698", "text": "679"}, {"range": "699", "text": "681"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "700", "text": "701"}, {"range": "702", "text": "679"}, {"range": "703", "text": "681"}, {"range": "704", "text": "679"}, {"range": "705", "text": "681"}, "Update the dependencies array to be: [fetchToolDetails, params.id]", {"range": "706", "text": "707"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "708", "text": "709"}, {"range": "710", "text": "679"}, {"range": "711", "text": "681"}, {"range": "712", "text": "679"}, {"range": "713", "text": "681"}, {"range": "714", "text": "679"}, {"range": "715", "text": "681"}, {"range": "716", "text": "679"}, {"range": "717", "text": "681"}, {"range": "718", "text": "679"}, {"range": "719", "text": "681"}, {"range": "720", "text": "679"}, {"range": "721", "text": "681"}, {"range": "722", "text": "679"}, {"range": "723", "text": "681"}, {"range": "724", "text": "679"}, {"range": "725", "text": "681"}, {"range": "726", "text": "679"}, {"range": "727", "text": "681"}, {"range": "728", "text": "679"}, {"range": "729", "text": "681"}, {"range": "730", "text": "679"}, {"range": "731", "text": "681"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "732", "text": "733"}, {"range": "734", "text": "679"}, {"range": "735", "text": "681"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "736", "text": "737"}, {"range": "738", "text": "679"}, {"range": "739", "text": "681"}, {"range": "740", "text": "737"}, {"range": "741", "text": "679"}, {"range": "742", "text": "681"}, {"range": "743", "text": "737"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1183, 1186], [1183, 1186], [2591, 2594], [2591, 2594], [3029, 3032], [3029, 3032], [4020, 4023], [4020, 4023], [802, 805], [802, 805], [1656, 1659], [1656, 1659], [4318, 4321], [4318, 4321], [1651, 1664], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, params.slug]", [1084, 1087], [1084, 1087], [1532, 1575], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [1084, 1095], "[fetchToolDetails, params.id]", [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624], [4092, 4095], [4092, 4095], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [751, 754], [751, 754], [1155, 1172], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875]]