[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx": "72"}, {"size": 12560, "mtime": 1750914757080, "results": "73", "hashOfConfig": "74"}, {"size": 17223, "mtime": 1750914371854, "results": "75", "hashOfConfig": "74"}, {"size": 14673, "mtime": 1750906110924, "results": "76", "hashOfConfig": "74"}, {"size": 5321, "mtime": 1750906802986, "results": "77", "hashOfConfig": "74"}, {"size": 1971, "mtime": 1750918481209, "results": "78", "hashOfConfig": "74"}, {"size": 2074, "mtime": 1750918572984, "results": "79", "hashOfConfig": "74"}, {"size": 2713, "mtime": 1750952924070, "results": "80", "hashOfConfig": "74"}, {"size": 171, "mtime": 1750921851894, "results": "81", "hashOfConfig": "74"}, {"size": 3714, "mtime": 1750921931408, "results": "82", "hashOfConfig": "74"}, {"size": 4489, "mtime": 1750930430193, "results": "83", "hashOfConfig": "74"}, {"size": 2376, "mtime": 1750906822203, "results": "84", "hashOfConfig": "74"}, {"size": 4403, "mtime": 1750924179468, "results": "85", "hashOfConfig": "74"}, {"size": 5614, "mtime": 1750951694652, "results": "86", "hashOfConfig": "74"}, {"size": 6593, "mtime": 1751007584388, "results": "87", "hashOfConfig": "74"}, {"size": 4878, "mtime": 1750944795050, "results": "88", "hashOfConfig": "74"}, {"size": 5538, "mtime": 1750952541605, "results": "89", "hashOfConfig": "74"}, {"size": 2484, "mtime": 1750938669998, "results": "90", "hashOfConfig": "74"}, {"size": 14489, "mtime": 1750918356378, "results": "91", "hashOfConfig": "74"}, {"size": 8836, "mtime": 1750919626197, "results": "92", "hashOfConfig": "74"}, {"size": 13839, "mtime": 1750908773390, "results": "93", "hashOfConfig": "74"}, {"size": 915, "mtime": 1750923464798, "results": "94", "hashOfConfig": "74"}, {"size": 11754, "mtime": 1750944837061, "results": "95", "hashOfConfig": "74"}, {"size": 7642, "mtime": 1750951120332, "results": "96", "hashOfConfig": "74"}, {"size": 10389, "mtime": 1750945315721, "results": "97", "hashOfConfig": "74"}, {"size": 20668, "mtime": 1751007539870, "results": "98", "hashOfConfig": "74"}, {"size": 21004, "mtime": 1750945519465, "results": "99", "hashOfConfig": "74"}, {"size": 16612, "mtime": 1750952813935, "results": "100", "hashOfConfig": "74"}, {"size": 4543, "mtime": 1750930937103, "results": "101", "hashOfConfig": "74"}, {"size": 12354, "mtime": 1750939410243, "results": "102", "hashOfConfig": "74"}, {"size": 9803, "mtime": 1750916566176, "results": "103", "hashOfConfig": "74"}, {"size": 1425, "mtime": 1750903550616, "results": "104", "hashOfConfig": "74"}, {"size": 845, "mtime": 1750908285683, "results": "105", "hashOfConfig": "74"}, {"size": 3063, "mtime": 1750925211983, "results": "106", "hashOfConfig": "74"}, {"size": 505, "mtime": 1750908273441, "results": "107", "hashOfConfig": "74"}, {"size": 863, "mtime": 1750908296528, "results": "108", "hashOfConfig": "74"}, {"size": 5768, "mtime": 1750942157899, "results": "109", "hashOfConfig": "74"}, {"size": 4732, "mtime": 1750951108798, "results": "110", "hashOfConfig": "74"}, {"size": 9109, "mtime": 1750930558601, "results": "111", "hashOfConfig": "74"}, {"size": 6661, "mtime": 1750945557905, "results": "112", "hashOfConfig": "74"}, {"size": 4438, "mtime": 1750923424688, "results": "113", "hashOfConfig": "74"}, {"size": 867, "mtime": 1750922283437, "results": "114", "hashOfConfig": "74"}, {"size": 362, "mtime": 1750922147686, "results": "115", "hashOfConfig": "74"}, {"size": 8935, "mtime": 1750924218629, "results": "116", "hashOfConfig": "74"}, {"size": 3198, "mtime": 1750951009317, "results": "117", "hashOfConfig": "74"}, {"size": 2449, "mtime": 1750942881883, "results": "118", "hashOfConfig": "74"}, {"size": 7038, "mtime": 1751006553710, "results": "119", "hashOfConfig": "74"}, {"size": 5059, "mtime": 1750930729612, "results": "120", "hashOfConfig": "74"}, {"size": 921, "mtime": 1750903252798, "results": "121", "hashOfConfig": "74"}, {"size": 6818, "mtime": 1750903357994, "results": "122", "hashOfConfig": "74"}, {"size": 1667, "mtime": 1750903308052, "results": "123", "hashOfConfig": "74"}, {"size": 2141, "mtime": 1750921803605, "results": "124", "hashOfConfig": "74"}, {"size": 4994, "mtime": 1750952472858, "results": "125", "hashOfConfig": "74"}, {"size": 3406, "mtime": 1750921782108, "results": "126", "hashOfConfig": "74"}, {"size": 720, "mtime": 1750903327281, "results": "127", "hashOfConfig": "74"}, {"size": 3866, "mtime": 1750984404444, "results": "128", "hashOfConfig": "74"}, {"size": 2238, "mtime": 1750995712168, "results": "129", "hashOfConfig": "74"}, {"size": 4072, "mtime": 1751001257292, "results": "130", "hashOfConfig": "74"}, {"size": 5143, "mtime": 1750984193831, "results": "131", "hashOfConfig": "74"}, {"size": 1022, "mtime": 1750984456438, "results": "132", "hashOfConfig": "74"}, {"size": 11751, "mtime": 1751005153199, "results": "133", "hashOfConfig": "74"}, {"size": 2237, "mtime": 1750949131424, "results": "134", "hashOfConfig": "74"}, {"size": 3202, "mtime": 1750953105864, "results": "135", "hashOfConfig": "74"}, {"size": 7991, "mtime": 1750984356171, "results": "136", "hashOfConfig": "74"}, {"size": 6764, "mtime": 1751005731451, "results": "137", "hashOfConfig": "74"}, {"size": 4621, "mtime": 1751005744888, "results": "138", "hashOfConfig": "74"}, {"size": 12431, "mtime": 1751004268664, "results": "139", "hashOfConfig": "74"}, {"size": 3687, "mtime": 1750984442753, "results": "140", "hashOfConfig": "74"}, {"size": 8199, "mtime": 1751005585384, "results": "141", "hashOfConfig": "74"}, {"size": 3470, "mtime": 1750984382081, "results": "142", "hashOfConfig": "74"}, {"size": 3931, "mtime": 1751002578968, "results": "143", "hashOfConfig": "74"}, {"size": 3989, "mtime": 1750984256539, "results": "144", "hashOfConfig": "74"}, {"size": 27088, "mtime": 1751007522724, "results": "145", "hashOfConfig": "74"}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "eypjqx", {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["362", "363", "364", "365", "366", "367", "368", "369", "370", "371"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["372", "373", "374", "375", "376", "377"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["378", "379", "380", "381", "382"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["383", "384"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["385", "386"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["387"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["388", "389"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["390", "391", "392"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["393"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["394"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["395"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["396"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["397"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["398", "399"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["400", "401", "402"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["403"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["404", "405", "406", "407", "408", "409"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["410", "411", "412", "413"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["414", "415", "416", "417"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["418", "419", "420", "421"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", ["422"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["423", "424", "425"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["426", "427"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["428", "429", "430", "431"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["432"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["433", "434", "435", "436"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["437"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["438"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["439"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", ["440"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["441", "442"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["443", "444", "445", "446"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["447", "448", "449", "450", "451"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["452", "453"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["454", "455"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["456", "457", "458"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["459"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["460"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx", ["461", "462", "463"], [], {"ruleId": "464", "severity": 2, "message": "465", "line": 11, "column": 3, "nodeType": null, "messageId": "466", "endLine": 11, "endColumn": 8}, {"ruleId": "464", "severity": 2, "message": "467", "line": 16, "column": 3, "nodeType": null, "messageId": "466", "endLine": 16, "endColumn": 8}, {"ruleId": "464", "severity": 2, "message": "468", "line": 17, "column": 3, "nodeType": null, "messageId": "466", "endLine": 17, "endColumn": 11}, {"ruleId": "464", "severity": 2, "message": "469", "line": 20, "column": 3, "nodeType": null, "messageId": "466", "endLine": 20, "endColumn": 7}, {"ruleId": "464", "severity": 2, "message": "470", "line": 25, "column": 7, "nodeType": null, "messageId": "466", "endLine": 25, "endColumn": 21}, {"ruleId": "471", "severity": 1, "message": "472", "line": 48, "column": 6, "nodeType": "473", "endLine": 48, "endColumn": 17, "suggestions": "474"}, {"ruleId": "464", "severity": 2, "message": "475", "line": 62, "column": 14, "nodeType": null, "messageId": "466", "endLine": 62, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "476", "line": 69, "column": 9, "nodeType": null, "messageId": "466", "endLine": 69, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "477", "line": 78, "column": 9, "nodeType": null, "messageId": "466", "endLine": 78, "endColumn": 24}, {"ruleId": "464", "severity": 2, "message": "478", "line": 91, "column": 9, "nodeType": null, "messageId": "466", "endLine": 91, "endColumn": 27}, {"ruleId": "464", "severity": 2, "message": "479", "line": 17, "column": 3, "nodeType": null, "messageId": "466", "endLine": 17, "endColumn": 17}, {"ruleId": "471", "severity": 1, "message": "480", "line": 61, "column": 6, "nodeType": "473", "endLine": 61, "endColumn": 20, "suggestions": "481"}, {"ruleId": "464", "severity": 2, "message": "475", "line": 78, "column": 14, "nodeType": null, "messageId": "466", "endLine": 78, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "475", "line": 111, "column": 14, "nodeType": null, "messageId": "466", "endLine": 111, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "475", "line": 137, "column": 14, "nodeType": null, "messageId": "466", "endLine": 137, "endColumn": 17}, {"ruleId": "482", "severity": 1, "message": "483", "line": 304, "column": 27, "nodeType": "484", "endLine": 308, "endColumn": 29}, {"ruleId": "464", "severity": 2, "message": "485", "line": 74, "column": 9, "nodeType": null, "messageId": "466", "endLine": 74, "endColumn": 15}, {"ruleId": "464", "severity": 2, "message": "486", "line": 88, "column": 14, "nodeType": null, "messageId": "466", "endLine": 88, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "486", "line": 104, "column": 14, "nodeType": null, "messageId": "466", "endLine": 104, "endColumn": 19}, {"ruleId": "482", "severity": 1, "message": "483", "line": 166, "column": 15, "nodeType": "484", "endLine": 170, "endColumn": 17}, {"ruleId": "482", "severity": 1, "message": "483", "line": 260, "column": 19, "nodeType": "484", "endLine": 265, "endColumn": 21}, {"ruleId": "487", "severity": 2, "message": "488", "line": 19, "column": 18, "nodeType": "489", "messageId": "490", "endLine": 19, "endColumn": 21, "suggestions": "491"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 39, "column": 22, "nodeType": "489", "messageId": "490", "endLine": 39, "endColumn": 25, "suggestions": "492"}, {"ruleId": "464", "severity": 2, "message": "493", "line": 8, "column": 27, "nodeType": null, "messageId": "466", "endLine": 8, "endColumn": 34}, {"ruleId": "487", "severity": 2, "message": "488", "line": 96, "column": 23, "nodeType": "489", "messageId": "490", "endLine": 96, "endColumn": 26, "suggestions": "494"}, {"ruleId": "464", "severity": 2, "message": "493", "line": 6, "column": 27, "nodeType": null, "messageId": "466", "endLine": 6, "endColumn": 34}, {"ruleId": "487", "severity": 2, "message": "488", "line": 158, "column": 20, "nodeType": "489", "messageId": "490", "endLine": 158, "endColumn": 23, "suggestions": "495"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 196, "column": 70, "nodeType": "489", "messageId": "490", "endLine": 196, "endColumn": 73, "suggestions": "496"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 22, "column": 18, "nodeType": "489", "messageId": "490", "endLine": 22, "endColumn": 21, "suggestions": "497"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 59, "column": 22, "nodeType": "489", "messageId": "490", "endLine": 59, "endColumn": 25, "suggestions": "498"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 164, "column": 70, "nodeType": "489", "messageId": "490", "endLine": 164, "endColumn": 73, "suggestions": "499"}, {"ruleId": "464", "severity": 2, "message": "486", "line": 56, "column": 14, "nodeType": null, "messageId": "466", "endLine": 56, "endColumn": 19}, {"ruleId": "471", "severity": 1, "message": "500", "line": 54, "column": 6, "nodeType": "473", "endLine": 54, "endColumn": 19, "suggestions": "501"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 41, "column": 82, "nodeType": "489", "messageId": "490", "endLine": 41, "endColumn": 85, "suggestions": "502"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "503", "line": 251, "column": 26}, {"ruleId": "464", "severity": 2, "message": "475", "line": 122, "column": 14, "nodeType": null, "messageId": "466", "endLine": 122, "endColumn": 17}, {"ruleId": "471", "severity": 1, "message": "504", "line": 55, "column": 6, "nodeType": "473", "endLine": 55, "endColumn": 49, "suggestions": "505"}, {"ruleId": "464", "severity": 2, "message": "475", "line": 70, "column": 14, "nodeType": null, "messageId": "466", "endLine": 70, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "506", "line": 11, "column": 3, "nodeType": null, "messageId": "466", "endLine": 11, "endColumn": 7}, {"ruleId": "464", "severity": 2, "message": "507", "line": 18, "column": 3, "nodeType": null, "messageId": "466", "endLine": 18, "endColumn": 7}, {"ruleId": "482", "severity": 1, "message": "483", "line": 97, "column": 19, "nodeType": "484", "endLine": 101, "endColumn": 21}, {"ruleId": "464", "severity": 2, "message": "486", "line": 104, "column": 14, "nodeType": null, "messageId": "466", "endLine": 104, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "508", "line": 12, "column": 3, "nodeType": null, "messageId": "466", "endLine": 12, "endColumn": 7}, {"ruleId": "464", "severity": 2, "message": "509", "line": 15, "column": 3, "nodeType": null, "messageId": "466", "endLine": 15, "endColumn": 6}, {"ruleId": "464", "severity": 2, "message": "475", "line": 94, "column": 14, "nodeType": null, "messageId": "466", "endLine": 94, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "475", "line": 111, "column": 14, "nodeType": null, "messageId": "466", "endLine": 111, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "475", "line": 128, "column": 14, "nodeType": null, "messageId": "466", "endLine": 128, "endColumn": 17}, {"ruleId": "482", "severity": 1, "message": "483", "line": 197, "column": 23, "nodeType": "484", "endLine": 201, "endColumn": 25}, {"ruleId": "464", "severity": 2, "message": "510", "line": 55, "column": 26, "nodeType": null, "messageId": "466", "endLine": 55, "endColumn": 32}, {"ruleId": "464", "severity": 2, "message": "511", "line": 74, "column": 10, "nodeType": null, "messageId": "466", "endLine": 74, "endColumn": 27}, {"ruleId": "464", "severity": 2, "message": "512", "line": 74, "column": 29, "nodeType": null, "messageId": "466", "endLine": 74, "endColumn": 49}, {"ruleId": "482", "severity": 1, "message": "483", "line": 311, "column": 21, "nodeType": "484", "endLine": 315, "endColumn": 23}, {"ruleId": "464", "severity": 2, "message": "486", "line": 27, "column": 14, "nodeType": null, "messageId": "466", "endLine": 27, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "486", "line": 46, "column": 14, "nodeType": null, "messageId": "466", "endLine": 46, "endColumn": 19}, {"ruleId": "487", "severity": 2, "message": "488", "line": 63, "column": 42, "nodeType": "489", "messageId": "490", "endLine": 63, "endColumn": 45, "suggestions": "513"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 64, "column": 42, "nodeType": "489", "messageId": "490", "endLine": 64, "endColumn": 45, "suggestions": "514"}, {"ruleId": "471", "severity": 1, "message": "515", "line": 37, "column": 6, "nodeType": "473", "endLine": 37, "endColumn": 17, "suggestions": "516"}, {"ruleId": "464", "severity": 2, "message": "475", "line": 53, "column": 14, "nodeType": null, "messageId": "466", "endLine": 53, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "475", "line": 73, "column": 14, "nodeType": null, "messageId": "466", "endLine": 73, "endColumn": 17}, {"ruleId": "482", "severity": 1, "message": "483", "line": 167, "column": 21, "nodeType": "484", "endLine": 171, "endColumn": 23}, {"ruleId": "482", "severity": 1, "message": "483", "line": 60, "column": 15, "nodeType": "484", "endLine": 64, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "486", "line": 44, "column": 14, "nodeType": null, "messageId": "466", "endLine": 44, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "486", "line": 84, "column": 14, "nodeType": null, "messageId": "466", "endLine": 84, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "486", "line": 111, "column": 14, "nodeType": null, "messageId": "466", "endLine": 111, "endColumn": 19}, {"ruleId": "482", "severity": 1, "message": "483", "line": 61, "column": 13, "nodeType": "484", "endLine": 65, "endColumn": 15}, {"ruleId": "482", "severity": 1, "message": "483", "line": 93, "column": 21, "nodeType": "484", "endLine": 97, "endColumn": 23}, {"ruleId": "464", "severity": 2, "message": "517", "line": 5, "column": 27, "nodeType": null, "messageId": "466", "endLine": 5, "endColumn": 34}, {"ruleId": "464", "severity": 2, "message": "518", "line": 5, "column": 36, "nodeType": null, "messageId": "466", "endLine": 5, "endColumn": 46}, {"ruleId": "471", "severity": 1, "message": "519", "line": 55, "column": 6, "nodeType": "473", "endLine": 55, "endColumn": 14, "suggestions": "520"}, {"ruleId": "482", "severity": 1, "message": "483", "line": 156, "column": 13, "nodeType": "484", "endLine": 160, "endColumn": 15}, {"ruleId": "487", "severity": 2, "message": "488", "line": 4, "column": 34, "nodeType": "489", "messageId": "490", "endLine": 4, "endColumn": 37, "suggestions": "521"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 65, "column": 60, "nodeType": "489", "messageId": "490", "endLine": 65, "endColumn": 63, "suggestions": "522"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 145, "column": 31, "nodeType": "489", "messageId": "490", "endLine": 145, "endColumn": 34, "suggestions": "523"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 151, "column": 26, "nodeType": "489", "messageId": "490", "endLine": 151, "endColumn": 29, "suggestions": "524"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 152, "column": 26, "nodeType": "489", "messageId": "490", "endLine": 152, "endColumn": 29, "suggestions": "525"}, {"ruleId": "464", "severity": 2, "message": "526", "line": 1, "column": 8, "nodeType": null, "messageId": "466", "endLine": 1, "endColumn": 16}, {"ruleId": "464", "severity": 2, "message": "527", "line": 27, "column": 13, "nodeType": null, "messageId": "466", "endLine": 27, "endColumn": 26}, {"ruleId": "464", "severity": 2, "message": "528", "line": 5, "column": 8, "nodeType": null, "messageId": "466", "endLine": 5, "endColumn": 12}, {"ruleId": "464", "severity": 2, "message": "529", "line": 7, "column": 58, "nodeType": null, "messageId": "466", "endLine": 7, "endColumn": 71}, {"ruleId": "464", "severity": 2, "message": "530", "line": 88, "column": 11, "nodeType": null, "messageId": "466", "endLine": 88, "endColumn": 14}, {"ruleId": "487", "severity": 2, "message": "488", "line": 157, "column": 25, "nodeType": "489", "messageId": "490", "endLine": 157, "endColumn": 28, "suggestions": "531"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 38, "column": 18, "nodeType": "489", "messageId": "490", "endLine": 38, "endColumn": 21, "suggestions": "532"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 51, "column": 22, "nodeType": "489", "messageId": "490", "endLine": 51, "endColumn": 25, "suggestions": "533"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 88, "column": 52, "nodeType": "489", "messageId": "490", "endLine": 88, "endColumn": 55, "suggestions": "534"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 89, "column": 52, "nodeType": "489", "messageId": "490", "endLine": 89, "endColumn": 55, "suggestions": "535"}, {"ruleId": "464", "severity": 2, "message": "536", "line": 18, "column": 17, "nodeType": null, "messageId": "466", "endLine": 18, "endColumn": 24}, {"ruleId": "487", "severity": 2, "message": "488", "line": 19, "column": 38, "nodeType": "489", "messageId": "490", "endLine": 19, "endColumn": 41, "suggestions": "537"}, {"ruleId": "471", "severity": 1, "message": "538", "line": 35, "column": 6, "nodeType": "473", "endLine": 35, "endColumn": 23, "suggestions": "539"}, {"ruleId": "464", "severity": 2, "message": "475", "line": 61, "column": 14, "nodeType": null, "messageId": "466", "endLine": 61, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "475", "line": 85, "column": 14, "nodeType": null, "messageId": "466", "endLine": 85, "endColumn": 17}, {"ruleId": "487", "severity": 2, "message": "488", "line": 16, "column": 36, "nodeType": "489", "messageId": "490", "endLine": 16, "endColumn": 39, "suggestions": "540"}, {"ruleId": "471", "severity": 1, "message": "541", "line": 32, "column": 6, "nodeType": "473", "endLine": 32, "endColumn": 22, "suggestions": "542"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 18, "column": 36, "nodeType": "489", "messageId": "490", "endLine": 18, "endColumn": 39, "suggestions": "543"}, {"ruleId": "471", "severity": 1, "message": "541", "line": 32, "column": 6, "nodeType": "473", "endLine": 32, "endColumn": 22, "suggestions": "544"}, {"ruleId": "464", "severity": 2, "message": "536", "line": 12, "column": 17, "nodeType": null, "messageId": "466", "endLine": 12, "endColumn": 24}, {"ruleId": "487", "severity": 2, "message": "488", "line": 13, "column": 36, "nodeType": "489", "messageId": "490", "endLine": 13, "endColumn": 39, "suggestions": "545"}, {"ruleId": "471", "severity": 1, "message": "541", "line": 28, "column": 6, "nodeType": "473", "endLine": 28, "endColumn": 22, "suggestions": "546"}, {"ruleId": "464", "severity": 2, "message": "486", "line": 80, "column": 14, "nodeType": null, "messageId": "466", "endLine": 80, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "475", "line": 54, "column": 14, "nodeType": null, "messageId": "466", "endLine": 54, "endColumn": 17}, {"ruleId": "464", "severity": 2, "message": "547", "line": 43, "column": 10, "nodeType": null, "messageId": "466", "endLine": 43, "endColumn": 18}, {"ruleId": "482", "severity": 1, "message": "483", "line": 499, "column": 19, "nodeType": "484", "endLine": 503, "endColumn": 21}, {"ruleId": "482", "severity": 1, "message": "483", "line": 659, "column": 21, "nodeType": "484", "endLine": 663, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["548"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["549"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["550", "551"], ["552", "553"], "'request' is defined but never used.", ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], ["564", "565"], "React Hook useEffect has a missing dependency: 'fetchCategoryData'. Either include it or remove the dependency array.", ["566"], ["567", "568"], "Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["569"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", "'status' is assigned a value but never used.", "'currentScreenshot' is assigned a value but never used.", "'setCurrentScreenshot' is assigned a value but never used.", ["570", "571"], ["572", "573"], "React Hook useEffect has a missing dependency: 'fetchToolDetails'. Either include it or remove the dependency array.", ["574"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["575"], ["576", "577"], ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'STRIPE_CONFIG' is defined but never used.", "'now' is assigned a value but never used.", ["586", "587"], ["588", "589"], ["590", "591"], ["592", "593"], ["594", "595"], "'session' is assigned a value but never used.", ["596", "597"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["598"], ["599", "600"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["601"], ["602", "603"], ["604"], ["605", "606"], ["607"], "'logoFile' is assigned a value but never used.", {"desc": "608", "fix": "609"}, {"desc": "610", "fix": "611"}, {"messageId": "612", "fix": "613", "desc": "614"}, {"messageId": "615", "fix": "616", "desc": "617"}, {"messageId": "612", "fix": "618", "desc": "614"}, {"messageId": "615", "fix": "619", "desc": "617"}, {"messageId": "612", "fix": "620", "desc": "614"}, {"messageId": "615", "fix": "621", "desc": "617"}, {"messageId": "612", "fix": "622", "desc": "614"}, {"messageId": "615", "fix": "623", "desc": "617"}, {"messageId": "612", "fix": "624", "desc": "614"}, {"messageId": "615", "fix": "625", "desc": "617"}, {"messageId": "612", "fix": "626", "desc": "614"}, {"messageId": "615", "fix": "627", "desc": "617"}, {"messageId": "612", "fix": "628", "desc": "614"}, {"messageId": "615", "fix": "629", "desc": "617"}, {"messageId": "612", "fix": "630", "desc": "614"}, {"messageId": "615", "fix": "631", "desc": "617"}, {"desc": "632", "fix": "633"}, {"messageId": "612", "fix": "634", "desc": "614"}, {"messageId": "615", "fix": "635", "desc": "617"}, {"desc": "636", "fix": "637"}, {"messageId": "612", "fix": "638", "desc": "614"}, {"messageId": "615", "fix": "639", "desc": "617"}, {"messageId": "612", "fix": "640", "desc": "614"}, {"messageId": "615", "fix": "641", "desc": "617"}, {"desc": "642", "fix": "643"}, {"desc": "644", "fix": "645"}, {"messageId": "612", "fix": "646", "desc": "614"}, {"messageId": "615", "fix": "647", "desc": "617"}, {"messageId": "612", "fix": "648", "desc": "614"}, {"messageId": "615", "fix": "649", "desc": "617"}, {"messageId": "612", "fix": "650", "desc": "614"}, {"messageId": "615", "fix": "651", "desc": "617"}, {"messageId": "612", "fix": "652", "desc": "614"}, {"messageId": "615", "fix": "653", "desc": "617"}, {"messageId": "612", "fix": "654", "desc": "614"}, {"messageId": "615", "fix": "655", "desc": "617"}, {"messageId": "612", "fix": "656", "desc": "614"}, {"messageId": "615", "fix": "657", "desc": "617"}, {"messageId": "612", "fix": "658", "desc": "614"}, {"messageId": "615", "fix": "659", "desc": "617"}, {"messageId": "612", "fix": "660", "desc": "614"}, {"messageId": "615", "fix": "661", "desc": "617"}, {"messageId": "612", "fix": "662", "desc": "614"}, {"messageId": "615", "fix": "663", "desc": "617"}, {"messageId": "612", "fix": "664", "desc": "614"}, {"messageId": "615", "fix": "665", "desc": "617"}, {"messageId": "612", "fix": "666", "desc": "614"}, {"messageId": "615", "fix": "667", "desc": "617"}, {"desc": "668", "fix": "669"}, {"messageId": "612", "fix": "670", "desc": "614"}, {"messageId": "615", "fix": "671", "desc": "617"}, {"desc": "672", "fix": "673"}, {"messageId": "612", "fix": "674", "desc": "614"}, {"messageId": "615", "fix": "675", "desc": "617"}, {"desc": "672", "fix": "676"}, {"messageId": "612", "fix": "677", "desc": "614"}, {"messageId": "615", "fix": "678", "desc": "617"}, {"desc": "672", "fix": "679"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "680", "text": "681"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "682", "text": "683"}, "suggestUnknown", {"range": "684", "text": "685"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "686", "text": "687"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "688", "text": "685"}, {"range": "689", "text": "687"}, {"range": "690", "text": "685"}, {"range": "691", "text": "687"}, {"range": "692", "text": "685"}, {"range": "693", "text": "687"}, {"range": "694", "text": "685"}, {"range": "695", "text": "687"}, {"range": "696", "text": "685"}, {"range": "697", "text": "687"}, {"range": "698", "text": "685"}, {"range": "699", "text": "687"}, {"range": "700", "text": "685"}, {"range": "701", "text": "687"}, "Update the dependencies array to be: [fetchCategoryData, params.slug]", {"range": "702", "text": "703"}, {"range": "704", "text": "685"}, {"range": "705", "text": "687"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "706", "text": "707"}, {"range": "708", "text": "685"}, {"range": "709", "text": "687"}, {"range": "710", "text": "685"}, {"range": "711", "text": "687"}, "Update the dependencies array to be: [fetchToolDetails, params.id]", {"range": "712", "text": "713"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "714", "text": "715"}, {"range": "716", "text": "685"}, {"range": "717", "text": "687"}, {"range": "718", "text": "685"}, {"range": "719", "text": "687"}, {"range": "720", "text": "685"}, {"range": "721", "text": "687"}, {"range": "722", "text": "685"}, {"range": "723", "text": "687"}, {"range": "724", "text": "685"}, {"range": "725", "text": "687"}, {"range": "726", "text": "685"}, {"range": "727", "text": "687"}, {"range": "728", "text": "685"}, {"range": "729", "text": "687"}, {"range": "730", "text": "685"}, {"range": "731", "text": "687"}, {"range": "732", "text": "685"}, {"range": "733", "text": "687"}, {"range": "734", "text": "685"}, {"range": "735", "text": "687"}, {"range": "736", "text": "685"}, {"range": "737", "text": "687"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "738", "text": "739"}, {"range": "740", "text": "685"}, {"range": "741", "text": "687"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "742", "text": "743"}, {"range": "744", "text": "685"}, {"range": "745", "text": "687"}, {"range": "746", "text": "743"}, {"range": "747", "text": "685"}, {"range": "748", "text": "687"}, {"range": "749", "text": "743"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1183, 1186], [1183, 1186], [2591, 2594], [2591, 2594], [3853, 3856], [3853, 3856], [4844, 4847], [4844, 4847], [802, 805], [802, 805], [1656, 1659], [1656, 1659], [4318, 4321], [4318, 4321], [1651, 1664], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, params.slug]", [1084, 1087], [1084, 1087], [1532, 1575], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [1084, 1095], "[fetchToolDetails, params.id]", [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624], [4092, 4095], [4092, 4095], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [751, 754], [751, 754], [1155, 1172], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875]]