[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "54"}, {"size": 12560, "mtime": 1750914757080, "results": "55", "hashOfConfig": "56"}, {"size": 17223, "mtime": 1750914371854, "results": "57", "hashOfConfig": "56"}, {"size": 14673, "mtime": 1750906110924, "results": "58", "hashOfConfig": "56"}, {"size": 5321, "mtime": 1750906802986, "results": "59", "hashOfConfig": "56"}, {"size": 1971, "mtime": 1750918481209, "results": "60", "hashOfConfig": "56"}, {"size": 2074, "mtime": 1750918572984, "results": "61", "hashOfConfig": "56"}, {"size": 2646, "mtime": 1750906732590, "results": "62", "hashOfConfig": "56"}, {"size": 171, "mtime": 1750921851894, "results": "63", "hashOfConfig": "56"}, {"size": 3714, "mtime": 1750921931408, "results": "64", "hashOfConfig": "56"}, {"size": 4489, "mtime": 1750930430193, "results": "65", "hashOfConfig": "56"}, {"size": 2376, "mtime": 1750906822203, "results": "66", "hashOfConfig": "56"}, {"size": 4403, "mtime": 1750924179468, "results": "67", "hashOfConfig": "56"}, {"size": 3101, "mtime": 1750924031614, "results": "68", "hashOfConfig": "56"}, {"size": 4398, "mtime": 1750918517868, "results": "69", "hashOfConfig": "56"}, {"size": 4878, "mtime": 1750944795050, "results": "70", "hashOfConfig": "56"}, {"size": 5417, "mtime": 1750939265191, "results": "71", "hashOfConfig": "56"}, {"size": 2484, "mtime": 1750938669998, "results": "72", "hashOfConfig": "56"}, {"size": 14489, "mtime": 1750918356378, "results": "73", "hashOfConfig": "56"}, {"size": 8836, "mtime": 1750919626197, "results": "74", "hashOfConfig": "56"}, {"size": 13839, "mtime": 1750908773390, "results": "75", "hashOfConfig": "56"}, {"size": 915, "mtime": 1750923464798, "results": "76", "hashOfConfig": "56"}, {"size": 11754, "mtime": 1750944837061, "results": "77", "hashOfConfig": "56"}, {"size": 8255, "mtime": 1750946241148, "results": "78", "hashOfConfig": "56"}, {"size": 10389, "mtime": 1750945315721, "results": "79", "hashOfConfig": "56"}, {"size": 14424, "mtime": 1750945398055, "results": "80", "hashOfConfig": "56"}, {"size": 21004, "mtime": 1750945519465, "results": "81", "hashOfConfig": "56"}, {"size": 17009, "mtime": 1750941372430, "results": "82", "hashOfConfig": "56"}, {"size": 4543, "mtime": 1750930937103, "results": "83", "hashOfConfig": "56"}, {"size": 12354, "mtime": 1750939410243, "results": "84", "hashOfConfig": "56"}, {"size": 9803, "mtime": 1750916566176, "results": "85", "hashOfConfig": "56"}, {"size": 1425, "mtime": 1750903550616, "results": "86", "hashOfConfig": "56"}, {"size": 845, "mtime": 1750908285683, "results": "87", "hashOfConfig": "56"}, {"size": 3063, "mtime": 1750925211983, "results": "88", "hashOfConfig": "56"}, {"size": 505, "mtime": 1750908273441, "results": "89", "hashOfConfig": "56"}, {"size": 863, "mtime": 1750908296528, "results": "90", "hashOfConfig": "56"}, {"size": 5768, "mtime": 1750942157899, "results": "91", "hashOfConfig": "56"}, {"size": 4516, "mtime": 1750924425109, "results": "92", "hashOfConfig": "56"}, {"size": 9109, "mtime": 1750930558601, "results": "93", "hashOfConfig": "56"}, {"size": 6661, "mtime": 1750945557905, "results": "94", "hashOfConfig": "56"}, {"size": 4438, "mtime": 1750923424688, "results": "95", "hashOfConfig": "56"}, {"size": 867, "mtime": 1750922283437, "results": "96", "hashOfConfig": "56"}, {"size": 362, "mtime": 1750922147686, "results": "97", "hashOfConfig": "56"}, {"size": 8935, "mtime": 1750924218629, "results": "98", "hashOfConfig": "56"}, {"size": 2667, "mtime": 1750924153400, "results": "99", "hashOfConfig": "56"}, {"size": 2449, "mtime": 1750942881883, "results": "100", "hashOfConfig": "56"}, {"size": 6203, "mtime": 1750944810180, "results": "101", "hashOfConfig": "56"}, {"size": 5059, "mtime": 1750930729612, "results": "102", "hashOfConfig": "56"}, {"size": 921, "mtime": 1750903252798, "results": "103", "hashOfConfig": "56"}, {"size": 6818, "mtime": 1750903357994, "results": "104", "hashOfConfig": "56"}, {"size": 1667, "mtime": 1750903308052, "results": "105", "hashOfConfig": "56"}, {"size": 2141, "mtime": 1750921803605, "results": "106", "hashOfConfig": "56"}, {"size": 3822, "mtime": 1750938585851, "results": "107", "hashOfConfig": "56"}, {"size": 3406, "mtime": 1750921782108, "results": "108", "hashOfConfig": "56"}, {"size": 720, "mtime": 1750903327281, "results": "109", "hashOfConfig": "56"}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1pgdh66", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["272", "273", "274", "275", "276", "277", "278", "279", "280", "281"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["282", "283", "284", "285", "286", "287"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["288", "289", "290", "291", "292"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["293", "294"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["295", "296"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["297"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["298", "299"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["300", "301", "302"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["303"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["304"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["305"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["306"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["307"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["308", "309"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["310", "311", "312"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["313", "314"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["315", "316", "317", "318", "319", "320"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["321", "322", "323"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["324", "325", "326", "327"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["328", "329", "330", "331"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", ["332"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["333", "334", "335"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["336", "337"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["338", "339", "340", "341"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["342"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["343", "344", "345", "346"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["347"], [], {"ruleId": "348", "severity": 2, "message": "349", "line": 11, "column": 3, "nodeType": null, "messageId": "350", "endLine": 11, "endColumn": 8}, {"ruleId": "348", "severity": 2, "message": "351", "line": 16, "column": 3, "nodeType": null, "messageId": "350", "endLine": 16, "endColumn": 8}, {"ruleId": "348", "severity": 2, "message": "352", "line": 17, "column": 3, "nodeType": null, "messageId": "350", "endLine": 17, "endColumn": 11}, {"ruleId": "348", "severity": 2, "message": "353", "line": 20, "column": 3, "nodeType": null, "messageId": "350", "endLine": 20, "endColumn": 7}, {"ruleId": "348", "severity": 2, "message": "354", "line": 25, "column": 7, "nodeType": null, "messageId": "350", "endLine": 25, "endColumn": 21}, {"ruleId": "355", "severity": 1, "message": "356", "line": 48, "column": 6, "nodeType": "357", "endLine": 48, "endColumn": 17, "suggestions": "358"}, {"ruleId": "348", "severity": 2, "message": "359", "line": 62, "column": 14, "nodeType": null, "messageId": "350", "endLine": 62, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "360", "line": 69, "column": 9, "nodeType": null, "messageId": "350", "endLine": 69, "endColumn": 19}, {"ruleId": "348", "severity": 2, "message": "361", "line": 78, "column": 9, "nodeType": null, "messageId": "350", "endLine": 78, "endColumn": 24}, {"ruleId": "348", "severity": 2, "message": "362", "line": 91, "column": 9, "nodeType": null, "messageId": "350", "endLine": 91, "endColumn": 27}, {"ruleId": "348", "severity": 2, "message": "363", "line": 17, "column": 3, "nodeType": null, "messageId": "350", "endLine": 17, "endColumn": 17}, {"ruleId": "355", "severity": 1, "message": "364", "line": 61, "column": 6, "nodeType": "357", "endLine": 61, "endColumn": 20, "suggestions": "365"}, {"ruleId": "348", "severity": 2, "message": "359", "line": 78, "column": 14, "nodeType": null, "messageId": "350", "endLine": 78, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "359", "line": 111, "column": 14, "nodeType": null, "messageId": "350", "endLine": 111, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "359", "line": 137, "column": 14, "nodeType": null, "messageId": "350", "endLine": 137, "endColumn": 17}, {"ruleId": "366", "severity": 1, "message": "367", "line": 304, "column": 27, "nodeType": "368", "endLine": 308, "endColumn": 29}, {"ruleId": "348", "severity": 2, "message": "369", "line": 74, "column": 9, "nodeType": null, "messageId": "350", "endLine": 74, "endColumn": 15}, {"ruleId": "348", "severity": 2, "message": "370", "line": 88, "column": 14, "nodeType": null, "messageId": "350", "endLine": 88, "endColumn": 19}, {"ruleId": "348", "severity": 2, "message": "370", "line": 104, "column": 14, "nodeType": null, "messageId": "350", "endLine": 104, "endColumn": 19}, {"ruleId": "366", "severity": 1, "message": "367", "line": 166, "column": 15, "nodeType": "368", "endLine": 170, "endColumn": 17}, {"ruleId": "366", "severity": 1, "message": "367", "line": 260, "column": 19, "nodeType": "368", "endLine": 265, "endColumn": 21}, {"ruleId": "371", "severity": 2, "message": "372", "line": 19, "column": 18, "nodeType": "373", "messageId": "374", "endLine": 19, "endColumn": 21, "suggestions": "375"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 39, "column": 22, "nodeType": "373", "messageId": "374", "endLine": 39, "endColumn": 25, "suggestions": "376"}, {"ruleId": "348", "severity": 2, "message": "377", "line": 8, "column": 27, "nodeType": null, "messageId": "350", "endLine": 8, "endColumn": 34}, {"ruleId": "371", "severity": 2, "message": "372", "line": 96, "column": 23, "nodeType": "373", "messageId": "374", "endLine": 96, "endColumn": 26, "suggestions": "378"}, {"ruleId": "348", "severity": 2, "message": "377", "line": 6, "column": 27, "nodeType": null, "messageId": "350", "endLine": 6, "endColumn": 34}, {"ruleId": "371", "severity": 2, "message": "372", "line": 88, "column": 20, "nodeType": "373", "messageId": "374", "endLine": 88, "endColumn": 23, "suggestions": "379"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 126, "column": 70, "nodeType": "373", "messageId": "374", "endLine": 126, "endColumn": 73, "suggestions": "380"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 22, "column": 18, "nodeType": "373", "messageId": "374", "endLine": 22, "endColumn": 21, "suggestions": "381"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 59, "column": 22, "nodeType": "373", "messageId": "374", "endLine": 59, "endColumn": 25, "suggestions": "382"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 164, "column": 70, "nodeType": "373", "messageId": "374", "endLine": 164, "endColumn": 73, "suggestions": "383"}, {"ruleId": "348", "severity": 2, "message": "370", "line": 56, "column": 14, "nodeType": null, "messageId": "350", "endLine": 56, "endColumn": 19}, {"ruleId": "355", "severity": 1, "message": "384", "line": 54, "column": 6, "nodeType": "357", "endLine": 54, "endColumn": 19, "suggestions": "385"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 41, "column": 82, "nodeType": "373", "messageId": "374", "endLine": 41, "endColumn": 85, "suggestions": "386"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "387", "line": 251, "column": 26}, {"ruleId": "348", "severity": 2, "message": "359", "line": 122, "column": 14, "nodeType": null, "messageId": "350", "endLine": 122, "endColumn": 17}, {"ruleId": "355", "severity": 1, "message": "388", "line": 55, "column": 6, "nodeType": "357", "endLine": 55, "endColumn": 49, "suggestions": "389"}, {"ruleId": "348", "severity": 2, "message": "359", "line": 73, "column": 14, "nodeType": null, "messageId": "350", "endLine": 73, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "390", "line": 11, "column": 3, "nodeType": null, "messageId": "350", "endLine": 11, "endColumn": 7}, {"ruleId": "348", "severity": 2, "message": "391", "line": 18, "column": 3, "nodeType": null, "messageId": "350", "endLine": 18, "endColumn": 7}, {"ruleId": "366", "severity": 1, "message": "367", "line": 97, "column": 19, "nodeType": "368", "endLine": 101, "endColumn": 21}, {"ruleId": "348", "severity": 2, "message": "354", "line": 25, "column": 7, "nodeType": null, "messageId": "350", "endLine": 25, "endColumn": 21}, {"ruleId": "348", "severity": 2, "message": "359", "line": 108, "column": 14, "nodeType": null, "messageId": "350", "endLine": 108, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "392", "line": 12, "column": 3, "nodeType": null, "messageId": "350", "endLine": 12, "endColumn": 7}, {"ruleId": "348", "severity": 2, "message": "393", "line": 15, "column": 3, "nodeType": null, "messageId": "350", "endLine": 15, "endColumn": 6}, {"ruleId": "348", "severity": 2, "message": "359", "line": 94, "column": 14, "nodeType": null, "messageId": "350", "endLine": 94, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "359", "line": 111, "column": 14, "nodeType": null, "messageId": "350", "endLine": 111, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "359", "line": 128, "column": 14, "nodeType": null, "messageId": "350", "endLine": 128, "endColumn": 17}, {"ruleId": "366", "severity": 1, "message": "367", "line": 197, "column": 23, "nodeType": "368", "endLine": 201, "endColumn": 25}, {"ruleId": "348", "severity": 2, "message": "394", "line": 54, "column": 26, "nodeType": null, "messageId": "350", "endLine": 54, "endColumn": 32}, {"ruleId": "348", "severity": 2, "message": "395", "line": 72, "column": 10, "nodeType": null, "messageId": "350", "endLine": 72, "endColumn": 27}, {"ruleId": "366", "severity": 1, "message": "367", "line": 327, "column": 21, "nodeType": "368", "endLine": 331, "endColumn": 23}, {"ruleId": "348", "severity": 2, "message": "370", "line": 27, "column": 14, "nodeType": null, "messageId": "350", "endLine": 27, "endColumn": 19}, {"ruleId": "348", "severity": 2, "message": "370", "line": 46, "column": 14, "nodeType": null, "messageId": "350", "endLine": 46, "endColumn": 19}, {"ruleId": "371", "severity": 2, "message": "372", "line": 63, "column": 42, "nodeType": "373", "messageId": "374", "endLine": 63, "endColumn": 45, "suggestions": "396"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 64, "column": 42, "nodeType": "373", "messageId": "374", "endLine": 64, "endColumn": 45, "suggestions": "397"}, {"ruleId": "355", "severity": 1, "message": "398", "line": 37, "column": 6, "nodeType": "357", "endLine": 37, "endColumn": 17, "suggestions": "399"}, {"ruleId": "348", "severity": 2, "message": "359", "line": 53, "column": 14, "nodeType": null, "messageId": "350", "endLine": 53, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "359", "line": 73, "column": 14, "nodeType": null, "messageId": "350", "endLine": 73, "endColumn": 17}, {"ruleId": "366", "severity": 1, "message": "367", "line": 167, "column": 21, "nodeType": "368", "endLine": 171, "endColumn": 23}, {"ruleId": "366", "severity": 1, "message": "367", "line": 58, "column": 15, "nodeType": "368", "endLine": 62, "endColumn": 17}, {"ruleId": "348", "severity": 2, "message": "370", "line": 44, "column": 14, "nodeType": null, "messageId": "350", "endLine": 44, "endColumn": 19}, {"ruleId": "348", "severity": 2, "message": "370", "line": 84, "column": 14, "nodeType": null, "messageId": "350", "endLine": 84, "endColumn": 19}, {"ruleId": "348", "severity": 2, "message": "370", "line": 111, "column": 14, "nodeType": null, "messageId": "350", "endLine": 111, "endColumn": 19}, {"ruleId": "366", "severity": 1, "message": "367", "line": 61, "column": 13, "nodeType": "368", "endLine": 65, "endColumn": 15}, {"ruleId": "366", "severity": 1, "message": "367", "line": 93, "column": 21, "nodeType": "368", "endLine": 97, "endColumn": 23}, {"ruleId": "348", "severity": 2, "message": "400", "line": 5, "column": 27, "nodeType": null, "messageId": "350", "endLine": 5, "endColumn": 34}, {"ruleId": "348", "severity": 2, "message": "401", "line": 5, "column": 36, "nodeType": null, "messageId": "350", "endLine": 5, "endColumn": 46}, {"ruleId": "355", "severity": 1, "message": "402", "line": 55, "column": 6, "nodeType": "357", "endLine": 55, "endColumn": 14, "suggestions": "403"}, {"ruleId": "366", "severity": 1, "message": "367", "line": 156, "column": 13, "nodeType": "368", "endLine": 160, "endColumn": 15}, {"ruleId": "371", "severity": 2, "message": "372", "line": 4, "column": 34, "nodeType": "373", "messageId": "374", "endLine": 4, "endColumn": 37, "suggestions": "404"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 65, "column": 60, "nodeType": "373", "messageId": "374", "endLine": 65, "endColumn": 63, "suggestions": "405"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 145, "column": 31, "nodeType": "373", "messageId": "374", "endLine": 145, "endColumn": 34, "suggestions": "406"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 151, "column": 26, "nodeType": "373", "messageId": "374", "endLine": 151, "endColumn": 29, "suggestions": "407"}, {"ruleId": "371", "severity": 2, "message": "372", "line": 152, "column": 26, "nodeType": "373", "messageId": "374", "endLine": 152, "endColumn": 29, "suggestions": "408"}, {"ruleId": "348", "severity": 2, "message": "409", "line": 1, "column": 8, "nodeType": null, "messageId": "350", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["410"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["411"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["412", "413"], ["414", "415"], "'request' is defined but never used.", ["416", "417"], ["418", "419"], ["420", "421"], ["422", "423"], ["424", "425"], ["426", "427"], "React Hook useEffect has a missing dependency: 'fetchCategoryData'. Either include it or remove the dependency array.", ["428"], ["429", "430"], "Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["431"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", "'status' is assigned a value but never used.", "'currentScreenshot' is assigned a value but never used.", ["432", "433"], ["434", "435"], "React Hook useEffect has a missing dependency: 'fetchToolDetails'. Either include it or remove the dependency array.", ["436"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["437"], ["438", "439"], ["440", "441"], ["442", "443"], ["444", "445"], ["446", "447"], "'mongoose' is defined but never used.", {"desc": "448", "fix": "449"}, {"desc": "450", "fix": "451"}, {"messageId": "452", "fix": "453", "desc": "454"}, {"messageId": "455", "fix": "456", "desc": "457"}, {"messageId": "452", "fix": "458", "desc": "454"}, {"messageId": "455", "fix": "459", "desc": "457"}, {"messageId": "452", "fix": "460", "desc": "454"}, {"messageId": "455", "fix": "461", "desc": "457"}, {"messageId": "452", "fix": "462", "desc": "454"}, {"messageId": "455", "fix": "463", "desc": "457"}, {"messageId": "452", "fix": "464", "desc": "454"}, {"messageId": "455", "fix": "465", "desc": "457"}, {"messageId": "452", "fix": "466", "desc": "454"}, {"messageId": "455", "fix": "467", "desc": "457"}, {"messageId": "452", "fix": "468", "desc": "454"}, {"messageId": "455", "fix": "469", "desc": "457"}, {"messageId": "452", "fix": "470", "desc": "454"}, {"messageId": "455", "fix": "471", "desc": "457"}, {"desc": "472", "fix": "473"}, {"messageId": "452", "fix": "474", "desc": "454"}, {"messageId": "455", "fix": "475", "desc": "457"}, {"desc": "476", "fix": "477"}, {"messageId": "452", "fix": "478", "desc": "454"}, {"messageId": "455", "fix": "479", "desc": "457"}, {"messageId": "452", "fix": "480", "desc": "454"}, {"messageId": "455", "fix": "481", "desc": "457"}, {"desc": "482", "fix": "483"}, {"desc": "484", "fix": "485"}, {"messageId": "452", "fix": "486", "desc": "454"}, {"messageId": "455", "fix": "487", "desc": "457"}, {"messageId": "452", "fix": "488", "desc": "454"}, {"messageId": "455", "fix": "489", "desc": "457"}, {"messageId": "452", "fix": "490", "desc": "454"}, {"messageId": "455", "fix": "491", "desc": "457"}, {"messageId": "452", "fix": "492", "desc": "454"}, {"messageId": "455", "fix": "493", "desc": "457"}, {"messageId": "452", "fix": "494", "desc": "454"}, {"messageId": "455", "fix": "495", "desc": "457"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "496", "text": "497"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "498", "text": "499"}, "suggestUnknown", {"range": "500", "text": "501"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "502", "text": "503"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "504", "text": "501"}, {"range": "505", "text": "503"}, {"range": "506", "text": "501"}, {"range": "507", "text": "503"}, {"range": "508", "text": "501"}, {"range": "509", "text": "503"}, {"range": "510", "text": "501"}, {"range": "511", "text": "503"}, {"range": "512", "text": "501"}, {"range": "513", "text": "503"}, {"range": "514", "text": "501"}, {"range": "515", "text": "503"}, {"range": "516", "text": "501"}, {"range": "517", "text": "503"}, "Update the dependencies array to be: [fetchCategoryData, params.slug]", {"range": "518", "text": "519"}, {"range": "520", "text": "501"}, {"range": "521", "text": "503"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "522", "text": "523"}, {"range": "524", "text": "501"}, {"range": "525", "text": "503"}, {"range": "526", "text": "501"}, {"range": "527", "text": "503"}, "Update the dependencies array to be: [fetchToolDetails, params.id]", {"range": "528", "text": "529"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "530", "text": "531"}, {"range": "532", "text": "501"}, {"range": "533", "text": "503"}, {"range": "534", "text": "501"}, {"range": "535", "text": "503"}, {"range": "536", "text": "501"}, {"range": "537", "text": "503"}, {"range": "538", "text": "501"}, {"range": "539", "text": "503"}, {"range": "540", "text": "501"}, {"range": "541", "text": "503"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1183, 1186], [1183, 1186], [2591, 2594], [2591, 2594], [2002, 2005], [2002, 2005], [2993, 2996], [2993, 2996], [802, 805], [802, 805], [1656, 1659], [1656, 1659], [4318, 4321], [4318, 4321], [1651, 1664], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, params.slug]", [1084, 1087], [1084, 1087], [1532, 1575], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [1084, 1095], "[fetchToolDetails, params.id]", [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624]]