{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IOrder extends Document {\n  userId: string; // 用户ID\n  toolId: string; // 工具ID\n  type: 'launch_date_priority'; // 订单类型\n  amount: number; // 金额（分为单位）\n  currency: string; // 货币类型\n  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';\n  \n  // 支付相关\n  paymentMethod?: string; // 支付方式\n  paymentIntentId?: string; // Stripe payment intent ID (已弃用，使用stripePaymentIntentId)\n  paymentSessionId?: string; // Stripe checkout session ID\n  stripePaymentIntentId?: string; // Stripe payment intent ID\n  stripeCustomerId?: string; // Stripe customer ID\n  stripePaymentDetails?: {\n    paymentIntentId: string;\n    amount: number;\n    currency: string;\n    status: string;\n    created: Date;\n    failureReason?: string;\n  };\n  \n  // 订单详情\n  description: string; // 订单描述\n  selectedLaunchDate: Date; // 选择的发布日期\n  \n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  paidAt?: Date;\n  cancelledAt?: Date;\n  refundedAt?: Date;\n}\n\nconst OrderSchema: Schema = new Schema({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'User ID is required']\n  },\n  toolId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Tool',\n    required: [true, 'Tool ID is required']\n  },\n  type: {\n    type: String,\n    required: true,\n    enum: ['launch_date_priority'],\n    default: 'launch_date_priority'\n  },\n  amount: {\n    type: Number,\n    required: [true, 'Amount is required'],\n    min: [0, 'Amount must be positive']\n  },\n  currency: {\n    type: String,\n    required: true,\n    default: 'CNY',\n    enum: ['CNY', 'USD']\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],\n    default: 'pending'\n  },\n  \n  // 支付相关\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paymentIntentId: {\n    type: String,\n    trim: true\n  },\n  paymentSessionId: {\n    type: String,\n    trim: true\n  },\n  stripePaymentIntentId: {\n    type: String,\n    trim: true\n  },\n  stripeCustomerId: {\n    type: String,\n    trim: true\n  },\n  stripePaymentDetails: {\n    paymentIntentId: String,\n    amount: Number,\n    currency: String,\n    status: String,\n    created: Date,\n    failureReason: String\n  },\n  \n  // 订单详情\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  selectedLaunchDate: {\n    type: Date,\n    required: [true, 'Selected launch date is required']\n  },\n  \n  // 时间戳\n  paidAt: {\n    type: Date\n  },\n  cancelledAt: {\n    type: Date\n  },\n  refundedAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// 索引\nOrderSchema.index({ userId: 1, createdAt: -1 });\nOrderSchema.index({ toolId: 1 });\nOrderSchema.index({ status: 1 });\nOrderSchema.index({ paymentIntentId: 1 });\nOrderSchema.index({ paymentSessionId: 1 });\nOrderSchema.index({ stripePaymentIntentId: 1 });\nOrderSchema.index({ stripeCustomerId: 1 });\n\n// 虚拟字段\nOrderSchema.virtual('user', {\n  ref: 'User',\n  localField: 'userId',\n  foreignField: '_id',\n  justOne: true\n});\n\nOrderSchema.virtual('tool', {\n  ref: 'Tool',\n  localField: 'toolId',\n  foreignField: '_id',\n  justOne: true\n});\n\n// 实例方法\nOrderSchema.methods.markAsPaid = function() {\n  this.status = 'completed';\n  this.paidAt = new Date();\n  return this.save();\n};\n\nOrderSchema.methods.markAsFailed = function() {\n  this.status = 'failed';\n  return this.save();\n};\n\nOrderSchema.methods.cancel = function() {\n  this.status = 'cancelled';\n  this.cancelledAt = new Date();\n  return this.save();\n};\n\nOrderSchema.methods.refund = function() {\n  this.status = 'refunded';\n  this.refundedAt = new Date();\n  return this.save();\n};\n\nexport default mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,cAAsB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACrC,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;SAAuB;QAC9B,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAAqB;QACtC,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,SAAS;QACT,MAAM;YAAC;YAAO;SAAM;IACtB;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAW;YAAa;YAAU;YAAa;SAAW;QACjE,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;QACN,MAAM;IACR;IACA,uBAAuB;QACrB,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;QACN,MAAM;IACR;IACA,sBAAsB;QACpB,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,eAAe;IACjB;IAEA,OAAO;IACP,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,oBAAoB;QAClB,MAAM;QACN,UAAU;YAAC;YAAM;SAAmC;IACtD;IAEA,MAAM;IACN,QAAQ;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,KAAK;AACL,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC9B,YAAY,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC9B,YAAY,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACvC,YAAY,KAAK,CAAC;IAAE,kBAAkB;AAAE;AACxC,YAAY,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAExC,OAAO;AACP,YAAY,OAAO,CAAC,QAAQ;IAC1B,KAAK;IACL,YAAY;IACZ,cAAc;IACd,SAAS;AACX;AAEA,YAAY,OAAO,CAAC,QAAQ;IAC1B,KAAK;IACL,YAAY;IACZ,cAAc;IACd,SAAS;AACX;AAEA,OAAO;AACP,YAAY,OAAO,CAAC,UAAU,GAAG;IAC/B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,YAAY,GAAG;IACjC,IAAI,CAAC,MAAM,GAAG;IACd,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,MAAM,GAAG;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,WAAW,GAAG,IAAI;IACvB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,MAAM,GAAG;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG,IAAI;IACtB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport dbConnect from './mongodb';\nimport User from '../models/User';\n\nexport const authOptions: NextAuthOptions = {\n  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略\n  // adapter: MongoDBAdapter(client),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      id: 'email-code',\n      name: '<PERSON><PERSON> Code',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        code: { label: 'Code', type: 'text' },\n        token: { label: 'Token', type: 'text' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.code || !credentials?.token) {\n          return null;\n        }\n\n        try {\n          await dbConnect();\n\n          // 查找用户\n          const user = await User.findOne({\n            email: credentials.email.toLowerCase(),\n            emailVerificationExpires: { $gt: new Date() }\n          });\n\n          if (!user) {\n            return null;\n          }\n\n          // 验证token和验证码\n          const storedData = user.emailVerificationToken;\n          if (!storedData || !storedData.includes(':')) {\n            return null;\n          }\n\n          const [storedToken, storedCode] = storedData.split(':');\n\n          if (storedToken !== credentials.token || storedCode !== credentials.code) {\n            return null;\n          }\n\n          // 验证成功，更新用户状态\n          user.emailVerified = true;\n          user.emailVerificationToken = undefined;\n          user.emailVerificationExpires = undefined;\n          user.lastLoginAt = new Date();\n\n          // 如果用户没有邮箱账户记录，添加一个\n          const hasEmailAccount = user.accounts.some((acc: any) => acc.provider === 'email');\n          if (!hasEmailAccount) {\n            user.accounts.push({\n              provider: 'email',\n              providerId: 'email',\n              providerAccountId: user.email,\n            });\n          }\n\n          await user.save();\n\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            name: user.name,\n            image: user.avatar,\n            role: user.role,\n          };\n        } catch (error) {\n          console.error('Email code authorization error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      // 对于credentials provider，用户已经在authorize中处理过了\n      if (account?.provider === 'email-code') {\n        return true;\n      }\n\n      await dbConnect();\n\n      try {\n        // 查找或创建用户（仅用于OAuth providers）\n        let existingUser = await User.findOne({ email: user.email });\n\n        if (!existingUser) {\n          // 创建新用户\n          existingUser = new User({\n            email: user.email,\n            name: user.name || profile?.name || 'User',\n            avatar: user.image || profile?.image,\n            emailVerified: true, // OAuth登录默认已验证\n            lastLoginAt: new Date(),\n          });\n          await existingUser.save();\n        } else {\n          // 更新最后登录时间\n          existingUser.lastLoginAt = new Date();\n          await existingUser.save();\n        }\n\n        // 添加或更新账户信息\n        if (account && account.provider !== 'email-code') {\n          existingUser.addAccount({\n            provider: account.provider as 'google' | 'github',\n            providerId: account.provider,\n            providerAccountId: account.providerAccountId || account.id || '',\n            accessToken: account.access_token,\n            refreshToken: account.refresh_token,\n            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,\n          });\n          await existingUser.save();\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Sign in error:', error);\n        return false;\n      }\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        // 对于credentials provider，user对象已经包含了我们需要的信息\n        token.userId = user.id;\n        token.role = (user as any).role || 'user';\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        (session.user as any).id = token.userId as string;\n        (session.user as any).role = token.role as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,cAA+B;IAC1C,iDAAiD;IACjD,mCAAmC;IACnC,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,MAAM;oBAAE,OAAO;oBAAQ,MAAM;gBAAO;gBACpC,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;YACxC;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;oBACpE,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;oBAEd,OAAO;oBACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,YAAY,KAAK,CAAC,WAAW;wBACpC,0BAA0B;4BAAE,KAAK,IAAI;wBAAO;oBAC9C;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,cAAc;oBACd,MAAM,aAAa,KAAK,sBAAsB;oBAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM;wBAC5C,OAAO;oBACT;oBAEA,MAAM,CAAC,aAAa,WAAW,GAAG,WAAW,KAAK,CAAC;oBAEnD,IAAI,gBAAgB,YAAY,KAAK,IAAI,eAAe,YAAY,IAAI,EAAE;wBACxE,OAAO;oBACT;oBAEA,cAAc;oBACd,KAAK,aAAa,GAAG;oBACrB,KAAK,sBAAsB,GAAG;oBAC9B,KAAK,wBAAwB,GAAG;oBAChC,KAAK,WAAW,GAAG,IAAI;oBAEvB,oBAAoB;oBACpB,MAAM,kBAAkB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK;oBAC1E,IAAI,CAAC,iBAAiB;wBACpB,KAAK,QAAQ,CAAC,IAAI,CAAC;4BACjB,UAAU;4BACV,YAAY;4BACZ,mBAAmB,KAAK,KAAK;wBAC/B;oBACF;oBAEA,MAAM,KAAK,IAAI;oBAEf,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,MAAM;wBAClB,MAAM,KAAK,IAAI;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,6CAA6C;YAC7C,IAAI,SAAS,aAAa,cAAc;gBACtC,OAAO;YACT;YAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YAEd,IAAI;gBACF,8BAA8B;gBAC9B,IAAI,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,KAAK,KAAK;gBAAC;gBAE1D,IAAI,CAAC,cAAc;oBACjB,QAAQ;oBACR,eAAe,IAAI,uHAAA,CAAA,UAAI,CAAC;wBACtB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;wBACpC,QAAQ,KAAK,KAAK,IAAI,SAAS;wBAC/B,eAAe;wBACf,aAAa,IAAI;oBACnB;oBACA,MAAM,aAAa,IAAI;gBACzB,OAAO;oBACL,WAAW;oBACX,aAAa,WAAW,GAAG,IAAI;oBAC/B,MAAM,aAAa,IAAI;gBACzB;gBAEA,YAAY;gBACZ,IAAI,WAAW,QAAQ,QAAQ,KAAK,cAAc;oBAChD,aAAa,UAAU,CAAC;wBACtB,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,QAAQ;wBAC5B,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,EAAE,IAAI;wBAC9D,aAAa,QAAQ,YAAY;wBACjC,cAAc,QAAQ,aAAa;wBACnC,WAAW,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,QAAQ;oBACxE;oBACA,MAAM,aAAa,IAAI;gBACzB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;YACT;QACF;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,KAAK,EAAE;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,IAAI;YACrC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,MAAM;gBACtC,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts"], "sourcesContent": ["import Stripe from 'stripe';\n\n// 服务端Stripe实例\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2024-12-18.acacia',\n  typescript: true,\n});\n\n// 客户端Stripe配置\nexport const getStripePublishableKey = () => {\n  return process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!;\n};\n\n// Stripe产品和价格配置\nexport const STRIPE_CONFIG = {\n  // 优先发布服务产品\n  PRIORITY_LAUNCH: {\n    productName: 'AI工具优先发布服务',\n    price: 9900, // 99元，以分为单位\n    currency: 'cny',\n    description: '让您的AI工具获得优先审核和推荐位置',\n    features: [\n      '优先审核（1个工作日内）',\n      '首页推荐位置展示',\n      '自定义发布日期',\n      '专属客服支持'\n    ]\n  }\n};\n\n// 创建支付意图\nexport async function createPaymentIntent(\n  amount: number,\n  currency: string = 'cny',\n  metadata: Record<string, string> = {}\n): Promise<Stripe.PaymentIntent> {\n  try {\n    const paymentIntent = await stripe.paymentIntents.create({\n      amount,\n      currency,\n      metadata,\n      automatic_payment_methods: {\n        enabled: true,\n      },\n    });\n\n    return paymentIntent;\n  } catch (error) {\n    console.error('Error creating payment intent:', error);\n    throw new Error('Failed to create payment intent');\n  }\n}\n\n// 确认支付意图\nexport async function confirmPaymentIntent(\n  paymentIntentId: string\n): Promise<Stripe.PaymentIntent> {\n  try {\n    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);\n    return paymentIntent;\n  } catch (error) {\n    console.error('Error confirming payment intent:', error);\n    throw new Error('Failed to confirm payment intent');\n  }\n}\n\n// 创建客户\nexport async function createStripeCustomer(\n  email: string,\n  name?: string,\n  metadata: Record<string, string> = {}\n): Promise<Stripe.Customer> {\n  try {\n    const customer = await stripe.customers.create({\n      email,\n      name,\n      metadata,\n    });\n\n    return customer;\n  } catch (error) {\n    console.error('Error creating Stripe customer:', error);\n    throw new Error('Failed to create customer');\n  }\n}\n\n// 获取或创建客户\nexport async function getOrCreateStripeCustomer(\n  email: string,\n  name?: string,\n  metadata: Record<string, string> = {}\n): Promise<Stripe.Customer> {\n  try {\n    // 首先尝试查找现有客户\n    const existingCustomers = await stripe.customers.list({\n      email,\n      limit: 1,\n    });\n\n    if (existingCustomers.data.length > 0) {\n      return existingCustomers.data[0];\n    }\n\n    // 如果没有找到，创建新客户\n    return await createStripeCustomer(email, name, metadata);\n  } catch (error) {\n    console.error('Error getting or creating Stripe customer:', error);\n    throw new Error('Failed to get or create customer');\n  }\n}\n\n// 处理Webhook事件\nexport function constructWebhookEvent(\n  payload: string | Buffer,\n  signature: string,\n  secret: string\n): Stripe.Event {\n  try {\n    return stripe.webhooks.constructEvent(payload, signature, secret);\n  } catch (error) {\n    console.error('Error constructing webhook event:', error);\n    throw new Error('Invalid webhook signature');\n  }\n}\n\n// 格式化金额显示\nexport function formatAmount(amount: number, currency: string = 'cny'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n}\n\n// 验证Webhook签名\nexport function verifyWebhookSignature(\n  payload: string | Buffer,\n  signature: string\n): boolean {\n  try {\n    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n    if (!webhookSecret) {\n      throw new Error('Webhook secret not configured');\n    }\n\n    constructWebhookEvent(payload, signature, webhookSecret);\n    return true;\n  } catch (error) {\n    console.error('Webhook signature verification failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAGO,MAAM,SAAS,IAAI,wJAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAG;IAC/D,YAAY;IACZ,YAAY;AACd;AAGO,MAAM,0BAA0B;IACrC,OAAO,QAAQ,GAAG,CAAC,kCAAkC;AACvD;AAGO,MAAM,gBAAgB;IAC3B,WAAW;IACX,iBAAiB;QACf,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,eAAe,oBACpB,MAAc,EACd,WAAmB,KAAK,EACxB,WAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,gBAAgB,MAAM,OAAO,cAAc,CAAC,MAAM,CAAC;YACvD;YACA;YACA;YACA,2BAA2B;gBACzB,SAAS;YACX;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,qBACpB,eAAuB;IAEvB,IAAI;QACF,MAAM,gBAAgB,MAAM,OAAO,cAAc,CAAC,QAAQ,CAAC;QAC3D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,qBACpB,KAAa,EACb,IAAa,EACb,WAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,SAAS,CAAC,MAAM,CAAC;YAC7C;YACA;YACA;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,0BACpB,KAAa,EACb,IAAa,EACb,WAAmC,CAAC,CAAC;IAErC,IAAI;QACF,aAAa;QACb,MAAM,oBAAoB,MAAM,OAAO,SAAS,CAAC,IAAI,CAAC;YACpD;YACA,OAAO;QACT;QAEA,IAAI,kBAAkB,IAAI,CAAC,MAAM,GAAG,GAAG;YACrC,OAAO,kBAAkB,IAAI,CAAC,EAAE;QAClC;QAEA,eAAe;QACf,OAAO,MAAM,qBAAqB,OAAO,MAAM;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,SAAS,sBACd,OAAwB,EACxB,SAAiB,EACjB,MAAc;IAEd,IAAI;QACF,OAAO,OAAO,QAAQ,CAAC,cAAc,CAAC,SAAS,WAAW;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,SAAS,aAAa,MAAc,EAAE,WAAmB,KAAK;IACnE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB;AAGO,SAAS,uBACd,OAAwB,EACxB,SAAiB;IAEjB,IAAI;QACF,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,sBAAsB,SAAS,WAAW;QAC1C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport dbConnect from '@/lib/mongodb';\nimport Order from '@/models/Order';\nimport User from '@/models/User';\nimport { authOptions } from '@/lib/auth';\nimport { createPaymentIntent, getOrCreateStripeCustomer, STRIPE_CONFIG, stripe } from '@/lib/stripe';\nimport mongoose from 'mongoose';\n\n// POST /api/stripe/create-payment-intent - 创建Stripe支付意图\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: '请先登录' },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { orderId } = await request.json();\n\n    if (!mongoose.Types.ObjectId.isValid(orderId)) {\n      return NextResponse.json(\n        { success: false, message: '无效的订单ID' },\n        { status: 400 }\n      );\n    }\n\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: '用户不存在' },\n        { status: 404 }\n      );\n    }\n\n    const order = await Order.findById(orderId);\n    if (!order) {\n      return NextResponse.json(\n        { success: false, message: '订单不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 检查订单所有权\n    if (order.userId.toString() !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: '您没有权限操作此订单' },\n        { status: 403 }\n      );\n    }\n\n    // 检查订单状态\n    if (order.status !== 'pending') {\n      return NextResponse.json(\n        { success: false, message: '订单状态异常，无法支付' },\n        { status: 400 }\n      );\n    }\n\n    // 如果已经有支付意图ID，先检查状态\n    if (order.stripePaymentIntentId) {\n      try {\n        const existingPaymentIntent = await stripe.paymentIntents.retrieve(order.stripePaymentIntentId);\n        \n        if (existingPaymentIntent.status === 'succeeded') {\n          return NextResponse.json(\n            { success: false, message: '订单已支付' },\n            { status: 400 }\n          );\n        }\n        \n        if (existingPaymentIntent.status === 'requires_payment_method' || \n            existingPaymentIntent.status === 'requires_confirmation') {\n          // 返回现有的支付意图\n          return NextResponse.json({\n            success: true,\n            data: {\n              clientSecret: existingPaymentIntent.client_secret,\n              paymentIntentId: existingPaymentIntent.id\n            }\n          });\n        }\n      } catch (error) {\n        console.error('Error retrieving existing payment intent:', error);\n        // 继续创建新的支付意图\n      }\n    }\n\n    console.log('user email..............', user.email, user)\n    // 获取或创建Stripe客户\n    const stripeCustomer = await getOrCreateStripeCustomer(\n      user.email,\n      user.name || undefined,\n      {\n        userId: user._id.toString(),\n        orderId: order._id.toString()\n      }\n    );\n\n    // 创建支付意图\n    const paymentIntent = await createPaymentIntent(\n      order.amount,\n      'usd',\n      {\n        orderId: order._id.toString(),\n        userId: user._id.toString(),\n        toolId: order.toolId.toString(),\n        productType: 'priority_launch'\n      }\n    );\n\n    // 更新订单，保存支付意图ID和客户ID\n    order.stripePaymentIntentId = paymentIntent.id;\n    order.stripeCustomerId = stripeCustomer.id;\n    await order.save();\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        clientSecret: paymentIntent.client_secret,\n        paymentIntentId: paymentIntent.id,\n        amount: order.amount,\n        currency: 'cny'\n      }\n    });\n\n  } catch (error) {\n    console.error('Error creating payment intent:', error);\n    return NextResponse.json(\n      { success: false, message: '创建支付失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAO,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtC,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAU,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,MAAM,MAAM,CAAC,QAAQ,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAa,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,MAAM,MAAM,KAAK,WAAW;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAc,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,IAAI,MAAM,qBAAqB,EAAE;YAC/B,IAAI;gBACF,MAAM,wBAAwB,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,qBAAqB;gBAE9F,IAAI,sBAAsB,MAAM,KAAK,aAAa;oBAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBAAE,SAAS;wBAAO,SAAS;oBAAQ,GACnC;wBAAE,QAAQ;oBAAI;gBAElB;gBAEA,IAAI,sBAAsB,MAAM,KAAK,6BACjC,sBAAsB,MAAM,KAAK,yBAAyB;oBAC5D,YAAY;oBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,MAAM;4BACJ,cAAc,sBAAsB,aAAa;4BACjD,iBAAiB,sBAAsB,EAAE;wBAC3C;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,aAAa;YACf;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B,KAAK,KAAK,EAAE;QACpD,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EACnD,KAAK,KAAK,EACV,KAAK,IAAI,IAAI,WACb;YACE,QAAQ,KAAK,GAAG,CAAC,QAAQ;YACzB,SAAS,MAAM,GAAG,CAAC,QAAQ;QAC7B;QAGF,SAAS;QACT,MAAM,gBAAgB,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAC5C,MAAM,MAAM,EACZ,OACA;YACE,SAAS,MAAM,GAAG,CAAC,QAAQ;YAC3B,QAAQ,KAAK,GAAG,CAAC,QAAQ;YACzB,QAAQ,MAAM,MAAM,CAAC,QAAQ;YAC7B,aAAa;QACf;QAGF,qBAAqB;QACrB,MAAM,qBAAqB,GAAG,cAAc,EAAE;QAC9C,MAAM,gBAAgB,GAAG,eAAe,EAAE;QAC1C,MAAM,MAAM,IAAI;QAEhB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,cAAc,cAAc,aAAa;gBACzC,iBAAiB,cAAc,EAAE;gBACjC,QAAQ,MAAM,MAAM;gBACpB,UAAU;YACZ;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAS,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}