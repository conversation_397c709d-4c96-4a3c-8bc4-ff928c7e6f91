{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  publishedAt?: Date;\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 添加draft状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    maxlength: [100, 'Tool name cannot exceed 100 characters']\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Tagline cannot exceed 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [2000, 'Long description cannot exceed 2000 characters']\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: [\n      'text-generation',\n      'image-generation', \n      'video-generation',\n      'audio-generation',\n      'code-generation',\n      'data-analysis',\n      'productivity',\n      'design',\n      'marketing',\n      'education',\n      'research',\n      'other'\n    ]\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Pricing details cannot exceed 500 characters']\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  publishedAt: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ publishedAt: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA2CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAuC;IAC1D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAiD;IACrE;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAA+C;IAClE;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;SAAW;QAClD,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IOrder extends Document {\n  userId: string; // 用户ID\n  toolId: string; // 工具ID\n  type: 'launch_date_priority'; // 订单类型\n  amount: number; // 金额（分为单位）\n  currency: string; // 货币类型\n  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';\n  \n  // 支付相关\n  paymentMethod?: string; // 支付方式\n  paymentIntentId?: string; // Stripe payment intent ID (已弃用，使用stripePaymentIntentId)\n  paymentSessionId?: string; // Stripe checkout session ID\n  stripePaymentIntentId?: string; // Stripe payment intent ID\n  stripeCustomerId?: string; // Stripe customer ID\n  stripePaymentDetails?: {\n    paymentIntentId: string;\n    amount: number;\n    currency: string;\n    status: string;\n    created: Date;\n    failureReason?: string;\n  };\n  \n  // 订单详情\n  description: string; // 订单描述\n  selectedLaunchDate: Date; // 选择的发布日期\n  \n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  paidAt?: Date;\n  cancelledAt?: Date;\n  refundedAt?: Date;\n}\n\nconst OrderSchema: Schema = new Schema({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'User ID is required']\n  },\n  toolId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Tool',\n    required: [true, 'Tool ID is required']\n  },\n  type: {\n    type: String,\n    required: true,\n    enum: ['launch_date_priority'],\n    default: 'launch_date_priority'\n  },\n  amount: {\n    type: Number,\n    required: [true, 'Amount is required'],\n    min: [0, 'Amount must be positive']\n  },\n  currency: {\n    type: String,\n    required: true,\n    default: 'CNY',\n    enum: ['CNY', 'USD']\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],\n    default: 'pending'\n  },\n  \n  // 支付相关\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paymentIntentId: {\n    type: String,\n    trim: true\n  },\n  paymentSessionId: {\n    type: String,\n    trim: true\n  },\n  stripePaymentIntentId: {\n    type: String,\n    trim: true\n  },\n  stripeCustomerId: {\n    type: String,\n    trim: true\n  },\n  stripePaymentDetails: {\n    paymentIntentId: String,\n    amount: Number,\n    currency: String,\n    status: String,\n    created: Date,\n    failureReason: String\n  },\n  \n  // 订单详情\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  selectedLaunchDate: {\n    type: Date,\n    required: [true, 'Selected launch date is required']\n  },\n  \n  // 时间戳\n  paidAt: {\n    type: Date\n  },\n  cancelledAt: {\n    type: Date\n  },\n  refundedAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// 索引\nOrderSchema.index({ userId: 1, createdAt: -1 });\nOrderSchema.index({ toolId: 1 });\nOrderSchema.index({ status: 1 });\nOrderSchema.index({ paymentIntentId: 1 });\nOrderSchema.index({ paymentSessionId: 1 });\nOrderSchema.index({ stripePaymentIntentId: 1 });\nOrderSchema.index({ stripeCustomerId: 1 });\n\n// 虚拟字段\nOrderSchema.virtual('user', {\n  ref: 'User',\n  localField: 'userId',\n  foreignField: '_id',\n  justOne: true\n});\n\nOrderSchema.virtual('tool', {\n  ref: 'Tool',\n  localField: 'toolId',\n  foreignField: '_id',\n  justOne: true\n});\n\n// 实例方法\nOrderSchema.methods.markAsPaid = function() {\n  this.status = 'completed';\n  this.paidAt = new Date();\n  return this.save();\n};\n\nOrderSchema.methods.markAsFailed = function() {\n  this.status = 'failed';\n  return this.save();\n};\n\nOrderSchema.methods.cancel = function() {\n  this.status = 'cancelled';\n  this.cancelledAt = new Date();\n  return this.save();\n};\n\nOrderSchema.methods.refund = function() {\n  this.status = 'refunded';\n  this.refundedAt = new Date();\n  return this.save();\n};\n\nexport default mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,cAAsB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACrC,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;SAAuB;QAC9B,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAAqB;QACtC,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,SAAS;QACT,MAAM;YAAC;YAAO;SAAM;IACtB;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAW;YAAa;YAAU;YAAa;SAAW;QACjE,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;QACN,MAAM;IACR;IACA,uBAAuB;QACrB,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;QACN,MAAM;IACR;IACA,sBAAsB;QACpB,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,eAAe;IACjB;IAEA,OAAO;IACP,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,oBAAoB;QAClB,MAAM;QACN,UAAU;YAAC;YAAM;SAAmC;IACtD;IAEA,MAAM;IACN,QAAQ;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,KAAK;AACL,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC9B,YAAY,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC9B,YAAY,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACvC,YAAY,KAAK,CAAC;IAAE,kBAAkB;AAAE;AACxC,YAAY,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAExC,OAAO;AACP,YAAY,OAAO,CAAC,QAAQ;IAC1B,KAAK;IACL,YAAY;IACZ,cAAc;IACd,SAAS;AACX;AAEA,YAAY,OAAO,CAAC,QAAQ;IAC1B,KAAK;IACL,YAAY;IACZ,cAAc;IACd,SAAS;AACX;AAEA,OAAO;AACP,YAAY,OAAO,CAAC,UAAU,GAAG;IAC/B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,YAAY,GAAG;IACjC,IAAI,CAAC,MAAM,GAAG;IACd,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,MAAM,GAAG;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,WAAW,GAAG,IAAI;IACvB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,MAAM,GAAG;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG,IAAI;IACtB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport dbConnect from './mongodb';\nimport User from '../models/User';\n\nexport const authOptions: NextAuthOptions = {\n  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略\n  // adapter: MongoDBAdapter(client),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      id: 'email-code',\n      name: '<PERSON><PERSON> Code',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        code: { label: 'Code', type: 'text' },\n        token: { label: 'Token', type: 'text' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.code || !credentials?.token) {\n          return null;\n        }\n\n        try {\n          await dbConnect();\n\n          // 查找用户\n          const user = await User.findOne({\n            email: credentials.email.toLowerCase(),\n            emailVerificationExpires: { $gt: new Date() }\n          });\n\n          if (!user) {\n            return null;\n          }\n\n          // 验证token和验证码\n          const storedData = user.emailVerificationToken;\n          if (!storedData || !storedData.includes(':')) {\n            return null;\n          }\n\n          const [storedToken, storedCode] = storedData.split(':');\n\n          if (storedToken !== credentials.token || storedCode !== credentials.code) {\n            return null;\n          }\n\n          // 验证成功，更新用户状态\n          user.emailVerified = true;\n          user.emailVerificationToken = undefined;\n          user.emailVerificationExpires = undefined;\n          user.lastLoginAt = new Date();\n\n          // 如果用户没有邮箱账户记录，添加一个\n          const hasEmailAccount = user.accounts.some((acc: any) => acc.provider === 'email');\n          if (!hasEmailAccount) {\n            user.accounts.push({\n              provider: 'email',\n              providerId: 'email',\n              providerAccountId: user.email,\n            });\n          }\n\n          await user.save();\n\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            name: user.name,\n            image: user.avatar,\n            role: user.role,\n          };\n        } catch (error) {\n          console.error('Email code authorization error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      // 对于credentials provider，用户已经在authorize中处理过了\n      if (account?.provider === 'email-code') {\n        return true;\n      }\n\n      await dbConnect();\n\n      try {\n        // 查找或创建用户（仅用于OAuth providers）\n        let existingUser = await User.findOne({ email: user.email });\n\n        if (!existingUser) {\n          // 创建新用户\n          existingUser = new User({\n            email: user.email,\n            name: user.name || profile?.name || 'User',\n            avatar: user.image || profile?.image,\n            emailVerified: true, // OAuth登录默认已验证\n            lastLoginAt: new Date(),\n          });\n          await existingUser.save();\n        } else {\n          // 更新最后登录时间\n          existingUser.lastLoginAt = new Date();\n          await existingUser.save();\n        }\n\n        // 添加或更新账户信息\n        if (account && account.provider !== 'email-code') {\n          existingUser.addAccount({\n            provider: account.provider as 'google' | 'github',\n            providerId: account.provider,\n            providerAccountId: account.providerAccountId || account.id || '',\n            accessToken: account.access_token,\n            refreshToken: account.refresh_token,\n            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,\n          });\n          await existingUser.save();\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Sign in error:', error);\n        return false;\n      }\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        // 对于credentials provider，user对象已经包含了我们需要的信息\n        token.userId = user.id;\n        token.role = (user as any).role || 'user';\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        (session.user as any).id = token.userId as string;\n        (session.user as any).role = token.role as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,cAA+B;IAC1C,iDAAiD;IACjD,mCAAmC;IACnC,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,MAAM;oBAAE,OAAO;oBAAQ,MAAM;gBAAO;gBACpC,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;YACxC;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;oBACpE,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;oBAEd,OAAO;oBACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,YAAY,KAAK,CAAC,WAAW;wBACpC,0BAA0B;4BAAE,KAAK,IAAI;wBAAO;oBAC9C;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,cAAc;oBACd,MAAM,aAAa,KAAK,sBAAsB;oBAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM;wBAC5C,OAAO;oBACT;oBAEA,MAAM,CAAC,aAAa,WAAW,GAAG,WAAW,KAAK,CAAC;oBAEnD,IAAI,gBAAgB,YAAY,KAAK,IAAI,eAAe,YAAY,IAAI,EAAE;wBACxE,OAAO;oBACT;oBAEA,cAAc;oBACd,KAAK,aAAa,GAAG;oBACrB,KAAK,sBAAsB,GAAG;oBAC9B,KAAK,wBAAwB,GAAG;oBAChC,KAAK,WAAW,GAAG,IAAI;oBAEvB,oBAAoB;oBACpB,MAAM,kBAAkB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK;oBAC1E,IAAI,CAAC,iBAAiB;wBACpB,KAAK,QAAQ,CAAC,IAAI,CAAC;4BACjB,UAAU;4BACV,YAAY;4BACZ,mBAAmB,KAAK,KAAK;wBAC/B;oBACF;oBAEA,MAAM,KAAK,IAAI;oBAEf,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,MAAM;wBAClB,MAAM,KAAK,IAAI;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,6CAA6C;YAC7C,IAAI,SAAS,aAAa,cAAc;gBACtC,OAAO;YACT;YAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YAEd,IAAI;gBACF,8BAA8B;gBAC9B,IAAI,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,KAAK,KAAK;gBAAC;gBAE1D,IAAI,CAAC,cAAc;oBACjB,QAAQ;oBACR,eAAe,IAAI,uHAAA,CAAA,UAAI,CAAC;wBACtB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;wBACpC,QAAQ,KAAK,KAAK,IAAI,SAAS;wBAC/B,eAAe;wBACf,aAAa,IAAI;oBACnB;oBACA,MAAM,aAAa,IAAI;gBACzB,OAAO;oBACL,WAAW;oBACX,aAAa,WAAW,GAAG,IAAI;oBAC/B,MAAM,aAAa,IAAI;gBACzB;gBAEA,YAAY;gBACZ,IAAI,WAAW,QAAQ,QAAQ,KAAK,cAAc;oBAChD,aAAa,UAAU,CAAC;wBACtB,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,QAAQ;wBAC5B,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,EAAE,IAAI;wBAC9D,aAAa,QAAQ,YAAY;wBACjC,cAAc,QAAQ,aAAa;wBACnC,WAAW,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,QAAQ;oBACxE;oBACA,MAAM,aAAa,IAAI;gBACzB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;YACT;QACF;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,KAAK,EAAE;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,IAAI;YACrC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,MAAM;gBACtC,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/%5Bid%5D/launch-date/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\nimport Order from '@/models/Order';\nimport User from '@/models/User';\nimport { authOptions } from '@/lib/auth';\nimport mongoose from 'mongoose';\n\n// POST /api/tools/[id]/launch-date - 设置工具发布日期和选项\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // 检查用户认证\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: '请先登录' },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { id } = await params;\n    const { launchOption, selectedDate } = await request.json();\n\n    // 验证ID格式\n    if (!mongoose.Types.ObjectId.isValid(id)) {\n      return NextResponse.json(\n        { success: false, message: '无效的工具ID' },\n        { status: 400 }\n      );\n    }\n\n    // 验证输入\n    if (!launchOption || !selectedDate) {\n      return NextResponse.json(\n        { success: false, message: '请选择发布选项和日期' },\n        { status: 400 }\n      );\n    }\n\n    if (!['free', 'paid'].includes(launchOption)) {\n      return NextResponse.json(\n        { success: false, message: '无效的发布选项' },\n        { status: 400 }\n      );\n    }\n\n    // 获取用户信息\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: '用户不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 查找工具\n    const tool = await Tool.findById(id);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: '工具不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 检查工具所有权\n    if (tool.submittedBy !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: '您没有权限修改此工具' },\n        { status: 403 }\n      );\n    }\n\n    // 检查工具状态\n    if (tool.status !== 'draft') {\n      return NextResponse.json(\n        { success: false, message: '此工具已经选择了发布日期' },\n        { status: 400 }\n      );\n    }\n\n    const selectedLaunchDate = new Date(selectedDate);\n    const now = new Date();\n\n    // 验证日期\n    if (launchOption === 'free') {\n      // 免费选项：必须是一个月后或更晚的日期\n      const oneMonthLater = new Date();\n      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);\n      oneMonthLater.setHours(0, 0, 0, 0);\n\n      const selectedDateOnly = new Date(selectedLaunchDate);\n      selectedDateOnly.setHours(0, 0, 0, 0);\n\n      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {\n        return NextResponse.json(\n          { success: false, message: '免费选项只能选择一个月后的日期' },\n          { status: 400 }\n        );\n      }\n    } else {\n      // 付费选项：必须是明天或以后的日期\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      tomorrow.setHours(0, 0, 0, 0);\n      \n      if (selectedLaunchDate < tomorrow) {\n        return NextResponse.json(\n          { success: false, message: '付费选项最早只能选择明天的日期' },\n          { status: 400 }\n        );\n      }\n    }\n\n    if (launchOption === 'free') {\n      // 免费选项：直接更新工具状态并进入审核\n      await Tool.findByIdAndUpdate(id, {\n        $set: {\n          launchDateSelected: true,\n          selectedLaunchDate,\n          launchOption: 'free',\n          paymentRequired: false,\n          status: 'pending' // 进入审核队列\n        }\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          message: '发布日期设置成功，工具已进入审核队列'\n        }\n      });\n\n    } else {\n      // 付费选项：创建订单\n      const paymentAmount = 9900; // 99元，以分为单位\n\n      const order = new Order({\n        userId: user._id,\n        toolId: id,\n        type: 'launch_date_priority',\n        amount: paymentAmount,\n        currency: 'CNY',\n        status: 'pending',\n        description: `工具 \"${tool.name}\" 优先发布服务`,\n        selectedLaunchDate\n      });\n\n      await order.save();\n\n      // 更新工具状态\n      await Tool.findByIdAndUpdate(id, {\n        $set: {\n          launchDateSelected: true,\n          selectedLaunchDate,\n          launchOption: 'paid',\n          paymentRequired: true,\n          paymentAmount,\n          paymentStatus: 'pending',\n          orderId: order._id.toString()\n        }\n      });\n\n      // 这里应该集成真实的支付系统（如Stripe、支付宝等）\n      // 现在返回一个模拟的支付URL\n      const paymentUrl = `/payment/checkout?orderId=${order._id}`;\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          orderId: order._id,\n          paymentUrl,\n          amount: paymentAmount,\n          message: '订单创建成功，请完成支付'\n        }\n      });\n    }\n\n  } catch (error) {\n    console.error('Launch date selection error:', error);\n    return NextResponse.json(\n      { success: false, message: '服务器错误' },\n      { status: 500 }\n    );\n  }\n}\n\n// GET /api/tools/[id]/launch-date - 获取工具发布日期信息\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: '请先登录' },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { id } = await params;\n\n    if (!mongoose.Types.ObjectId.isValid(id)) {\n      return NextResponse.json(\n        { success: false, message: '无效的工具ID' },\n        { status: 400 }\n      );\n    }\n\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: '用户不存在' },\n        { status: 404 }\n      );\n    }\n\n    const tool = await Tool.findById(id);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: '工具不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 检查工具所有权\n    if (tool.submittedBy !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: '您没有权限访问此工具' },\n        { status: 403 }\n      );\n    }\n\n    // 如果有订单，获取订单信息\n    let order = null;\n    if (tool.orderId) {\n      order = await Order.findById(tool.orderId);\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        tool: {\n          id: tool._id,\n          name: tool.name,\n          status: tool.status,\n          launchDateSelected: tool.launchDateSelected,\n          selectedLaunchDate: tool.selectedLaunchDate,\n          launchOption: tool.launchOption,\n          paymentRequired: tool.paymentRequired,\n          paymentStatus: tool.paymentStatus\n        },\n        order\n      }\n    });\n\n  } catch (error) {\n    console.error('Get launch date info error:', error);\n    return NextResponse.json(\n      { success: false, message: '服务器错误' },\n      { status: 500 }\n    );\n  }\n}\n\n// PATCH /api/tools/[id]/launch-date - 修改工具发布日期（仅限付费用户）\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: '请先登录' },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { id } = await params;\n    const { selectedDate } = await request.json();\n\n    if (!mongoose.Types.ObjectId.isValid(id)) {\n      return NextResponse.json(\n        { success: false, message: '无效的工具ID' },\n        { status: 400 }\n      );\n    }\n\n    if (!selectedDate) {\n      return NextResponse.json(\n        { success: false, message: '请选择发布日期' },\n        { status: 400 }\n      );\n    }\n\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: '用户不存在' },\n        { status: 404 }\n      );\n    }\n\n    const tool = await Tool.findById(id);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: '工具不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 检查工具所有权\n    if (tool.submittedBy !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: '您没有权限修改此工具' },\n        { status: 403 }\n      );\n    }\n\n    // 检查工具状态（只有还未发布的工具可以修改）\n    if (!['pending', 'approved'].includes(tool.status)) {\n      return NextResponse.json(\n        { success: false, message: '当前状态不允许修改发布日期' },\n        { status: 400 }\n      );\n    }\n\n    // 检查是否已经发布\n    const now = new Date();\n    if (tool.selectedLaunchDate && new Date(tool.selectedLaunchDate) <= now && tool.status === 'approved') {\n      return NextResponse.json(\n        { success: false, message: '工具已发布，无法修改发布日期' },\n        { status: 400 }\n      );\n    }\n\n    const selectedLaunchDate = new Date(selectedDate);\n\n    // 根据付费状态验证日期\n    if (tool.launchOption === 'paid') {\n      // 付费用户：可以选择明天及以后的任意日期\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      tomorrow.setHours(0, 0, 0, 0);\n\n      if (selectedLaunchDate < tomorrow) {\n        return NextResponse.json(\n          { success: false, message: '付费用户发布日期最早只能选择明天' },\n          { status: 400 }\n        );\n      }\n    } else {\n      // 免费用户：只能选择一个月后的日期\n      const oneMonthLater = new Date();\n      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);\n      oneMonthLater.setHours(0, 0, 0, 0);\n\n      const selectedDateOnly = new Date(selectedLaunchDate);\n      selectedDateOnly.setHours(0, 0, 0, 0);\n\n      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {\n        return NextResponse.json(\n          { success: false, message: '免费用户只能选择一个月后的日期' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 更新工具的发布日期\n    await Tool.findByIdAndUpdate(id, {\n      $set: {\n        selectedLaunchDate\n      }\n    });\n\n    // 如果有关联的订单，也更新订单的发布日期\n    if (tool.orderId) {\n      await Order.findByIdAndUpdate(tool.orderId, {\n        $set: {\n          selectedLaunchDate\n        }\n      });\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: '发布日期修改成功',\n        selectedLaunchDate\n      }\n    });\n\n  } catch (error) {\n    console.error('Update launch date error:', error);\n    return NextResponse.json(\n      { success: false, message: '服务器错误' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAGO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,SAAS;QACT,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAO,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzD,SAAS;QACT,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAU,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAa,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC;YAAC;YAAQ;SAAO,CAAC,QAAQ,CAAC,eAAe;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAU,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAa,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,KAAK,MAAM,KAAK,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAe,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,qBAAqB,IAAI,KAAK;QACpC,MAAM,MAAM,IAAI;QAEhB,OAAO;QACP,IAAI,iBAAiB,QAAQ;YAC3B,qBAAqB;YACrB,MAAM,gBAAgB,IAAI;YAC1B,cAAc,QAAQ,CAAC,cAAc,QAAQ,KAAK;YAClD,cAAc,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEhC,MAAM,mBAAmB,IAAI,KAAK;YAClC,iBAAiB,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEnC,IAAI,iBAAiB,OAAO,KAAK,cAAc,OAAO,IAAI;gBACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS;gBAAkB,GAC7C;oBAAE,QAAQ;gBAAI;YAElB;QACF,OAAO;YACL,mBAAmB;YACnB,MAAM,WAAW,IAAI;YACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;YAE3B,IAAI,qBAAqB,UAAU;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS;gBAAkB,GAC7C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,IAAI,iBAAiB,QAAQ;YAC3B,qBAAqB;YACrB,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;gBAC/B,MAAM;oBACJ,oBAAoB;oBACpB;oBACA,cAAc;oBACd,iBAAiB;oBACjB,QAAQ,UAAU,SAAS;gBAC7B;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,SAAS;gBACX;YACF;QAEF,OAAO;YACL,YAAY;YACZ,MAAM,gBAAgB,MAAM,YAAY;YAExC,MAAM,QAAQ,IAAI,wHAAA,CAAA,UAAK,CAAC;gBACtB,QAAQ,KAAK,GAAG;gBAChB,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR,aAAa,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;gBACvC;YACF;YAEA,MAAM,MAAM,IAAI;YAEhB,SAAS;YACT,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;gBAC/B,MAAM;oBACJ,oBAAoB;oBACpB;oBACA,cAAc;oBACd,iBAAiB;oBACjB;oBACA,eAAe;oBACf,SAAS,MAAM,GAAG,CAAC,QAAQ;gBAC7B;YACF;YAEA,8BAA8B;YAC9B,iBAAiB;YACjB,MAAM,aAAa,CAAC,0BAA0B,EAAE,MAAM,GAAG,EAAE;YAE3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,SAAS,MAAM,GAAG;oBAClB;oBACA,QAAQ;oBACR,SAAS;gBACX;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAQ,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAO,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAU,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAa,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,IAAI,QAAQ;QACZ,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,KAAK,OAAO;QAC3C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,MAAM;oBACJ,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;oBACnB,oBAAoB,KAAK,kBAAkB;oBAC3C,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,iBAAiB,KAAK,eAAe;oBACrC,eAAe,KAAK,aAAa;gBACnC;gBACA;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAQ,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAO,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3C,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAU,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAU,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAQ,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAa,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,IAAI,CAAC;YAAC;YAAW;SAAW,CAAC,QAAQ,CAAC,KAAK,MAAM,GAAG;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAgB,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,MAAM,IAAI;QAChB,IAAI,KAAK,kBAAkB,IAAI,IAAI,KAAK,KAAK,kBAAkB,KAAK,OAAO,KAAK,MAAM,KAAK,YAAY;YACrG,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAiB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,qBAAqB,IAAI,KAAK;QAEpC,aAAa;QACb,IAAI,KAAK,YAAY,KAAK,QAAQ;YAChC,sBAAsB;YACtB,MAAM,WAAW,IAAI;YACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;YAE3B,IAAI,qBAAqB,UAAU;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS;gBAAmB,GAC9C;oBAAE,QAAQ;gBAAI;YAElB;QACF,OAAO;YACL,mBAAmB;YACnB,MAAM,gBAAgB,IAAI;YAC1B,cAAc,QAAQ,CAAC,cAAc,QAAQ,KAAK;YAClD,cAAc,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEhC,MAAM,mBAAmB,IAAI,KAAK;YAClC,iBAAiB,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEnC,IAAI,iBAAiB,OAAO,KAAK,cAAc,OAAO,IAAI;gBACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS;gBAAkB,GAC7C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,YAAY;QACZ,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;YAC/B,MAAM;gBACJ;YACF;QACF;QAEA,sBAAsB;QACtB,IAAI,KAAK,OAAO,EAAE;YAChB,MAAM,wHAAA,CAAA,UAAK,CAAC,iBAAiB,CAAC,KAAK,OAAO,EAAE;gBAC1C,MAAM;oBACJ;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS;gBACT;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAQ,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}