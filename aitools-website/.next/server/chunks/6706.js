"use strict";exports.id=6706,exports.ids=[6706],exports.modules={96706:(r,t,e)=>{e.d(t,{ZW:()=>i,bw:()=>a,f:()=>n,stripe:()=>o});let o=new(e(97877)).A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia",typescript:!0});async function n(r,t="cny",e={}){try{return await o.paymentIntents.create({amount:r,currency:t,metadata:e,automatic_payment_methods:{enabled:!0}})}catch(r){throw console.error("Error creating payment intent:",r),Error("Failed to create payment intent")}}async function c(r,t,e={}){try{return await o.customers.create({email:r,name:t,metadata:e})}catch(r){throw console.error("Error creating Stripe customer:",r),Error("Failed to create customer")}}async function a(r,t,e={}){try{let n=await o.customers.list({email:r,limit:1});if(n.data.length>0)return n.data[0];return await c(r,t,e)}catch(r){throw console.error("Error getting or creating Stripe customer:",r),Error("Failed to get or create customer")}}function i(r,t,e){try{return o.webhooks.constructEvent(r,t,e)}catch(r){throw console.error("Error constructing webhook event:",r),Error("Invalid webhook signature")}}}};