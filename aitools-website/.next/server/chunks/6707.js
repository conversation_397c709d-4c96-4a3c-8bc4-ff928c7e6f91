exports.id=6707,exports.ids=[6707],exports.modules={9358:(e,s,t)=>{Promise.resolve().then(t.bind(t,14008)),Promise.resolve().then(t.bind(t,76242))},14008:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var r=t(60687),l=t(43210),a=t(85814),n=t.n(a),i=t(16189),o=t(23877),c=t(82136),d=t(48577);function x(){let{data:e,status:s}=(0,c.useSession)(),t=(0,i.useRouter)(),[a,n]=(0,l.useState)(!1),[x,m]=(0,l.useState)(!1),h=async()=>{await (0,c.signOut)({callbackUrl:"/"})},u=e=>{m(!1),t.push(e)};return"loading"===s?(0,r.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:"加载中..."}):e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>m(!x),children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsx)("span",{className:"text-sm hidden md:block",children:e.user?.name}),(0,r.jsx)(o.Vr3,{className:`text-xs transition-transform ${x?"rotate-180":""}`})]}),x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>m(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.user?.name}),(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:e.user?.email}),e.user?.role==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"})]})]})}),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>u("/profile"),children:[(0,r.jsx)(o.x$1,{}),"个人资料"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>u("/profile/submitted"),children:[(0,r.jsx)(o.svy,{}),"我提交的工具"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>u("/profile/liked"),children:[(0,r.jsx)(o.Mbv,{}),"我的收藏"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>u("/submit"),children:[(0,r.jsx)(o.OiG,{}),"提交工具"]})]}),e.user?.role==="admin"&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"border-t py-2",children:(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>u("/admin"),children:[(0,r.jsx)(o.Pcn,{}),"管理后台"]})})}),(0,r.jsxs)("div",{className:"border-t py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>u("/settings"),children:[(0,r.jsx)(o.Pcn,{}),"设置"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",onClick:h,children:[(0,r.jsx)(o.axc,{}),"退出登录"]})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>n(!0),children:[(0,r.jsx)(o.Zu,{}),"登录"]}),(0,r.jsx)(d.A,{isOpen:a,onClose:()=>n(!1)})]})}let m=({children:e,href:s})=>(0,r.jsx)(n(),{href:s,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e}),h=[{name:"首页",href:"/"},{name:"工具目录",href:"/tools"},{name:"分类",href:"/categories"},{name:"提交工具",href:"/submit"}];function u(){let[e,s]=(0,l.useState)(!1),t=(0,i.useRouter)(),a=e=>{e.preventDefault();let s=new FormData(e.currentTarget).get("search");s.trim()&&t.push(`/search?q=${encodeURIComponent(s.trim())}`)};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 hover:no-underline",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-4",children:h.map(e=>(0,r.jsx)(m,{href:e.href,children:e.name},e.name))})]}),(0,r.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,r.jsx)("form",{onSubmit:a,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(o.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:"搜索 AI 工具...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x,{}),(0,r.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>s(!e),"aria-label":"Open Menu",children:e?(0,r.jsx)(o.QCr,{}):(0,r.jsx)(o.OXb,{})})]})]}),e&&(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[h.map(e=>(0,r.jsx)(m,{href:e.href,children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)("form",{onSubmit:a,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(o.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:"搜索 AI 工具...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})})]})})]})})}},23440:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx","default")},25536:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},48577:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),l=t(43210),a=t(82136),n=t(23877);function i({isOpen:e,onClose:s}){let[t,i]=(0,l.useState)("method"),[o,c]=(0,l.useState)(""),[d,x]=(0,l.useState)(""),[m,h]=(0,l.useState)(!1),[u,b]=(0,l.useState)(""),p=(e,s="success")=>{let t=document.createElement("div");t.className=`fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${"success"===s?"bg-green-500":"bg-red-500"}`,t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},g=()=>{i("method"),c(""),x(""),b(""),s()},f=async e=>{try{h(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{h(!1)}},j=async()=>{if(!o)return void b("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return void b("请输入有效的邮箱地址");b(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),s=await e.json();s.success?(x(s.token),i("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}},y=async e=>{if(6===e.length){h(!0);try{let s=await (0,a.signIn)("email-code",{email:o,code:e,token:d,redirect:!1});s?.ok?(p("登录成功，欢迎回来！"),g()):p(s?.error||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}}},v=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");t[e].value=s,s&&e<5&&t[e+1]?.focus();let r=Array.from(t).map(e=>e.value).join("");6===r.length&&y(r)};return e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:g}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===t&&"登录 AI Tools Directory","email"===t&&"邮箱登录","code"===t&&"输入验证码"]}),(0,r.jsx)("button",{onClick:g,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>f("google"),disabled:m,children:[(0,r.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>f("github"),disabled:m,children:[(0,r.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>i("email"),children:[(0,r.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:o,onChange:e=>c(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&j(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),u&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:j,disabled:m,children:m?"发送中...":"发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>i("method"),children:"返回"})]})]}),"code"===t&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",o," 的6位验证码"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:s=>v(e,s.target.value),disabled:m,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>i("email"),children:"重新发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>i("method"),children:"返回"})]})]})]})]})]}):null}},61135:()=>{},61984:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},68926:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx","default")},74854:(e,s,t)=>{Promise.resolve().then(t.bind(t,68926)),Promise.resolve().then(t.bind(t,23440))},76242:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});var r=t(60687),l=t(82136);function a({children:e}){return(0,r.jsx)(l.SessionProvider,{children:e})}},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x,metadata:()=>d});var r=t(37413),l=t(2202),a=t.n(l),n=t(64988),i=t.n(n);t(61135);var o=t(23440),c=t(68926);let d={title:"AI工具导航",description:"发现最好的AI工具"};function x({children:e}){return(0,r.jsx)("html",{lang:"zh",children:(0,r.jsx)("body",{className:`${a().variable} ${i().variable} antialiased`,children:(0,r.jsxs)(o.default,{children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("main",{children:e})]})})})}}};