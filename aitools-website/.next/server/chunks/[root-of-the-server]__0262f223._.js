module.exports = {

"[project]/.next-internal/server/app/api/tools/[id]/launch-date/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[project]/src/models/Tool.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const ToolSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            'Tool name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Tool name cannot exceed 100 characters'
        ]
    },
    tagline: {
        type: String,
        trim: true,
        maxlength: [
            200,
            'Tagline cannot exceed 200 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Tool description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    longDescription: {
        type: String,
        trim: true,
        maxlength: [
            2000,
            'Long description cannot exceed 2000 characters'
        ]
    },
    website: {
        type: String,
        required: [
            true,
            'Website URL is required'
        ],
        trim: true,
        validate: {
            validator: function(v) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Please enter a valid URL'
        }
    },
    logo: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: [
            'text-generation',
            'image-generation',
            'video-generation',
            'audio-generation',
            'code-generation',
            'data-analysis',
            'productivity',
            'design',
            'marketing',
            'education',
            'research',
            'other'
        ]
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    pricing: {
        type: String,
        required: [
            true,
            'Pricing model is required'
        ],
        enum: [
            'free',
            'freemium',
            'paid'
        ]
    },
    pricingDetails: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Pricing details cannot exceed 500 characters'
        ]
    },
    screenshots: [
        {
            type: String,
            trim: true
        }
    ],
    submittedBy: {
        type: String,
        required: [
            true,
            'Submitter ID is required'
        ],
        trim: true
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    publishedAt: {
        type: Date
    },
    status: {
        type: String,
        required: true,
        enum: [
            'draft',
            'pending',
            'approved',
            'rejected'
        ],
        default: 'draft'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            'Review notes cannot exceed 1000 characters'
        ]
    },
    reviewedBy: {
        type: String,
        trim: true
    },
    reviewedAt: {
        type: Date
    },
    // 发布日期选择相关
    launchDateSelected: {
        type: Boolean,
        default: false
    },
    selectedLaunchDate: {
        type: Date
    },
    launchOption: {
        type: String,
        enum: [
            'free',
            'paid'
        ]
    },
    // 付费相关
    paymentRequired: {
        type: Boolean,
        default: false
    },
    paymentAmount: {
        type: Number,
        min: 0
    },
    paymentStatus: {
        type: String,
        enum: [
            'pending',
            'completed',
            'failed',
            'refunded'
        ]
    },
    orderId: {
        type: String,
        trim: true
    },
    paymentMethod: {
        type: String,
        trim: true
    },
    paidAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    likes: {
        type: Number,
        default: 0,
        min: 0
    },
    likedBy: [
        {
            type: String,
            trim: true
        }
    ],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes for better query performance
ToolSchema.index({
    status: 1,
    isActive: 1
});
ToolSchema.index({
    category: 1,
    status: 1
});
ToolSchema.index({
    tags: 1,
    status: 1
});
ToolSchema.index({
    submittedBy: 1
});
ToolSchema.index({
    publishedAt: -1
});
ToolSchema.index({
    views: -1
});
ToolSchema.index({
    likes: -1
});
// Text search index
ToolSchema.index({
    name: 'text',
    tagline: 'text',
    description: 'text',
    longDescription: 'text',
    tags: 'text'
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Tool || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Tool', ToolSchema);
}}),
"[project]/src/models/Order.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const OrderSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    userId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: [
            true,
            'User ID is required'
        ]
    },
    toolId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'Tool',
        required: [
            true,
            'Tool ID is required'
        ]
    },
    type: {
        type: String,
        required: true,
        enum: [
            'launch_date_priority'
        ],
        default: 'launch_date_priority'
    },
    amount: {
        type: Number,
        required: [
            true,
            'Amount is required'
        ],
        min: [
            0,
            'Amount must be positive'
        ]
    },
    currency: {
        type: String,
        required: true,
        default: 'CNY',
        enum: [
            'CNY',
            'USD'
        ]
    },
    status: {
        type: String,
        required: true,
        enum: [
            'pending',
            'completed',
            'failed',
            'cancelled',
            'refunded'
        ],
        default: 'pending'
    },
    // 支付相关
    paymentMethod: {
        type: String,
        trim: true
    },
    paymentIntentId: {
        type: String,
        trim: true
    },
    paymentSessionId: {
        type: String,
        trim: true
    },
    stripePaymentIntentId: {
        type: String,
        trim: true
    },
    stripeCustomerId: {
        type: String,
        trim: true
    },
    stripePaymentDetails: {
        paymentIntentId: String,
        amount: Number,
        currency: String,
        status: String,
        created: Date,
        failureReason: String
    },
    // 订单详情
    description: {
        type: String,
        required: [
            true,
            'Description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    selectedLaunchDate: {
        type: Date,
        required: [
            true,
            'Selected launch date is required'
        ]
    },
    // 时间戳
    paidAt: {
        type: Date
    },
    cancelledAt: {
        type: Date
    },
    refundedAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// 索引
OrderSchema.index({
    userId: 1,
    createdAt: -1
});
OrderSchema.index({
    toolId: 1
});
OrderSchema.index({
    status: 1
});
OrderSchema.index({
    paymentIntentId: 1
});
OrderSchema.index({
    paymentSessionId: 1
});
OrderSchema.index({
    stripePaymentIntentId: 1
});
OrderSchema.index({
    stripeCustomerId: 1
});
// 虚拟字段
OrderSchema.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true
});
OrderSchema.virtual('tool', {
    ref: 'Tool',
    localField: 'toolId',
    foreignField: '_id',
    justOne: true
});
// 实例方法
OrderSchema.methods.markAsPaid = function() {
    this.status = 'completed';
    this.paidAt = new Date();
    return this.save();
};
OrderSchema.methods.markAsFailed = function() {
    this.status = 'failed';
    return this.save();
};
OrderSchema.methods.cancel = function() {
    this.status = 'cancelled';
    this.cancelledAt = new Date();
    return this.save();
};
OrderSchema.methods.refund = function() {
    this.status = 'refunded';
    this.refundedAt = new Date();
    return this.save();
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Order || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Order', OrderSchema);
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const AccountSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    provider: {
        type: String,
        required: true,
        enum: [
            'google',
            'github',
            'email'
        ]
    },
    providerId: {
        type: String,
        required: true
    },
    providerAccountId: {
        type: String,
        required: true
    },
    accessToken: String,
    refreshToken: String,
    expiresAt: Date
}, {
    _id: false
});
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    name: {
        type: String,
        required: [
            true,
            'Name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Name cannot exceed 100 characters'
        ]
    },
    avatar: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        required: true,
        enum: [
            'user',
            'admin'
        ],
        default: 'user'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    // 认证相关
    emailVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: {
        type: String,
        trim: true
    },
    emailVerificationExpires: {
        type: Date
    },
    // OAuth账户关联
    accounts: [
        AccountSchema
    ],
    // 用户行为
    submittedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    likedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    comments: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Comment'
        }
    ],
    // 时间戳
    lastLoginAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
UserSchema.index({
    emailVerificationToken: 1
});
UserSchema.index({
    'accounts.provider': 1,
    'accounts.providerAccountId': 1
});
// 实例方法
UserSchema.methods.addAccount = function(account) {
    // 检查是否已存在相同的账户
    const existingAccount = this.accounts.find((acc)=>acc.provider === account.provider && acc.providerAccountId === account.providerAccountId);
    if (!existingAccount) {
        this.accounts.push(account);
    } else {
        // 更新现有账户信息
        Object.assign(existingAccount, account);
    }
};
UserSchema.methods.removeAccount = function(provider, providerAccountId) {
    this.accounts = this.accounts.filter((acc)=>!(acc.provider === provider && acc.providerAccountId === providerAccountId));
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/github.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
;
;
;
;
;
const authOptions = {
    // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略
    // adapter: MongoDBAdapter(client),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            id: 'email-code',
            name: 'Email Code',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                code: {
                    label: 'Code',
                    type: 'text'
                },
                token: {
                    label: 'Token',
                    type: 'text'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.code || !credentials?.token) {
                    return null;
                }
                try {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
                    // 查找用户
                    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                        email: credentials.email.toLowerCase(),
                        emailVerificationExpires: {
                            $gt: new Date()
                        }
                    });
                    if (!user) {
                        return null;
                    }
                    // 验证token和验证码
                    const storedData = user.emailVerificationToken;
                    if (!storedData || !storedData.includes(':')) {
                        return null;
                    }
                    const [storedToken, storedCode] = storedData.split(':');
                    if (storedToken !== credentials.token || storedCode !== credentials.code) {
                        return null;
                    }
                    // 验证成功，更新用户状态
                    user.emailVerified = true;
                    user.emailVerificationToken = undefined;
                    user.emailVerificationExpires = undefined;
                    user.lastLoginAt = new Date();
                    // 如果用户没有邮箱账户记录，添加一个
                    const hasEmailAccount = user.accounts.some((acc)=>acc.provider === 'email');
                    if (!hasEmailAccount) {
                        user.accounts.push({
                            provider: 'email',
                            providerId: 'email',
                            providerAccountId: user.email
                        });
                    }
                    await user.save();
                    return {
                        id: user._id.toString(),
                        email: user.email,
                        name: user.name,
                        image: user.avatar,
                        role: user.role
                    };
                } catch (error) {
                    console.error('Email code authorization error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async signIn ({ user, account, profile }) {
            // 对于credentials provider，用户已经在authorize中处理过了
            if (account?.provider === 'email-code') {
                return true;
            }
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            try {
                // 查找或创建用户（仅用于OAuth providers）
                let existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                    email: user.email
                });
                if (!existingUser) {
                    // 创建新用户
                    existingUser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                        email: user.email,
                        name: user.name || profile?.name || 'User',
                        avatar: user.image || profile?.image,
                        emailVerified: true,
                        lastLoginAt: new Date()
                    });
                    await existingUser.save();
                } else {
                    // 更新最后登录时间
                    existingUser.lastLoginAt = new Date();
                    await existingUser.save();
                }
                // 添加或更新账户信息
                if (account && account.provider !== 'email-code') {
                    existingUser.addAccount({
                        provider: account.provider,
                        providerId: account.provider,
                        providerAccountId: account.providerAccountId || account.id || '',
                        accessToken: account.access_token,
                        refreshToken: account.refresh_token,
                        expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined
                    });
                    await existingUser.save();
                }
                return true;
            } catch (error) {
                console.error('Sign in error:', error);
                return false;
            }
        },
        async jwt ({ token, user }) {
            if (user) {
                // 对于credentials provider，user对象已经包含了我们需要的信息
                token.userId = user.id;
                token.role = user.role || 'user';
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.userId;
                session.user.role = token.role;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/app/api/tools/[id]/launch-date/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/next/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Tool.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Order.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
;
;
;
;
;
;
;
async function POST(request, { params }) {
    try {
        // 检查用户认证
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '请先登录'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = await params;
        const { launchOption, selectedDate } = await request.json();
        // 验证ID格式
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Types.ObjectId.isValid(id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '无效的工具ID'
            }, {
                status: 400
            });
        }
        // 验证输入
        if (!launchOption || !selectedDate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '请选择发布选项和日期'
            }, {
                status: 400
            });
        }
        if (![
            'free',
            'paid'
        ].includes(launchOption)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '无效的发布选项'
            }, {
                status: 400
            });
        }
        // 获取用户信息
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: session.user.email
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '用户不存在'
            }, {
                status: 404
            });
        }
        // 查找工具
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '工具不存在'
            }, {
                status: 404
            });
        }
        // 检查工具所有权
        if (tool.submittedBy !== user._id.toString()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '您没有权限修改此工具'
            }, {
                status: 403
            });
        }
        // 检查工具状态
        if (tool.status !== 'draft') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '此工具已经选择了发布日期'
            }, {
                status: 400
            });
        }
        const selectedLaunchDate = new Date(selectedDate);
        const now = new Date();
        // 验证日期
        if (launchOption === 'free') {
            // 免费选项：必须是一个月后的日期
            const oneMonthLater = new Date();
            oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
            oneMonthLater.setHours(0, 0, 0, 0);
            const selectedDateOnly = new Date(selectedLaunchDate);
            selectedDateOnly.setHours(0, 0, 0, 0);
            if (selectedDateOnly.getTime() !== oneMonthLater.getTime()) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: '免费选项只能选择一个月后的日期'
                }, {
                    status: 400
                });
            }
        } else {
            // 付费选项：必须是明天或以后的日期
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            if (selectedLaunchDate < tomorrow) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: '付费选项最早只能选择明天的日期'
                }, {
                    status: 400
                });
            }
        }
        if (launchOption === 'free') {
            // 免费选项：直接更新工具状态并进入审核
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(id, {
                $set: {
                    launchDateSelected: true,
                    selectedLaunchDate,
                    launchOption: 'free',
                    paymentRequired: false,
                    status: 'pending' // 进入审核队列
                }
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    message: '发布日期设置成功，工具已进入审核队列'
                }
            });
        } else {
            // 付费选项：创建订单
            const paymentAmount = 1990; // 19.9元，以分为单位
            const order = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                userId: user._id,
                toolId: id,
                type: 'launch_date_priority',
                amount: paymentAmount,
                currency: 'USD',
                status: 'pending',
                description: `工具 "${tool.name}" 优先发布服务`,
                selectedLaunchDate
            });
            await order.save();
            // 更新工具状态
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(id, {
                $set: {
                    launchDateSelected: true,
                    selectedLaunchDate,
                    launchOption: 'paid',
                    paymentRequired: true,
                    paymentAmount,
                    paymentStatus: 'pending',
                    orderId: order._id.toString()
                }
            });
            // 这里应该集成真实的支付系统（如Stripe、支付宝等）
            // 现在返回一个模拟的支付URL
            const paymentUrl = `/payment/checkout?orderId=${order._id}`;
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    orderId: order._id,
                    paymentUrl,
                    amount: paymentAmount,
                    message: '订单创建成功，请完成支付'
                }
            });
        }
    } catch (error) {
        console.error('Launch date selection error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: '服务器错误'
        }, {
            status: 500
        });
    }
}
async function GET(request, { params }) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '请先登录'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = await params;
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Types.ObjectId.isValid(id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '无效的工具ID'
            }, {
                status: 400
            });
        }
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: session.user.email
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '用户不存在'
            }, {
                status: 404
            });
        }
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '工具不存在'
            }, {
                status: 404
            });
        }
        // 检查工具所有权
        if (tool.submittedBy !== user._id.toString()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '您没有权限访问此工具'
            }, {
                status: 403
            });
        }
        // 如果有订单，获取订单信息
        let order = null;
        if (tool.orderId) {
            order = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(tool.orderId);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                tool: {
                    id: tool._id,
                    name: tool.name,
                    status: tool.status,
                    launchDateSelected: tool.launchDateSelected,
                    selectedLaunchDate: tool.selectedLaunchDate,
                    launchOption: tool.launchOption,
                    paymentRequired: tool.paymentRequired,
                    paymentStatus: tool.paymentStatus
                },
                order
            }
        });
    } catch (error) {
        console.error('Get launch date info error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: '服务器错误'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0262f223._.js.map