(()=>{var e={};e.id=6935,e.ids=[6706,6935],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39020:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>u});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),c=r(96706);async function u(e){try{let{amount:t,currency:r="cny"}=await e.json();if(!t||t<=0)return i.NextResponse.json({success:!1,message:"无效的金额"},{status:400});let s=await (0,c.f)(t,r,{test:"true",description:"Stripe集成测试支付"});return i.NextResponse.json({success:!0,clientSecret:s.client_secret,paymentIntentId:s.id})}catch(e){return console.error("Error creating test payment intent:",e),i.NextResponse.json({success:!1,message:"创建支付失败"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test/create-payment-intent/route",pathname:"/api/test/create-payment-intent",filename:"route",bundlePath:"app/api/test/create-payment-intent/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function y(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96706:(e,t,r)=>{"use strict";r.d(t,{ZW:()=>i,bw:()=>a,f:()=>n,stripe:()=>s});let s=new(r(97877)).A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia",typescript:!0});async function n(e,t="cny",r={}){try{return await s.paymentIntents.create({amount:e,currency:t,metadata:r,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function o(e,t,r={}){try{return await s.customers.create({email:e,name:t,metadata:r})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function a(e,t,r={}){try{let n=await s.customers.list({email:e,limit:1});if(n.data.length>0)return n.data[0];return await o(e,t,r)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function i(e,t,r){try{return s.webhooks.constructEvent(e,t,r)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,7877],()=>r(39020));module.exports=s})();