(()=>{var e={};e.id=4604,e.ids=[4604,6706],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var i=r(36344),s=r(65752),n=r(13581),a=r(75745),o=r(17063);let c={providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,s]=r.split(":");if(i!==e.token||s!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,a.A)();try{let i=await o.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let n=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},a.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=s().models.User||s().model("User",a)},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31098:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),s=r.n(i);let n=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:i.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({userId:1,createdAt:-1}),n.index({toolId:1}),n.index({status:1}),n.index({paymentIntentId:1}),n.index({paymentSessionId:1}),n.index({stripePaymentIntentId:1}),n.index({stripeCustomerId:1}),n.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),n.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),n.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},n.methods.markAsFailed=function(){return this.status="failed",this.save()},n.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},n.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let a=s().models.Order||s().model("Order",n)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=s().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},77191:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>v,serverHooks:()=>x,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>I});var i={};r.r(i),r.d(i,{POST:()=>f});var s=r(96559),n=r(48088),a=r(37719),o=r(32190),c=r(35426),u=r(75745),d=r(31098),l=r(17063),p=r(12909),m=r(96706),y=r(56037),g=r.n(y);async function f(e){try{let t=await (0,c.getServerSession)(p.N);if(!t?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,u.A)();let{orderId:r}=await e.json();if(!g().Types.ObjectId.isValid(r))return o.NextResponse.json({success:!1,message:"无效的订单ID"},{status:400});let i=await l.A.findOne({email:t.user.email});if(!i)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let s=await d.A.findById(r);if(!s)return o.NextResponse.json({success:!1,message:"订单不存在"},{status:404});if(s.userId.toString()!==i._id.toString())return o.NextResponse.json({success:!1,message:"您没有权限操作此订单"},{status:403});if("pending"!==s.status)return o.NextResponse.json({success:!1,message:"订单状态异常，无法支付"},{status:400});if(s.stripePaymentIntentId)try{let e=await m.stripe.paymentIntents.retrieve(s.stripePaymentIntentId);if("succeeded"===e.status)return o.NextResponse.json({success:!1,message:"订单已支付"},{status:400});if("requires_payment_method"===e.status||"requires_confirmation"===e.status)return o.NextResponse.json({success:!0,data:{clientSecret:e.client_secret,paymentIntentId:e.id}})}catch(e){console.error("Error retrieving existing payment intent:",e)}console.log("user email..............",i.email,i);let n=await (0,m.bw)(i.email,i.name||void 0,{userId:i._id.toString(),orderId:s._id.toString()}),a=await (0,m.f)(s.amount,"usd",{orderId:s._id.toString(),userId:i._id.toString(),toolId:s.toolId.toString(),productType:"priority_launch"});return s.stripePaymentIntentId=a.id,s.stripeCustomerId=n.id,await s.save(),o.NextResponse.json({success:!0,data:{clientSecret:a.client_secret,paymentIntentId:a.id,amount:s.amount,currency:"cny"}})}catch(e){return console.error("Error creating payment intent:",e),o.NextResponse.json({success:!1,message:"创建支付失败"},{status:500})}}let v=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stripe/create-payment-intent/route",pathname:"/api/stripe/create-payment-intent",filename:"route",bundlePath:"app/api/stripe/create-payment-intent/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:I,serverHooks:x}=v;function S(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:I})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96706:(e,t,r)=>{"use strict";r.d(t,{ZW:()=>o,bw:()=>a,f:()=>s,stripe:()=>i});let i=new(r(97877)).A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia",typescript:!0});async function s(e,t="cny",r={}){try{return await i.paymentIntents.create({amount:e,currency:t,metadata:r,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function n(e,t,r={}){try{return await i.customers.create({email:e,name:t,metadata:r})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function a(e,t,r={}){try{let s=await i.customers.list({email:e,limit:1});if(s.data.length>0)return s.data[0];return await n(e,t,r)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function o(e,t,r){try{return i.webhooks.constructEvent(e,t,r)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580,4999,3136,7877],()=>r(77191));module.exports=i})();