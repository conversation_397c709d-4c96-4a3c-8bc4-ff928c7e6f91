(()=>{var e={};e.id=1014,e.ids=[1014],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>u});var i=t(36344),n=t(65752),o=t(13581),a=t(75745),s=t(17063);let u={providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,o.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let r=await s.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!r)return null;let t=r.emailVerificationToken;if(!t||!t.includes(":"))return null;let[i,n]=t.split(":");if(i!==e.token||n!==e.code)return null;return r.emailVerified=!0,r.emailVerificationToken=void 0,r.emailVerificationExpires=void 0,r.lastLoginAt=new Date,r.accounts.some(e=>"email"===e.provider)||r.accounts.push({provider:"email",providerId:"email",providerAccountId:r.email}),await r.save(),{id:r._id.toString(),email:r.email,name:r.name,image:r.avatar,role:r.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:r,profile:t}){if(r?.provider==="email-code")return!0;await (0,a.A)();try{let i=await s.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new s.A({email:e.email,name:e.name||t?.name||"User",avatar:e.image||t?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),r&&"email-code"!==r.provider&&(i.addAccount({provider:r.provider,providerId:r.provider,providerAccountId:r.providerAccountId||r.id||"",accessToken:r.access_token,refreshToken:r.refresh_token,expiresAt:r.expires_at?new Date(1e3*r.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:r})=>(r&&(e.userId=r.id,e.role=r.role||"user"),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.userId,e.user.role=r.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var i=t(56037),n=t.n(i);let o=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[o],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let r=this.accounts.find(r=>r.provider===e.provider&&r.providerAccountId===e.providerAccountId);r?Object.assign(r,e):this.accounts.push(e)},a.methods.removeAccount=function(e,r){this.accounts=this.accounts.filter(t=>t.provider!==e||t.providerAccountId!==r)};let s=n().models.User||n().model("User",a)},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=n?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(i,o,s):i[o]=e[o]}return i.default=e,t&&t.set(e,i),i}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},25709:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var i={};t.r(i),t.d(i,{GET:()=>l,POST:()=>l});var n=t(96559),o=t(48088),a=t(37719),s=t(19854),u=t.n(s),c=t(12909);let l=u()(c.N),d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=d;function v(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var i=t(56037),n=t.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let s=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(o,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4243,4999,3136],()=>t(25709));module.exports=i})();