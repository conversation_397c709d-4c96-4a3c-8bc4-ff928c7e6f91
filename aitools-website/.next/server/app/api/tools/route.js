(()=>{var e={};e.id=3839,e.ids=[3839],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),s=r.n(i);let n=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({publishedAt:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let a=s().models.Tool||s().model("Tool",n)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=s().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},96487:()=>{},96855:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{GET:()=>p,POST:()=>d});var s=r(96559),n=r(48088),a=r(37719),o=r(32190),c=r(75745),u=r(30762);async function p(e){try{await (0,c.A)();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"12"),s=t.get("category"),n=t.get("status"),a=t.get("search"),p=t.get("sort")||"createdAt",d=t.get("order")||"desc",l=t.get("dateFrom"),g=t.get("dateTo"),m={};s&&"all"!==s&&(m.category=s),n&&"all"!==n?m.status=n:m.status="approved",a&&(m.$or=[{name:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}},{tags:{$in:[RegExp(a,"i")]}}]),(l||g)&&(m.publishedAt={},l&&(m.publishedAt.$gte=new Date(l)),g&&(m.publishedAt.$lte=new Date(g)));let y=(r-1)*i,x={};x[p]="desc"===d?-1:1;let h=await u.A.find(m).sort(x).skip(y).limit(i).select("-submittedBy -reviewNotes -reviewedBy").lean(),v=await u.A.countDocuments(m),w=Math.ceil(v/i);return o.NextResponse.json({success:!0,data:{tools:h,pagination:{currentPage:r,totalPages:w,totalItems:v,itemsPerPage:i,hasNextPage:r<w,hasPrevPage:r>1}}})}catch(e){return console.error("Error fetching tools:",e),o.NextResponse.json({success:!1,error:"获取工具列表失败"},{status:500})}}async function d(e){try{await (0,c.A)();let t=await e.json();for(let e of["name","description","website","category","pricing","submitterName","submitterEmail"])if(!t[e])return o.NextResponse.json({success:!1,error:`${e} 是必需的`},{status:400});if(await u.A.findOne({name:t.name}))return o.NextResponse.json({success:!1,error:"该工具名称已存在"},{status:400});let r={name:t.name,description:t.description,longDescription:t.longDescription,website:t.website,logo:t.logo,category:t.category,pricing:t.pricing,pricingDetails:t.pricingDetails,tags:t.tags||[],features:t.features||[],screenshots:t.screenshots||[],submittedBy:t.submitterName,submittedAt:new Date,publishedAt:t.publishDate?new Date(t.publishDate):void 0,status:"pending",views:0,likes:0,isActive:!0},i=new u.A(r);return await i.save(),o.NextResponse.json({success:!0,data:i,message:"工具提交成功，等待审核"},{status:201})}catch(e){if(console.error("Error creating tool:",e),"ValidationError"===e.name){let t=Object.values(e.errors).map(e=>e.message);return o.NextResponse.json({success:!1,error:"验证失败",details:t},{status:400})}return o.NextResponse.json({success:!1,error:"创建工具失败"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tools/route",pathname:"/api/tools",filename:"route",bundlePath:"app/api/tools/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:y}=l;function x(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580],()=>r(96855));module.exports=i})();