(()=>{var e={};e.id=3135,e.ids=[3135],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>y,serverHooks:()=>k,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>v});var i={};r.r(i),r.d(i,{GET:()=>g,POST:()=>m});var n=r(96559),s=r(48088),o=r(37719),a=r(32190),l=r(19854),d=r(12909),c=r(75745),u=r(30762),p=r(17063);async function m(e,{params:t}){try{let r=await (0,l.getServerSession)(d.N);if(!r?.user?.email)return a.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,c.A)();let i=await p.A.findOne({email:r.user.email});if(!i)return a.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let{id:n}=await t,s=await u.A.findById(n);if(!s)return a.NextResponse.json({success:!1,message:"工具不存在"},{status:404});let o=await e.json().catch(()=>({})),m=!0===o.forceUnlike;if(console.log("force unlike..........",m,n,i._id,s.likedBy,i.likedTools),m)return s.likedBy=s.likedBy.filter(e=>e?.toString()!==i._id.toString()),i.likedTools=i.likedTools.filter(e=>e?.toString()!==n),s.likes=s.likedBy.length,await s.save(),await i.save(),a.NextResponse.json({success:!0,data:{liked:!1,likes:s.likes}});let g=s.likedBy.includes(i._id.toString()),y=i.likedTools.includes(n);g!==y&&console.warn(`Data inconsistency detected for user ${i._id} and tool ${n}:`,{hasLikedInTool:g,hasLikedInUser:y,toolLikedBy:s.likedBy,userLikedTools:i.likedTools});let f=g||y;return f?(s.likedBy=s.likedBy.filter(e=>e!==i._id.toString()),i.likedTools=i.likedTools.filter(e=>e!==n)):(s.likedBy.includes(i._id.toString())||s.likedBy.push(i._id.toString()),i.likedTools.includes(n)||i.likedTools.push(n)),s.likes=s.likedBy.length,await s.save(),await i.save(),a.NextResponse.json({success:!0,data:{liked:!f,likes:s.likes}})}catch(e){return console.error("Like tool error:",e),a.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}async function g(e,{params:t}){try{let e=await (0,l.getServerSession)(d.N);await (0,c.A)();let{id:r}=await t,i=await u.A.findById(r);if(!i)return a.NextResponse.json({success:!1,message:"工具不存在"},{status:404});let n=!1;if(e?.user?.email){let t=await p.A.findOne({email:e.user.email});if(t){let e=i.likedBy.includes(t._id.toString()),s=t.likedTools.includes(r);e!==s&&console.warn(`Data inconsistency detected in GET for user ${t._id} and tool ${r}:`,{hasLikedInTool:e,hasLikedInUser:s}),n=e||s}}let s=i.likedBy.length;return i.likes!==s&&(console.warn(`Likes count inconsistency for tool ${r}: stored=${i.likes}, actual=${s}`),i.likes=s,await i.save()),a.NextResponse.json({success:!0,data:{liked:n,likes:i.likes}})}catch(e){return console.error("Get like status error:",e),a.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}let y=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/tools/[id]/like/route",pathname:"/api/tools/[id]/like",filename:"route",bundlePath:"app/api/tools/[id]/like/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:f,workUnitAsyncStorage:v,serverHooks:k}=y;function x(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:v})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var i=r(36344),n=r(65752),s=r(13581),o=r(75745),a=r(17063);let l={providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,s.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await a.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,n]=r.split(":");if(i!==e.token||n!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,o.A)();try{let i=await a.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new a.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),n=r.n(i);let s=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[s],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let a=n().models.User||n().model("User",o)},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var n=r(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var a=n?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(i,s,a):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let s=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({status:1,isActive:1}),s.index({category:1,status:1}),s.index({tags:1,status:1}),s.index({submittedBy:1}),s.index({publishedAt:-1}),s.index({views:-1}),s.index({likes:-1}),s.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=n().models.Tool||n().model("Tool",s)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),n=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let a=async function(){if(o.conn)return o.conn;o.promise||(o.promise=n().connect(s,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580,4999,3136],()=>r(5490));module.exports=i})();