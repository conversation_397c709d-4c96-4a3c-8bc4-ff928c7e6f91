(()=>{var e={};e.id=8439,e.ids=[8439],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(56037),n=r.n(i);let a=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({publishedAt:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let s=n().models.Tool||n().model("Tool",a)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},56132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>u});var n=r(96559),a=r(48088),s=r(37719),o=r(32190),d=r(75745),p=r(30762);async function u(e){try{await (0,d.A)();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"12"),n=t.get("status"),a=t.get("search"),s=t.get("sort")||"submittedAt",u=t.get("order")||"desc",c={};n&&"all"!==n&&(c.status=n),a&&(c.$or=[{name:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}},{submittedBy:{$regex:a,$options:"i"}},{tags:{$in:[RegExp(a,"i")]}}]);let l=(r-1)*i,g={};g[s]="desc"===u?-1:1;let m=await p.A.find(c).sort(g).skip(l).limit(i).lean(),y=await p.A.countDocuments(c),x=Math.ceil(y/i),h=await p.A.aggregate([{$group:{_id:"$status",count:{$sum:1}}}]),v={total:y,draft:h.find(e=>"draft"===e._id)?.count||0,pending:h.find(e=>"pending"===e._id)?.count||0,approved:h.find(e=>"approved"===e._id)?.count||0,rejected:h.find(e=>"rejected"===e._id)?.count||0};return o.NextResponse.json({success:!0,data:{tools:m,stats:v,pagination:{currentPage:r,totalPages:x,totalItems:y,itemsPerPage:i,hasNextPage:r<x,hasPrevPage:r>1}}})}catch(e){return console.error("Error fetching admin tools:",e),o.NextResponse.json({success:!1,error:"获取工具列表失败"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/tools/route",pathname:"/api/admin/tools",filename:"route",bundlePath:"app/api/admin/tools/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=c;function y(){return(0,s.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let o=async function(){if(s.conn)return s.conn;s.promise||(s.promise=n().connect(a,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580],()=>r(56132));module.exports=i})();