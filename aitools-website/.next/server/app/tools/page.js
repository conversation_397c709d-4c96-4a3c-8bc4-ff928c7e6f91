(()=>{var e={};e.id=3554,e.ids=[3554],e.modules={2964:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(60687);t(43210);var a=t(85814),l=t.n(a),i=t(25334),n=t(13861),o=t(67760),d=t(7485);let c=({tool:e,onLoginRequired:s,onUnlike:t,isInLikedPage:a=!1})=>(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,r.jsx)("img",{src:e.logo,alt:e.name,className:"w-12 h-12 rounded-lg object-cover"}):(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(e=>{switch(e){case"free":return"bg-green-100 text-green-800";case"freemium":return"bg-blue-100 text-blue-800";case"paid":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(e.pricing)}`,children:(e=>{switch(e){case"free":return"免费";case"freemium":return"免费增值";case"paid":return"付费";default:return e}})(e.pricing)})]})]}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,s)=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},s)),e.tags.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.views})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.likes})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{toolId:e._id,initialLikes:e.likes,onLoginRequired:s,onUnlike:t,isInLikedPage:a}),(0,r.jsx)(l(),{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18575:(e,s,t)=>{Promise.resolve().then(t.bind(t,79456))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},26823:(e,s,t)=>{Promise.resolve().then(t.bind(t,73314))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73314:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(60687),a=t(43210),l=t(98402),i=t(2964),n=t(33823),o=t(11011);t(62185);var d=t(99270),c=t(80462),x=t(78272),u=t(6943),m=t(25366);let p=[{value:"",label:"所有分类"},{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"}],h=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],g=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function b(){let[e,s]=(0,a.useState)([]),[t,b]=(0,a.useState)(!0),[v,f]=(0,a.useState)(""),[j,y]=(0,a.useState)(""),[w,N]=(0,a.useState)(""),[k,A]=(0,a.useState)(""),[C,_]=(0,a.useState)("popular"),[P,q]=(0,a.useState)("grid"),[M,L]=(0,a.useState)(!1),S=[...e.filter(e=>{let s=e.name.toLowerCase().includes(j.toLowerCase())||e.description.toLowerCase().includes(j.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(j.toLowerCase())),t=!w||e.category===w,r=!k||e.pricing===k;return s&&t&&r})].sort((e,s)=>{switch(C){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":return new Date(s.createdAt||"").getTime()-new Date(e.createdAt||"").getTime();default:return 0}});return t?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,r.jsx)(n.A,{size:"lg"})})})}):v?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(o.A,{message:v})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI 工具目录"}),(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:["发现 ",e.length," 个精选的 AI 工具，提升您的工作效率"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或标签...",value:j,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(d.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"md:hidden mb-4",children:(0,r.jsxs)("button",{onClick:()=>L(!M),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,r.jsx)(x.A,{className:`ml-2 h-4 w-4 transform ${M?"rotate-180":""}`})]})}),(0,r.jsxs)("div",{className:`grid grid-cols-1 md:grid-cols-4 gap-4 ${M?"block":"hidden md:grid"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"分类"}),(0,r.jsx)("select",{value:w,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:p.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,r.jsx)("select",{value:k,onChange:e=>A(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:h.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,r.jsx)("select",{value:C,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:g.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,r.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,r.jsx)("button",{onClick:()=>q("grid"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${"grid"===P?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(u.A,{className:"h-4 w-4 mx-auto"})}),(0,r.jsx)("button",{onClick:()=>q("list"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${"list"===P?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(m.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["显示 ",S.length," 个结果",j&&` 搜索 "${j}"`,w&&` 在 "${p.find(e=>e.value===w)?.label}"`]})}),S.length>0?(0,r.jsx)("div",{className:"grid"===P?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:S.map(e=>(0,r.jsx)(i.A,{tool:e},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]})]})})}},78272:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79456:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},86726:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["tools",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79456)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tools/page",pathname:"/tools",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707,5319],()=>t(86726));module.exports=r})();