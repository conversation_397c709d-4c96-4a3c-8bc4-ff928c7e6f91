(()=>{var e={};e.id=1731,e.ids=[1731],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(93613),l=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15068:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(60687),a=t(43210),l=t(82136),i=t(16189),n=t(98402),c=t(33823),o=t(11011),d=t(78890),m=t(48577),x=t(16023),u=t(62688);let h=(0,u.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),p=(0,u.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),g=["AI助手","ChatGPT","对话AI","智能问答","语言模型","写作助手","内容生成","文案创作","博客写作","营销文案","图像生成","图像编辑","AI绘画","头像生成","背景移除","视频生成","视频编辑","视频剪辑","短视频制作","视频字幕","语音合成","语音识别","音乐生成","语音转文字","文字转语音","代码生成","代码补全","代码审查","开发助手","低代码平台","数据分析","数据可视化","商业智能","机器学习","深度学习","办公自动化","文档处理","项目管理","团队协作","笔记工具","UI设计","Logo设计","网页设计","平面设计","原型设计","SEO优化","社交媒体营销","邮件营销","内容营销","市场分析","机器翻译","实时翻译","文档翻译","语音翻译"];var b=t(11860),j=t(99270),f=t(37360);function v({selectedTags:e,onTagsChange:s,maxTags:t=3}){let[l,i]=(0,a.useState)(""),[n,c]=(0,a.useState)(!1),o=r=>{e.includes(r)?s(e.filter(e=>e!==r)):e.length<t&&s([...e,r])},d=t=>{s(e.filter(e=>e!==t))},m=g.filter(s=>s.toLowerCase().includes(l.toLowerCase())&&!e.includes(s));return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"选择标签"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["已选择 ",e.length,"/",t," 个标签"]})]}),e.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"已选择的标签："}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[e,(0,r.jsx)("button",{type:"button",onClick:()=>d(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,r.jsx)(b.A,{className:"h-3 w-3"})})]},e))})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["选择标签（最多",t,"个）"]}),(0,r.jsxs)("div",{className:"relative mb-3",children:[(0,r.jsx)("input",{type:"text",placeholder:"搜索标签...",value:l,onChange:e=>i(e.target.value),onFocus:()=>c(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(j.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(n||l)&&(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:m.length>0?(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 gap-1",children:m.map(s=>{let a=e.length>=t;return(0,r.jsx)("button",{type:"button",onClick:()=>{o(s),i(""),c(!1)},disabled:a,className:`
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${a?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700"}
                            `,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-2 text-gray-400"}),s]})},s)})}),m.length>50&&(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:["找到 ",m.length," 个匹配标签"]})]}):(0,r.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:l?"未找到匹配的标签":"开始输入以搜索标签"})})})]})}),(n||l)&&(0,r.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{c(!1),i("")}}),e.length>=t&&(0,r.jsxs)("p",{className:"text-sm text-amber-600",children:["最多只能选择",t,"个标签"]})]})}let y=[{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"},{value:"translation",label:"语言翻译"},{value:"search-engines",label:"搜索引擎"},{value:"education",label:"教育学习"},{value:"marketing",label:"营销工具"},{value:"productivity",label:"生产力工具"},{value:"customer-service",label:"客户服务"}],N=[{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}];function w(){let{data:e,status:s}=(0,l.useSession)(),t=(0,i.useRouter)(),[u,g]=(0,a.useState)({name:"",tagline:"",description:"",longDescription:"",website:"",logo:"",category:"",tags:[],pricing:"",pricingDetails:"",screenshots:[]}),[b,j]=(0,a.useState)(""),[f,w]=(0,a.useState)(!1),[k,A]=(0,a.useState)("idle"),[C,P]=(0,a.useState)({}),[S,$]=(0,a.useState)(""),[_,I]=(0,a.useState)(!1),[q,M]=(0,a.useState)(null),[E,L]=(0,a.useState)(""),G=()=>{let e={};return u.name.trim()||(e.name="工具名称是必填项"),u.description.trim()||(e.description="工具描述是必填项"),u.website.trim()||(e.website="官方网站是必填项"),u.category||(e.category="请选择一个分类"),u.pricing||(e.pricing="请选择价格模式"),u.website&&!u.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),P(e),0===Object.keys(e).length},T=async s=>{if(s.preventDefault(),!e)return void I(!0);if(G()){w(!0),A("idle");try{let e=u.logo;if(q){let s=new FormData;s.append("logo",q);let t=await fetch("/api/upload/logo",{method:"POST",body:s}),r=await t.json();if(r.success)e=r.data.url;else throw Error(r.message||"Logo上传失败")}let s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:u.name,tagline:u.tagline,description:u.description,longDescription:u.longDescription,website:u.website,logo:e||void 0,category:u.category,tags:u.tags,pricing:u.pricing,pricingDetails:u.pricingDetails,screenshots:u.screenshots})}),r=await s.json();r.success?t.push(`/submit/launch-date/${r.data.toolId}`):(A("error"),$(r.message||"提交失败，请重试"))}catch(e){console.error("Error submitting tool:",e),A("error"),$("网络错误，请检查连接后重试")}finally{w(!1)}}};return(0,r.jsxs)(n.A,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)(x.A,{className:"inline-block mr-3 h-8 w-8 text-blue-600"}),"提交 AI 工具"]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。"})]}),"success"===k&&(0,r.jsx)(d.A,{message:S||"工具提交成功！我们会在 1-3 个工作日内审核您的提交。",onClose:()=>A("idle"),className:"mb-6"}),"error"===k&&(0,r.jsx)(o.A,{message:S||"提交失败，请检查网络连接后重试。",onClose:()=>A("idle"),className:"mb-6"}),(0,r.jsxs)("form",{onSubmit:T,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,r.jsx)("input",{type:"text",value:u.name,onChange:e=>g(s=>({...s,name:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.name?"border-red-300":"border-gray-300"}`,placeholder:"例如：ChatGPT"}),C.name&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语（可选）"}),(0,r.jsx)("input",{type:"text",value:u.tagline,onChange:e=>g(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"简短描述工具的核心价值"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"url",value:u.website,onChange:e=>g(s=>({...s,website:e.target.value})),className:`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.website?"border-red-300":"border-gray-300"}`,placeholder:"https://example.com"}),(0,r.jsx)(h,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),C.website&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.website})]})}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,r.jsx)("textarea",{value:u.description,onChange:e=>g(s=>({...s,description:e.target.value})),rows:4,className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.description?"border-red-300":"border-gray-300"}`,placeholder:"详细描述这个 AI 工具的功能和特点..."}),C.description&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.description})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo图片（可选）"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];if(s){M(s);let e=new FileReader;e.onload=e=>{L(e.target?.result)},e.readAsDataURL(s)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),E&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{src:E,alt:"Logo预览",className:"w-16 h-16 object-cover rounded-lg border border-gray-300"})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB"})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,r.jsxs)("select",{value:u.category,onChange:e=>g(s=>({...s,category:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.category?"border-red-300":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"",children:"请选择分类"}),y.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),C.category&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,r.jsxs)("select",{value:u.pricing,onChange:e=>g(s=>({...s,pricing:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.pricing?"border-red-300":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"",children:"请选择价格模式"}),N.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),C.pricing&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.pricing})]})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(v,{selectedTags:u.tags,onTagsChange:e=>g(s=>({...s,tags:e})),maxTags:3})}),e&&(0,r.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 mb-2",children:"提交者信息"}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:["提交者：",e.user?.name||e.user?.email]}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:["邮箱：",e.user?.email]})]}),(0,r.jsx)("div",{className:"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(p,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"提交指南"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• 请确保提交的是真实存在且可正常访问的 AI 工具"}),(0,r.jsx)("li",{children:"• 工具描述应该准确、客观，避免过度营销"}),(0,r.jsx)("li",{children:"• 我们会在 1-3 个工作日内审核您的提交"}),(0,r.jsx)("li",{children:"• 审核通过后，工具将出现在我们的目录中"}),(0,r.jsx)("li",{children:"• 如有问题，我们会通过邮箱联系您"})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:f,className:`px-8 py-3 rounded-lg font-medium transition-colors ${f?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:f?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{size:"sm",className:"mr-2"}),"提交中..."]}):"提交工具"})})]})]}),(0,r.jsx)(m.A,{isOpen:_,onClose:()=>I(!1)})]})}},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o={children:["",{children:["submit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,83839)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/submit/page",pathname:"/submit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},36332:(e,s,t)=>{Promise.resolve().then(t.bind(t,15068))},37360:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},46604:(e,s,t)=>{Promise.resolve().then(t.bind(t,83839))},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:d,...m},x)=>(0,r.createElement)("svg",{ref:x,...o,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:n("lucide",l),...!i&&!c(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},c)=>(0,r.createElement)(d,{ref:c,iconNode:s,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78890:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(5336),l=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},79551:e=>{"use strict";e.exports=require("url")},83839:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx","default")},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},98402:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687);t(43210);var a=t(85814),l=t.n(a);let i=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707],()=>t(29160));module.exports=r})();