(()=>{var e={};e.id=120,e.ids=[120],e.modules={862:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(60687),a=t(43210),l=t(16189),i=t(82136),o=t(98402),n=t(33823),d=t(48577),c=t(28559),m=t(16023),x=t(11860),u=t(96474),p=t(85814),h=t.n(p);function g({params:e}){let{data:s,status:t}=(0,i.useSession)(),p=(0,l.useRouter)(),[g,b]=(0,a.useState)(""),[f,j]=(0,a.useState)(null),[v,y]=(0,a.useState)(!0),[N,w]=(0,a.useState)(!1),[A,k]=(0,a.useState)(!1),[C,P]=(0,a.useState)({}),[I,S]=(0,a.useState)("idle"),[_,D]=(0,a.useState)(""),[$,q]=(0,a.useState)(null),[E,G]=(0,a.useState)(""),[M,L]=(0,a.useState)(!1),[U,T]=(0,a.useState)(""),[z,O]=(0,a.useState)(""),[R,F]=(0,a.useState)({name:"",tagline:"",description:"",longDescription:"",website:"",logo:"",category:"",tags:[],pricing:"",pricingDetails:"",screenshots:[]}),K=()=>{let e={};return R.name.trim()||(e.name="工具名称是必填项"),R.description.trim()||(e.description="工具描述是必填项"),R.website.trim()||(e.website="官方网站是必填项"),R.category||(e.category="请选择一个分类"),R.pricing||(e.pricing="请选择价格模式"),R.website&&!R.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),P(e),0===Object.keys(e).length},W=async e=>{if(e.preventDefault(),!s)return void k(!0);if(K()){w(!0),S("idle");try{let e=await fetch(`/api/tools/${g}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:R.name,tagline:R.tagline,description:R.description,longDescription:R.longDescription,website:R.website,logo:E||void 0,category:R.category,tags:R.tags,pricing:R.pricing,pricingDetails:R.pricingDetails,screenshots:R.screenshots})}),s=await e.json();s.success?(S("success"),D("工具信息更新成功！"),setTimeout(()=>{p.push("/profile/submitted")},2e3)):(S("error"),D(s.error||"更新失败，请重试"))}catch(e){console.error("更新工具失败:",e),S("error"),D("网络错误，请重试")}finally{w(!1)}}},H=async e=>{if(!e)return;L(!0);let s=new FormData;s.append("logo",e);try{let e=await fetch("/api/upload/logo",{method:"POST",body:s}),t=await e.json();t.success?(G(t.data.url),F(e=>({...e,logo:t.data.url}))):(S("error"),D(t.message||"上传失败"))}catch(e){console.error("上传失败:",e),S("error"),D("上传失败，请重试")}finally{L(!1)}},J=()=>{U.trim()&&!R.tags.includes(U.trim())&&R.tags.length<3&&(F(e=>({...e,tags:[...e.tags,U.trim()]})),T(""))},Z=e=>{F(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},B=()=>{z.trim()&&!R.screenshots.includes(z.trim())&&(F(e=>({...e,screenshots:[...e.screenshots,z.trim()]})),O(""))},X=e=>{F(s=>({...s,screenshots:s.screenshots.filter(s=>s!==e)}))};return"loading"===t||v?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(n.A,{size:"lg"})})}):s?f?(0,r.jsxs)(o.A,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)(h(),{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"返回工具列表"]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"编辑工具信息"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"更新您的工具信息，让更多用户了解您的产品"})]}),"success"===I&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-green-800",children:_})}),"error"===I&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-800",children:_})}),(0,r.jsxs)("form",{onSubmit:W,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,r.jsx)("input",{type:"text",value:R.name,onChange:e=>F(s=>({...s,name:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.name?"border-red-300":"border-gray-300"}`,placeholder:"例如：ChatGPT"}),C.name&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语"}),(0,r.jsx)("input",{type:"text",value:R.tagline,onChange:e=>F(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：AI驱动的对话助手"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,r.jsx)("textarea",{value:R.description,onChange:e=>F(s=>({...s,description:e.target.value})),rows:3,className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.description?"border-red-300":"border-gray-300"}`,placeholder:"简要描述您的工具功能和特点..."}),C.description&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.description})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"详细描述"}),(0,r.jsx)("textarea",{value:R.longDescription,onChange:e=>F(s=>({...s,longDescription:e.target.value})),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"详细介绍您的工具，包括主要功能、使用场景、技术特点等..."})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,r.jsx)("input",{type:"url",value:R.website,onChange:e=>F(s=>({...s,website:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.website?"border-red-300":"border-gray-300"}`,placeholder:"https://example.com"}),C.website&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.website})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"工具Logo"}),(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"上传Logo图片"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];s&&(q(s),H(s))},className:"hidden",id:"logo-upload"}),(0,r.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:M?"上传中...":"点击上传或拖拽图片到此处"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"支持 PNG, JPG, GIF 格式，建议尺寸 200x200px"})]})]})]}),E&&(0,r.jsx)("div",{className:"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:E,alt:"Logo预览",className:"w-full h-full object-cover"})})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,r.jsxs)("select",{value:R.category,onChange:e=>F(s=>({...s,category:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.category?"border-red-300":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"",children:"请选择分类"}),[{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"},{value:"design-tools",label:"设计工具"},{value:"productivity",label:"生产力工具"},{value:"customer-service",label:"客户服务"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),C.category&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,r.jsxs)("select",{value:R.pricing,onChange:e=>F(s=>({...s,pricing:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${C.pricing?"border-red-300":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"",children:"请选择价格模式"}),[{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),C.pricing&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.pricing})]})]}),R.pricing&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格详情"}),(0,r.jsx)("textarea",{value:R.pricingDetails,onChange:e=>F(s=>({...s,pricingDetails:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"详细说明价格信息，如免费版功能、付费版价格等..."})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"最多选择3个标签来描述您的工具"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:R.tags.map((e,s)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full",children:[e,(0,r.jsx)("button",{type:"button",onClick:()=>Z(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,r.jsx)(x.A,{className:"h-3 w-3"})})]},s))}),R.tags.length<3&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"text",value:U,onChange:e=>T(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),J()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入自定义标签",maxLength:20}),(0,r.jsx)("button",{type:"button",onClick:J,disabled:!U.trim()||R.tags.includes(U.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"或选择预定义标签："}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:["AI写作","AI绘画","AI视频","AI音频","AI编程","AI翻译","AI客服","AI分析","自然语言处理","计算机视觉","机器学习","深度学习","语音识别","图像识别","文本生成","图像生成","视频生成","音频生成","代码生成","数据分析","聊天机器人","虚拟助手","自动化","智能推荐","内容创作","设计工具","生产力工具","教育工具","营销工具","开发工具","API服务","云服务","移动应用","网页应用","桌面应用","浏览器插件","开源","商业","企业级","个人使用","团队协作","实时处理","批量处理","多语言支持"].filter(e=>!R.tags.includes(e)).map((e,s)=>(0,r.jsx)("button",{type:"button",onClick:()=>{R.tags.length<3&&F(s=>({...s,tags:[...s.tags,e]}))},className:"px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 transition-colors",children:e},s))})]})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"产品截图"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"添加产品截图来展示您的工具界面和功能"}),R.screenshots.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-4",children:R.screenshots.map((e,s)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("img",{src:e,alt:`截图 ${s+1}`,className:"w-full h-32 object-cover rounded-lg border border-gray-300"}),(0,r.jsx)("button",{type:"button",onClick:()=>X(e),className:"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity",children:(0,r.jsx)(x.A,{className:"h-3 w-3"})})]},s))}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"url",value:z,onChange:e=>O(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),B()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入截图URL"}),(0,r.jsx)("button",{type:"button",onClick:B,disabled:!z.trim()||R.screenshots.includes(z.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:N,className:`px-8 py-3 rounded-lg font-medium transition-colors ${N?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:N?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{size:"sm",className:"mr-2"}),"更新中..."]}):"更新工具信息"})})]})]}),(0,r.jsx)(d.A,{isOpen:A,onClose:()=>k(!1)})]}):(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"工具不存在"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"您要编辑的工具不存在或已被删除"}),(0,r.jsx)(h(),{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回工具列表"})]})})}):(0,r.jsxs)(o.A,{children:[(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"请先登录"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"您需要登录后才能编辑工具信息"}),(0,r.jsx)("button",{onClick:()=>k(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"登录"})]})}),(0,r.jsx)(d.A,{isOpen:A,onClose:()=>k(!1)})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let d={children:["",{children:["submit",{children:["edit",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23128)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/submit/edit/[toolId]/page",pathname:"/submit/edit/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23128:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx","default")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},40673:(e,s,t)=>{Promise.resolve().then(t.bind(t,23128))},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},o=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),n=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:c,...m},x)=>(0,r.createElement)("svg",{ref:x,...d,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:o("lucide",l),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},n)=>(0,r.createElement)(c,{ref:n,iconNode:s,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},77121:(e,s,t)=>{Promise.resolve().then(t.bind(t,862))},79551:e=>{"use strict";e.exports=require("url")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},98402:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687);t(43210);var a=t(85814),l=t.n(a);let i=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707],()=>t(6160));module.exports=r})();