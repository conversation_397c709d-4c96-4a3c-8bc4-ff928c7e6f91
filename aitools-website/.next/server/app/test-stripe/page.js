(()=>{var e={};e.id=311,e.ids=[311],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3998:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["test-stripe",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52693)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-stripe/page",pathname:"/test-stripe",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24303:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(60687),a=s(43210),i=s(39010),o=s(46299);let n=(0,i.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function l(){let e=(0,o.t2)(),t=(0,o.HH)(),[s,i]=(0,a.useState)(!1),[n,l]=(0,a.useState)(""),d=async s=>{if(s.preventDefault(),!e||!t)return;i(!0);let{error:r}=await e.confirmPayment({elements:t,confirmParams:{return_url:`${window.location.origin}/test-stripe`},redirect:"if_required"});r?l(r.message||"支付失败"):l("支付成功！"),i(!1)};return(0,r.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,r.jsx)(o.He,{}),(0,r.jsx)("button",{disabled:!e||!t||s,className:"w-full bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50",children:s?"处理中...":"支付 \xa599.00"}),n&&(0,r.jsx)("div",{className:"text-center text-sm",children:n})]})}function d(){let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)(!1),d=async()=>{i(!0);try{let e=await fetch("/api/test/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:9900,currency:"cny"})}),s=await e.json();s.success?t(s.clientSecret):alert("创建支付失败: "+s.message)}catch(e){alert("创建支付失败")}finally{i(!1)}};return(0,r.jsxs)("div",{className:"max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Stripe 支付测试"}),e?(0,r.jsx)(o.S8,{stripe:n,options:{clientSecret:e,appearance:{theme:"stripe"}},children:(0,r.jsx)(l,{})}):(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("button",{onClick:d,disabled:s,className:"bg-green-600 text-white py-2 px-4 rounded disabled:opacity-50",children:s?"创建中...":"创建测试支付"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"点击创建一个 \xa599.00 的测试支付"})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-100 rounded text-sm",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"测试卡号:"}),(0,r.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,r.jsx)("li",{children:"成功: 4242 4242 4242 4242"}),(0,r.jsx)("li",{children:"失败: 4000 0000 0000 0002"}),(0,r.jsx)("li",{children:"需要验证: 4000 0025 0000 3155"})]}),(0,r.jsx)("p",{className:"mt-2 text-xs",children:"使用任意未来日期作为过期时间，任意3位数作为CVC"})]})]})}},25430:(e,t,s)=>{Promise.resolve().then(s.bind(s,52693))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52693:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72286:(e,t,s)=>{Promise.resolve().then(s.bind(s,24303))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,865,1658,7580,6707],()=>s(3998));module.exports=r})();