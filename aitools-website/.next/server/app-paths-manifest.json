{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/orders/[id]/route": "app/api/orders/[id]/route.js", "/api/stripe/create-payment-intent/route": "app/api/stripe/create-payment-intent/route.js", "/api/tools/[id]/launch-date/route": "app/api/tools/[id]/launch-date/route.js", "/api/tools/[id]/like/route": "app/api/tools/[id]/like/route.js", "/api/tools/[id]/route": "app/api/tools/[id]/route.js", "/api/tools/route": "app/api/tools/route.js", "/api/upload/logo/route": "app/api/upload/logo/route.js", "/api/user/tools/route": "app/api/user/tools/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/payment/checkout/page": "app/payment/checkout/page.js", "/profile/submitted/page": "app/profile/submitted/page.js", "/submit/edit-launch-date/[toolId]/page": "app/submit/edit-launch-date/[toolId]/page.js", "/submit/edit/[toolId]/page": "app/submit/edit/[toolId]/page.js", "/submit/launch-date/[toolId]/page": "app/submit/launch-date/[toolId]/page.js", "/submit/page": "app/submit/page.js", "/submit/success/page": "app/submit/success/page.js"}