"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{2731:(e,s,t)=>{t.d(s,{A:()=>r});var l=t(5155);function r(e){let{size:s="md",className:t=""}=e;return(0,l.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,l.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4478:(e,s,t)=>{t.d(s,{A:()=>c});var l=t(5155);t(2115);var r=t(6874),a=t.n(r);let c=e=>{let{children:s}=e;return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)("main",{className:"flex-1",children:s}),(0,l.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,l.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,l.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,l.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},6063:(e,s,t)=>{t.d(s,{A:()=>i});var l=t(5155),r=t(2115),a=t(2108),c=t(9911);function i(e){let{isOpen:s,onClose:t}=e,[i,n]=(0,r.useState)("method"),[d,o]=(0,r.useState)(""),[x,m]=(0,r.useState)(""),[h,u]=(0,r.useState)(!1),[b,g]=(0,r.useState)(""),y=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},j=()=>{n("method"),o(""),m(""),g(""),t()},p=async e=>{try{u(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){y("登录失败，请稍后重试","error")}finally{u(!1)}},f=async()=>{if(!d)return void g("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return void g("请输入有效的邮箱地址");g(""),u(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d})}),s=await e.json();s.success?(m(s.token),n("code"),y("验证码已发送，请查看您的邮箱")):y(s.error||"发送失败，请稍后重试","error")}catch(e){y("网络错误，请检查网络连接","error")}finally{u(!1)}},v=async e=>{if(6===e.length){u(!0);try{let s=await (0,a.signIn)("email-code",{email:d,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(y("登录成功，欢迎回来！"),j()):y((null==s?void 0:s.error)||"验证码错误","error")}catch(e){y("网络错误，请检查网络连接","error")}finally{u(!1)}}},N=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var l;null==(l=t[e+1])||l.focus()}let r=Array.from(t).map(e=>e.value).join("");6===r.length&&v(r)};return s?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:j}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,l.jsx)("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(c.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===i&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>p("google"),disabled:h,children:[(0,l.jsx)(c.DSS,{}),"使用 Google 登录"]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>p("github"),disabled:h,children:[(0,l.jsx)(c.hL4,{}),"使用 GitHub 登录"]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>n("email"),children:[(0,l.jsx)(c.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,l.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&f(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),b&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:f,disabled:h,children:h?"发送中...":"发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]}),"code"===i&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",d," 的6位验证码"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:s=>N(e,s.target.value),disabled:h,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("email"),children:"重新发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]})]})]})]}):null}}}]);