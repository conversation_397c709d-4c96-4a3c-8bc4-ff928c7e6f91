"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[611],{646:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2657:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2731:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(5155);function a(e){let{size:t="md",className:r=""}=e;return(0,s.jsx)("div",{className:"flex justify-center items-center ".concat(r),children:(0,s.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3717:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4186:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4478:(e,t,r)=>{r.d(t,{A:()=>c});var s=r(5155);r(2115);var a=r(6874),l=r.n(a);let c=e=>{let{children:t}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("main",{className:"flex-1",children:t}),(0,s.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,s.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4616:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4861:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5731:(e,t,r)=>{r.d(t,{u:()=>l});let s=r(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r="".concat(this.baseURL).concat(e),s={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(r,s),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/tools".concat(r?"?".concat(r):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/admin/tools".concat(r?"?".concat(r):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=s){this.baseURL=e}}let l=new a},9074:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9783:(e,t,r)=>{r.d(t,{A:()=>c});var s=r(5155),a=r(5339),l=r(4416);function c(e){let{message:t,onClose:r,className:c=""}=e;return(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,s.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,r)=>{r.d(t,{A:()=>h});var s=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),c=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:d="",children:h,iconNode:m,...x}=e;return(0,s.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:r,strokeWidth:c?24*Number(l)/Number(a):l,className:n("lucide",d),...!h&&!i(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{className:i,...o}=r;return(0,s.createElement)(d,{ref:l,iconNode:t,className:n("lucide-".concat(a(c(e))),"lucide-".concat(e),i),...o})});return r.displayName=c(e),r}}}]);