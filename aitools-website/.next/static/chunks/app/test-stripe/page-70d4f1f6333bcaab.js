(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[311],{1309:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(5155),n=s(2115),r=s(7368),i=s(5855);let l=(0,r.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function c(){let e=(0,i.useStripe)(),t=(0,i.useElements)(),[s,r]=(0,n.useState)(!1),[l,c]=(0,n.useState)(""),d=async s=>{if(s.preventDefault(),!e||!t)return;r(!0);let{error:a}=await e.confirmPayment({elements:t,confirmParams:{return_url:"".concat(window.location.origin,"/test-stripe")},redirect:"if_required"});a?c(a.message||"支付失败"):c("支付成功！"),r(!1)};return(0,a.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,a.jsx)(i.PaymentElement,{}),(0,a.jsx)("button",{disabled:!e||!t||s,className:"w-full bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50",children:s?"处理中...":"支付 \xa599.00"}),l&&(0,a.jsx)("div",{className:"text-center text-sm",children:l})]})}function d(){let[e,t]=(0,n.useState)(""),[s,r]=(0,n.useState)(!1),d=async()=>{r(!0);try{let e=await fetch("/api/test/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:9900,currency:"cny"})}),s=await e.json();s.success?t(s.clientSecret):alert("创建支付失败: "+s.message)}catch(e){alert("创建支付失败")}finally{r(!1)}};return(0,a.jsxs)("div",{className:"max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Stripe 支付测试"}),e?(0,a.jsx)(i.Elements,{stripe:l,options:{clientSecret:e,appearance:{theme:"stripe"}},children:(0,a.jsx)(c,{})}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:d,disabled:s,className:"bg-green-600 text-white py-2 px-4 rounded disabled:opacity-50",children:s?"创建中...":"创建测试支付"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"点击创建一个 \xa599.00 的测试支付"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-100 rounded text-sm",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"测试卡号:"}),(0,a.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,a.jsx)("li",{children:"成功: 4242 4242 4242 4242"}),(0,a.jsx)("li",{children:"失败: 4000 0000 0000 0002"}),(0,a.jsx)("li",{children:"需要验证: 4000 0025 0000 3155"})]}),(0,a.jsx)("p",{className:"mt-2 text-xs",children:"使用任意未来日期作为过期时间，任意3位数作为CVC"})]})]})}},1626:(e,t,s)=>{Promise.resolve().then(s.bind(s,1309))}},e=>{var t=t=>e(e.s=t);e.O(0,[1834,8441,1684,7358],()=>t(1626)),_N_E=e.O()}]);