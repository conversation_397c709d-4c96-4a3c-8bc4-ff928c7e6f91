(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{1976:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(5155);function a(e){let{size:s="md",className:t=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4309:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(5155),a=t(2115),l=t(2108),i=t(5695),c=t(6874),d=t.n(c),n=t(4478),o=t(2731),h=t(5731);let m=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var x=t(4616),u=t(2713),g=t(2657),p=t(1976),j=t(9074);function y(){var e,s,t,c,y,N;let{data:f,status:b}=(0,l.useSession)(),v=(0,i.useRouter)(),[w,A]=(0,a.useState)({submittedTools:0,approvedTools:0,totalViews:0,totalLikes:0,likedTools:0}),[k,T]=(0,a.useState)([]),[S,E]=(0,a.useState)(!0);(0,a.useEffect)(()=>{if("unauthenticated"===b)return void v.push("/");"authenticated"===b&&L()},[b,v]);let L=async()=>{try{E(!0);let e=await h.u.getAdminTools();if(e.success&&e.data){let s=e.data.tools,t=s.filter(e=>"approved"===e.status);A({submittedTools:s.length,approvedTools:t.length,totalViews:t.reduce((e,s)=>e+s.views,0),totalLikes:t.reduce((e,s)=>e+s.likes,0),likedTools:0}),T(s.slice(0,3))}}catch(e){console.error("Error fetching user data:",e)}finally{E(!1)}};return"loading"===b||S?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(o.A,{size:"lg",className:"py-20"})})}):f?(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=f.user)?void 0:e.image)?(0,r.jsx)("img",{src:f.user.image,alt:f.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-2xl font-medium text-gray-600",children:(null==(t=f.user)||null==(s=t.name)?void 0:s.charAt(0))||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:null==(c=f.user)?void 0:c.name}),(0,r.jsx)("p",{className:"text-gray-600",children:null==(y=f.user)?void 0:y.email}),(null==(N=f.user)?void 0:N.role)==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["加入时间：",new Date().toLocaleDateString("zh-CN")]})]})]}),(0,r.jsxs)(d(),{href:"/settings",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(m,{className:"mr-2 h-4 w-4"}),"编辑资料"]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"提交工具"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.submittedTools})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.approvedTools})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(g.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(p.A,{className:"h-8 w-8 text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"获得点赞"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.totalLikes})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(d(),{href:"/profile/submitted",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"我提交的工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"管理您提交的AI工具"})]})]})}),(0,r.jsx)(d(),{href:"/profile/liked",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-red-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"我的收藏"}),(0,r.jsx)("p",{className:"text-gray-600",children:"查看您收藏的工具"})]})]})}),(0,r.jsx)(d(),{href:"/submit",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"提交新工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"分享您发现的AI工具"})]})]})})]}),k.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"最近提交的工具"}),(0,r.jsx)(d(),{href:"/profile/submitted",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"查看全部"})]}),(0,r.jsx)("div",{className:"space-y-4",children:k.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"h-3 w-3 mr-1"}),new Date(e.submittedAt).toLocaleDateString("zh-CN")]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("approved"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"approved"===e.status?"已通过":"pending"===e.status?"审核中":"已拒绝"})]})]}),"approved"===e.status&&(0,r.jsx)(d(),{href:"/tools/".concat(e._id),className:"ml-4 text-blue-600 hover:text-blue-700",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})})]},e._id))})]})]})}):null}},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(5155);t(2115);var a=t(6874),l=t.n(a);let i=e=>{let{children:s}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("main",{className:"flex-1",children:s}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},5731:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let r=t(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&s.append(t,r.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&s.append(t,r.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=r){this.baseURL=e}}let l=new a},6991:(e,s,t)=>{Promise.resolve().then(t.bind(t,4309))},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var r=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},c=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:h,iconNode:m,...x}=e;return(0,r.createElement)("svg",{ref:s,...n,width:a,height:a,stroke:t,strokeWidth:i?24*Number(l)/Number(a):l,className:c("lucide",o),...!h&&!d(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(h)?h:[h]])}),h=(e,s)=>{let t=(0,r.forwardRef)((t,l)=>{let{className:d,...n}=t;return(0,r.createElement)(o,{ref:l,iconNode:s,className:c("lucide-".concat(a(i(e))),"lucide-".concat(e),d),...n})});return t.displayName=i(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,108,441,684,358],()=>s(6991)),_N_E=e.O()}]);