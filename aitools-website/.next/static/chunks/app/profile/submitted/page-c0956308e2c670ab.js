(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[210],{4508:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(5155),r=t(2115),l=t(2108),n=t(5695),i=t(6874),c=t.n(i),d=t(4478),x=t(2731),m=t(9783),o=t(5731),h=t(646),g=t(4186),u=t(4861),p=t(7550),j=t(4616),N=t(2713),b=t(2657),f=t(9074),v=t(3786),y=t(3717);let w=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},A=e=>{switch(e){case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";default:return e}},k=e=>{switch(e){case"approved":return(0,a.jsx)(h.A,{className:"h-4 w-4"});case"pending":return(0,a.jsx)(g.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(u.A,{className:"h-4 w-4"});default:return null}};function C(){let{data:e,status:s}=(0,l.useSession)(),t=(0,n.useRouter)(),[i,g]=(0,r.useState)("all"),[u,C]=(0,r.useState)([]),[_,S]=(0,r.useState)(!0),[z,D]=(0,r.useState)("");(0,r.useEffect)(()=>{if("unauthenticated"===s)return void t.push("/");"authenticated"===s&&E()},[s,t]);let E=async()=>{try{S(!0),D("");let e=await o.u.getAdminTools();e.success&&e.data?C(e.data.tools):D(e.error||"获取工具列表失败")}catch(e){D("网络错误，请重试")}finally{S(!1)}},L=u.filter(e=>"all"===i||e.status===i),P={total:u.length,approved:u.filter(e=>"approved"===e.status).length,pending:u.filter(e=>"pending"===e.status).length,rejected:u.filter(e=>"rejected"===e.status).length,totalViews:u.reduce((e,s)=>e+s.views,0),totalLikes:u.reduce((e,s)=>e+s.likes,0)};return"loading"===s||_?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(x.A,{size:"lg",className:"py-20"})})}):e?(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(c(),{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(p.A,{className:"h-5 w-5"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"我提交的AI工具"})]}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的所有AI工具"})]}),(0,a.jsxs)(c(),{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(N.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.total})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.approved})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(b.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.totalViews})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.totalLikes})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("button",{onClick:()=>g("all"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("all"===i?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["全部 (",P.total,")"]}),(0,a.jsxs)("button",{onClick:()=>g("approved"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("approved"===i?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已通过 (",P.approved,")"]}),(0,a.jsxs)("button",{onClick:()=>g("pending"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("pending"===i?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["审核中 (",P.pending,")"]}),(0,a.jsxs)("button",{onClick:()=>g("rejected"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("rejected"===i?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已拒绝 (",P.rejected,")"]})]})}),z&&(0,a.jsx)(m.A,{message:z,onClose:()=>D(""),className:"mb-6"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:L.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:L.map(e=>(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(w(e.status)),children:[k(e.status),(0,a.jsx)("span",{className:"ml-1",children:A(e.status)})]})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.publishedAt&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["发布于 ",new Date(e.publishedAt).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:"❤️"}),(0,a.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-800",children:[(0,a.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,a.jsx)(c(),{href:"/tools/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,a.jsx)(b.A,{className:"h-5 w-5"})}),(0,a.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,a.jsx)(v.A,{className:"h-5 w-5"})}),("rejected"===e.status||"pending"===e.status)&&(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"编辑",children:(0,a.jsx)(y.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(N.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===i?"还没有提交任何工具":"没有".concat(A(i),"的工具")}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===i?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===i&&(0,a.jsxs)(c(),{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})}):null}},5183:(e,s,t)=>{Promise.resolve().then(t.bind(t,4508))},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[874,108,611,441,684,358],()=>s(5183)),_N_E=e.O()}]);