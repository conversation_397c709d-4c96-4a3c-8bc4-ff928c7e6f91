(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5093],{5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},7550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8651:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(5155),r=a(2115),l=a(6874),n=a.n(l),i=a(5695),c=a(4478),o=a(9824),d=a(2731),x=a(9783),m=a(5731),g=a(7550),u=a(6932),h=a(6474),p=a(4653),b=a(5968);let j=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],f=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function v(){let e=(0,i.useParams)(),[s,a]=(0,r.useState)(null),[l,v]=(0,r.useState)([]),[N,y]=(0,r.useState)(!0),[w,A]=(0,r.useState)(""),[C,k]=(0,r.useState)(""),[D,S]=(0,r.useState)(""),[B,P]=(0,r.useState)("popular"),[_,I]=(0,r.useState)("grid"),[L,E]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e.slug&&F(e.slug)},[e.slug]);let F=async e=>{try{y(!0),A("");let s=await m.u.getTools({category:e,status:"approved",limit:100});if(s.success&&s.data){v(s.data.tools);let t=z(e,s.data.tools.length);a(t)}else A("获取分类数据失败")}catch(e){console.error("获取分类数据失败:",e),A("获取分类数据失败")}finally{y(!1)}},z=(e,s)=>{let a={"text-generation":{name:"文本生成",description:"AI tools for generating and editing text content, including chatbots, writing assistants, and content creation tools.",icon:"\uD83D\uDCDD",color:"#3B82F6"},"image-generation":{name:"图像生成",description:"AI tools for creating, editing, and enhancing images and visual content.",icon:"\uD83C\uDFA8",color:"#10B981"},"code-generation":{name:"代码生成",description:"AI tools for writing, debugging, and optimizing code across various programming languages.",icon:"\uD83D\uDCBB",color:"#8B5CF6"},"data-analysis":{name:"数据分析",description:"AI tools for analyzing, visualizing, and extracting insights from data.",icon:"\uD83D\uDCCA",color:"#F59E0B"},"audio-processing":{name:"音频处理",description:"AI tools for generating, editing, and enhancing audio content.",icon:"\uD83C\uDFB5",color:"#EF4444"},"video-editing":{name:"视频编辑",description:"AI tools for creating, editing, and enhancing video content.",icon:"\uD83C\uDFAC",color:"#06B6D4"}}[e]||{name:e.replace("-"," ").replace(/\b\w/g,e=>e.toUpperCase()),description:"AI tools in the ".concat(e," category."),icon:"\uD83E\uDD16",color:"#6B7280"};return{_id:e,slug:e,toolCount:s,...a}},R=[...l.filter(e=>{let s=e.name.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(C.toLowerCase())),a=!D||e.pricing===D;return s&&a})].sort((e,s)=>{switch(B){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":if(e.createdAt&&s.createdAt)return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}});return N?(0,t.jsx)(c.A,{children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsx)(d.A,{size:"lg"})})}):w?(0,t.jsx)(c.A,{children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsx)(x.A,{message:w})})}):s?(0,t.jsx)(c.A,{children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,t.jsx)(n(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)(n(),{href:"/categories",className:"hover:text-blue-600",children:"分类"}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{className:"text-gray-900",children:s.name})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)(n(),{href:"/categories",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"返回分类列表"]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-3xl",style:{backgroundColor:s.color},children:(0,t.jsx)("span",{className:"text-white",children:s.icon})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.name}),(0,t.jsxs)("p",{className:"text-lg text-gray-600",children:[s.toolCount," 个工具"]})]})]}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:s.description})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsxs)("div",{className:"relative mb-4",children:[(0,t.jsx)("input",{type:"text",placeholder:"在此分类中搜索工具...",value:C,onChange:e=>k(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,t.jsx)(u.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,t.jsx)("div",{className:"md:hidden mb-4",children:(0,t.jsxs)("button",{onClick:()=>E(!L),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,t.jsx)(h.A,{className:"ml-2 h-4 w-4 transform ".concat(L?"rotate-180":"")})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 ".concat(L?"block":"hidden md:grid"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,t.jsx)("select",{value:D,onChange:e=>S(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:j.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,t.jsx)("select",{value:B,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:f.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,t.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,t.jsx)("button",{onClick:()=>I("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===_?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,t.jsx)(p.A,{className:"h-4 w-4 mx-auto"})}),(0,t.jsx)("button",{onClick:()=>I("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===_?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,t.jsx)(b.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["显示 ",R.length," 个结果",C&&' 搜索 "'.concat(C,'"')]})}),R.length>0?(0,t.jsx)("div",{className:"grid"===_?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:R.map(e=>(0,t.jsx)(o.A,{tool:e},e._id))}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:(0,t.jsx)(u.A,{className:"h-12 w-12 mx-auto"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,t.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]}),(0,t.jsxs)("section",{className:"mt-16 bg-gray-50 rounded-lg p-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"相关分类"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(n(),{href:"/categories/image-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA8"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"图像生成"})]}),(0,t.jsxs)(n(),{href:"/categories/code-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"代码生成"})]}),(0,t.jsxs)(n(),{href:"/categories/data-analysis",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"数据分析"})]}),(0,t.jsxs)(n(),{href:"/categories/audio-processing",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"音频处理"})]})]})]})]})}):(0,t.jsx)(c.A,{children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"分类未找到"}),(0,t.jsx)("p",{className:"text-gray-600",children:"请检查URL或返回分类列表"}),(0,t.jsx)(n(),{href:"/categories",className:"text-blue-600 hover:text-blue-700 mt-4 inline-block",children:"返回分类列表"})]})})})}},8864:(e,s,a)=>{Promise.resolve().then(a.bind(a,8651))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,1944,4697,8441,1684,7358],()=>s(8864)),_N_E=e.O()}]);