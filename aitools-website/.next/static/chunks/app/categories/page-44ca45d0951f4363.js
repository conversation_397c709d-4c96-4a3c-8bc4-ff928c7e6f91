(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2379],{2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(5155);function l(e){let{size:t="md",className:s=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3109:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4354:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(5155);s(2115);var l=s(6874),c=s.n(l);let i=e=>{let{category:t}=e;return(0,r.jsx)(c(),{href:"/categories/".concat(t.slug),children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:t.color||"#3B82F6"},children:(0,r.jsx)("span",{className:"text-white",children:t.icon||"\uD83D\uDD27"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[t.toolCount," 个工具"]})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:t.description})]})})})}},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4478:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(5155);s(2115);var l=s(6874),c=s.n(l);let i=e=>{let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("main",{className:"flex-1",children:t}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4653:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5216:(e,t,s)=>{Promise.resolve().then(s.bind(s,9137))},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5731:(e,t,s)=>{"use strict";s.d(t,{u:()=>c});let r=s(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class l{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t},l=await fetch(s,r),c=await l.json();if(!l.ok)throw Error(c.error||"HTTP error! status: ".concat(l.status));return c}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=r){this.baseURL=e}}let c=new l},9137:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(5155),l=s(2115),c=s(4478),i=s(4354),a=s(2731),n=s(9783),o=s(5731),d=s(4653),x=s(3109);function h(){let[e,t]=(0,l.useState)([]),[s,h]=(0,l.useState)(!0),[m,u]=(0,l.useState)("");(0,l.useEffect)(()=>{g()},[]);let g=async()=>{try{h(!0),u("");let e=await o.u.getCategories();if(e.success&&e.data){let s=e.data.categories.map(e=>{let t={"text-generation":{description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},"image-generation":{description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},"code-generation":{description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},"data-analysis":{description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},"audio-processing":{description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},"video-editing":{description:"视频编辑、特效制作、自动剪辑等视频处理工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},translation:{description:"多语言翻译和本地化工具，打破语言障碍",icon:"\uD83C\uDF10",color:"#84CC16"},"search-engines":{description:"智能搜索引擎和信息检索工具",icon:"\uD83D\uDD0D",color:"#6366F1"},education:{description:"教育学习辅助工具，个性化学习体验",icon:"\uD83D\uDCDA",color:"#EC4899"},marketing:{description:"营销自动化、内容营销、广告优化等营销工具",icon:"\uD83D\uDCC8",color:"#F97316"},productivity:{description:"提高工作效率的各类生产力工具",icon:"⚡",color:"#22D3EE"},"customer-service":{description:"客户服务自动化、聊天机器人等客服工具",icon:"\uD83E\uDD1D",color:"#A855F7"}}[e.id]||{description:"优质AI工具集合",icon:"\uD83D\uDD27",color:"#6B7280"};return{_id:e.id,name:e.name,slug:e.id,description:t.description,icon:t.icon,color:t.color,toolCount:e.count}});t(s)}}catch(e){u("获取分类列表失败，请稍后重试"),console.error("Error fetching categories:",e)}finally{h(!1)}},p=e.sort((e,t)=>t.toolCount-e.toolCount).slice(0,6);return s?(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,r.jsx)(a.A,{size:"lg"})})})}):m?(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(n.A,{message:m})})}):(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)(d.A,{className:"inline-block mr-3 h-10 w-10 text-blue-600"}),"AI 工具分类"]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"按功能分类浏览我们精选的 AI 工具集合。每个分类都包含经过验证的高质量工具，帮助您快速找到所需的解决方案。"})]}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length}),(0,r.jsx)("div",{className:"text-gray-700",children:"个分类"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.reduce((e,t)=>e+t.toolCount,0)}),(0,r.jsx)("div",{className:"text-gray-700",children:"个工具"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length>0?Math.round(e.reduce((e,t)=>e+t.toolCount,0)/e.length):0}),(0,r.jsx)("div",{className:"text-gray-700",children:"平均每分类工具数"})]})]})}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"热门分类"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(e=>(0,r.jsx)(i.A,{category:e},e._id))})]}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"所有分类"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[e.length," 个分类"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,r.jsx)(i.A,{category:e},e._id))})]}),(0,r.jsxs)("section",{className:"mt-16 bg-blue-600 rounded-lg p-8 text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"没有找到您需要的分类？"}),(0,r.jsx)("p",{className:"text-blue-100 mb-6",children:"我们持续添加新的工具和分类。如果您有建议或想要提交新工具，请联系我们。"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("a",{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:"提交新工具"}),(0,r.jsx)("a",{href:"/contact",className:"inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors",children:"联系我们"})]})]})]})})}},9783:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(5155),l=s(5339),c=s(4416);function i(e){let{message:t,onClose:s,className:i=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(i),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:t})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=c(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:i,className:d="",children:x,iconNode:h,...m}=e;return(0,r.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:s,strokeWidth:i?24*Number(c)/Number(l):c,className:a("lucide",d),...!x&&!n(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,r.forwardRef)((s,c)=>{let{className:n,...o}=s;return(0,r.createElement)(d,{ref:c,iconNode:t,className:a("lucide-".concat(l(i(e))),"lucide-".concat(e),n),...o})});return s.displayName=i(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(5216)),_N_E=e.O()}]);