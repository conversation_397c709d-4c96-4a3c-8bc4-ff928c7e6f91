(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2868],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1243:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1544:(e,s,t)=>{Promise.resolve().then(t.bind(t,5093))},3332:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3786:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(5155);t(2115);var r=t(6874),l=t.n(r);let i=e=>{let{children:s}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("main",{className:"flex-1",children:s}),(0,a.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,a.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5093:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(5155),r=t(2115),l=t(5695),i=t(4478),c=t(4186),d=t(646),n=t(4861),m=t(7550),x=t(5868),o=t(9946);let h=(0,o.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var u=t(3786),g=t(8564),p=t(3332),y=t(1007);let b=(0,o.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var j=t(9074),N=t(1243);let f={id:"1",name:"AI写作助手",description:"基于GPT技术的智能写作工具，支持多种文体创作，提供实时语法检查和内容优化建议。这是一个功能强大的AI写作助手，能够帮助用户快速生成高质量的文章、报告、邮件等各种文本内容。工具采用最新的自然语言处理技术，具有强大的语言理解和生成能力。",website:"https://aiwriter.example.com",logo:"https://via.placeholder.com/128",category:"text-generation",pricing:"freemium",tags:["写作","GPT","内容创作","语法检查","文本生成"],features:["智能写作：基于AI的内容生成","语法检查：实时检测和修正语法错误","多语言支持：支持中文、英文等多种语言","模板库：提供丰富的写作模板","协作功能：支持团队协作编辑","导出功能：支持多种格式导出"],submitterName:"张三",submitterEmail:"<EMAIL>",submittedAt:"2024-06-25T10:30:00Z",status:"pending",publishDate:"2024-06-28",screenshots:["https://via.placeholder.com/600x400","https://via.placeholder.com/600x400","https://via.placeholder.com/600x400"]},v={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},w={free:"免费",freemium:"免费增值",paid:"付费"};function k(){(0,l.useParams)();let e=(0,l.useRouter)(),[s]=(0,r.useState)(f),[t,o]=(0,r.useState)(!1),[k,A]=(0,r.useState)(""),[C,M]=(0,r.useState)(!1),z=async()=>{M(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("工具已批准！"),e.push("/admin")}catch(e){alert("操作失败，请重试")}finally{M(!1)}},P=async()=>{if(k.trim()){M(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("工具已拒绝！"),e.push("/admin")}catch(e){alert("操作失败，请重试")}finally{M(!1),o(!1),A("")}}};return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"返回审核列表"]}),(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,a.jsx)("img",{src:s.logo,alt:s.name,className:"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(e=>{switch(e){case"pending":return(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"待审核"]});case"approved":return(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"已批准"]});case"rejected":return(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"已拒绝"]});default:return null}})(s.status)]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-4",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:v[s.category]}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),w[s.pricing]]}),(0,a.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[(0,a.jsx)(h,{className:"w-4 h-4 mr-1"}),"访问网站",(0,a.jsx)(u.A,{className:"w-3 h-3 ml-1"})]})]}),(0,a.jsx)("p",{className:"text-gray-600 max-w-3xl",children:s.description})]})]}),"pending"===s.status&&(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{onClick:z,disabled:C,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2"}),C?"处理中...":"批准"]}),(0,a.jsxs)("button",{onClick:()=>o(!0),disabled:C,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"拒绝"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"主要功能"}),(0,a.jsx)("ul",{className:"space-y-3",children:s.features.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)(g.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 mr-1"}),e]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"截图预览"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.screenshots.map((e,t)=>(0,a.jsx)("img",{src:e,alt:"".concat(s.name," 截图 ").concat(t+1),className:"w-full h-48 object-cover rounded-lg border border-gray-200"},t))})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"提交信息"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submitterName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"提交者"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submitterEmail}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"联系邮箱"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.submittedAt).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"提交时间"})]})]}),s.publishDate&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.publishDate).toLocaleDateString("zh-CN")}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"期望发布日期"})]})]})]})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"审核指南"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsx)("li",{children:"• 验证工具网站是否可正常访问"}),(0,a.jsx)("li",{children:"• 检查工具描述是否准确客观"}),(0,a.jsx)("li",{children:"• 确认分类和标签是否合适"}),(0,a.jsx)("li",{children:"• 评估工具质量和实用性"}),(0,a.jsx)("li",{children:"• 检查是否存在重复提交"})]})]})]})})]})]}),t&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请详细说明拒绝的原因，这将帮助提交者了解问题并改进他们的提交。"}),(0,a.jsx)("textarea",{value:k,onChange:e=>A(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入详细的拒绝原因..."}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>{o(!1),A("")},disabled:C,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:P,disabled:!k.trim()||C,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:C?"处理中...":"确认拒绝"})]})]})})]})})}},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8564:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var a=t(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},c=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:m="",children:x,iconNode:o,...h}=e;return(0,a.createElement)("svg",{ref:s,...n,width:r,height:r,stroke:t,strokeWidth:i?24*Number(l)/Number(r):l,className:c("lucide",m),...!x&&!d(h)&&{"aria-hidden":"true"},...h},[...o.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(x)?x:[x]])}),x=(e,s)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:d,...n}=t;return(0,a.createElement)(m,{ref:l,iconNode:s,className:c("lucide-".concat(r(i(e))),"lucide-".concat(e),d),...n})});return t.displayName=i(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(1544)),_N_E=e.O()}]);