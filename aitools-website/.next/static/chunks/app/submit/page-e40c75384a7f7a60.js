(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1731],{2676:(e,s,l)=>{Promise.resolve().then(l.bind(l,4604))},4604:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>y});var t=l(5155),a=l(2115),r=l(2108),i=l(5695),n=l(4478),c=l(2731),d=l(9783),o=l(5734),m=l(6063),x=l(9869),u=l(8164),g=l(1284);let b=["AI助手","ChatGPT","对话AI","智能问答","语言模型","写作助手","内容生成","文案创作","博客写作","营销文案","图像生成","图像编辑","AI绘画","头像生成","背景移除","视频生成","视频编辑","视频剪辑","短视频制作","视频字幕","语音合成","语音识别","音乐生成","语音转文字","文字转语音","代码生成","代码补全","代码审查","开发助手","低代码平台","数据分析","数据可视化","商业智能","机器学习","深度学习","办公自动化","文档处理","项目管理","团队协作","笔记工具","UI设计","Logo设计","网页设计","平面设计","原型设计","SEO优化","社交媒体营销","邮件营销","内容营销","市场分析","机器翻译","实时翻译","文档翻译","语音翻译"];var h=l(4416),p=l(7924),j=l(3332);function v(e){let{selectedTags:s,onTagsChange:l,maxTags:r=3}=e,[i,n]=(0,a.useState)(""),[c,d]=(0,a.useState)(!1),o=e=>{s.includes(e)?l(s.filter(s=>s!==e)):s.length<r&&l([...s,e])},m=e=>{l(s.filter(s=>s!==e))},x=b.filter(e=>e.toLowerCase().includes(i.toLowerCase())&&!s.includes(e));return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"选择标签"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["已选择 ",s.length,"/",r," 个标签"]})]}),s.length>0&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"已选择的标签："}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:s.map(e=>(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>m(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,t.jsx)(h.A,{className:"h-3 w-3"})})]},e))})]}),(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["选择标签（最多",r,"个）"]}),(0,t.jsxs)("div",{className:"relative mb-3",children:[(0,t.jsx)("input",{type:"text",placeholder:"搜索标签...",value:i,onChange:e=>n(e.target.value),onFocus:()=>d(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,t.jsx)(p.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(c||i)&&(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:x.length>0?(0,t.jsxs)("div",{className:"p-2",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 gap-1",children:x.map(e=>{let l=s.length>=r;return(0,t.jsx)("button",{type:"button",onClick:()=>{o(e),n(""),d(!1)},disabled:l,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(l?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e]})},e)})}),x.length>50&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:["找到 ",x.length," 个匹配标签"]})]}):(0,t.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:i?"未找到匹配的标签":"开始输入以搜索标签"})})})]})}),(c||i)&&(0,t.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{d(!1),n("")}}),s.length>=r&&(0,t.jsxs)("p",{className:"text-sm text-amber-600",children:["最多只能选择",r,"个标签"]})]})}let f=[{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"},{value:"translation",label:"语言翻译"},{value:"search-engines",label:"搜索引擎"},{value:"education",label:"教育学习"},{value:"marketing",label:"营销工具"},{value:"productivity",label:"生产力工具"},{value:"customer-service",label:"客户服务"}],N=[{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}];function y(){var e,s,l;let{data:b,status:h}=(0,r.useSession)(),p=(0,i.useRouter)(),[j,y]=(0,a.useState)({name:"",tagline:"",description:"",longDescription:"",website:"",logo:"",category:"",tags:[],pricing:"",pricingDetails:"",screenshots:[]}),[w,k]=(0,a.useState)(""),[A,C]=(0,a.useState)(!1),[S,I]=(0,a.useState)("idle"),[D,O]=(0,a.useState)({}),[P,T]=(0,a.useState)(""),[E,L]=(0,a.useState)(!1),[_,G]=(0,a.useState)(null),[F,z]=(0,a.useState)(""),R=()=>{let e={};return j.name.trim()||(e.name="工具名称是必填项"),j.description.trim()||(e.description="工具描述是必填项"),j.website.trim()||(e.website="官方网站是必填项"),j.category||(e.category="请选择一个分类"),j.pricing||(e.pricing="请选择价格模式"),j.website&&!j.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),O(e),0===Object.keys(e).length},J=async e=>{if(e.preventDefault(),!b)return void L(!0);if(R()){C(!0),I("idle");try{let e=j.logo;if(_){let s=new FormData;s.append("logo",_);let l=await fetch("/api/upload/logo",{method:"POST",body:s}),t=await l.json();if(t.success)e=t.data.url;else throw Error(t.message||"Logo上传失败")}let s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:j.name,tagline:j.tagline,description:j.description,longDescription:j.longDescription,website:j.website,logo:e||void 0,category:j.category,tags:j.tags,pricing:j.pricing,pricingDetails:j.pricingDetails,screenshots:j.screenshots})}),l=await s.json();l.success?p.push("/submit/launch-date/".concat(l.data.toolId)):(I("error"),T(l.message||"提交失败，请重试"))}catch(e){console.error("Error submitting tool:",e),I("error"),T("网络错误，请检查连接后重试")}finally{C(!1)}}};return(0,t.jsxs)(n.A,{children:[(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,t.jsx)(x.A,{className:"inline-block mr-3 h-8 w-8 text-blue-600"}),"提交 AI 工具"]}),(0,t.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。"})]}),"success"===S&&(0,t.jsx)(o.A,{message:P||"工具提交成功！我们会在 1-3 个工作日内审核您的提交。",onClose:()=>I("idle"),className:"mb-6"}),"error"===S&&(0,t.jsx)(d.A,{message:P||"提交失败，请检查网络连接后重试。",onClose:()=>I("idle"),className:"mb-6"}),(0,t.jsxs)("form",{onSubmit:J,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,t.jsx)("input",{type:"text",value:j.name,onChange:e=>y(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(D.name?"border-red-300":"border-gray-300"),placeholder:"例如：ChatGPT"}),D.name&&(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语（可选）"}),(0,t.jsx)("input",{type:"text",value:j.tagline,onChange:e=>y(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"简短描述工具的核心价值"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"url",value:j.website,onChange:e=>y(s=>({...s,website:e.target.value})),className:"w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(D.website?"border-red-300":"border-gray-300"),placeholder:"https://example.com"}),(0,t.jsx)(u.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),D.website&&(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.website})]})}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,t.jsx)("textarea",{value:j.description,onChange:e=>y(s=>({...s,description:e.target.value})),rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(D.description?"border-red-300":"border-gray-300"),placeholder:"详细描述这个 AI 工具的功能和特点..."}),D.description&&(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.description})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo图片（可选）"}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let l=null==(s=e.target.files)?void 0:s[0];if(l){G(l);let e=new FileReader;e.onload=e=>{var s;z(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(l)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),F&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("img",{src:F,alt:"Logo预览",className:"w-16 h-16 object-cover rounded-lg border border-gray-300"})})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB"})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,t.jsxs)("select",{value:j.category,onChange:e=>y(s=>({...s,category:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(D.category?"border-red-300":"border-gray-300"),children:[(0,t.jsx)("option",{value:"",children:"请选择分类"}),f.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))]}),D.category&&(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.category})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,t.jsxs)("select",{value:j.pricing,onChange:e=>y(s=>({...s,pricing:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(D.pricing?"border-red-300":"border-gray-300"),children:[(0,t.jsx)("option",{value:"",children:"请选择价格模式"}),N.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))]}),D.pricing&&(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D.pricing})]})]})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(v,{selectedTags:j.tags,onTagsChange:e=>y(s=>({...s,tags:e})),maxTags:3})}),b&&(0,t.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-green-800 mb-2",children:"提交者信息"}),(0,t.jsxs)("p",{className:"text-sm text-green-700",children:["提交者：",(null==(e=b.user)?void 0:e.name)||(null==(s=b.user)?void 0:s.email)]}),(0,t.jsxs)("p",{className:"text-sm text-green-700",children:["邮箱：",null==(l=b.user)?void 0:l.email]})]}),(0,t.jsx)("div",{className:"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"提交指南"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• 请确保提交的是真实存在且可正常访问的 AI 工具"}),(0,t.jsx)("li",{children:"• 工具描述应该准确、客观，避免过度营销"}),(0,t.jsx)("li",{children:"• 我们会在 1-3 个工作日内审核您的提交"}),(0,t.jsx)("li",{children:"• 审核通过后，工具将出现在我们的目录中"}),(0,t.jsx)("li",{children:"• 如有问题，我们会通过邮箱联系您"})]})]})]})}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:A,className:"px-8 py-3 rounded-lg font-medium transition-colors ".concat(A?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:A?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{size:"sm",className:"mr-2"}),"提交中..."]}):"提交工具"})})]})]}),(0,t.jsx)(m.A,{isOpen:E,onClose:()=>L(!1)})]})}},5734:(e,s,l)=>{"use strict";l.d(s,{A:()=>i});var t=l(5155),a=l(646),r=l(4416);function i(e){let{message:s,onClose:l,className:i=""}=e;return(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("p",{className:"text-green-800 text-sm",children:s})}),l&&(0,t.jsx)("button",{onClick:l,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,t.jsx)(r.A,{className:"w-4 h-4"})})]})})}},9783:(e,s,l)=>{"use strict";l.d(s,{A:()=>i});var t=l(5155),a=l(5339),r=l(4416);function i(e){let{message:s,onClose:l,className:i=""}=e;return(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(i),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("p",{className:"text-red-800 text-sm",children:s})}),l&&(0,t.jsx)("button",{onClick:l,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,t.jsx)(r.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2556,316,8441,1684,7358],()=>s(2676)),_N_E=e.O()}]);