(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1731],{2676:(e,s,t)=>{Promise.resolve().then(t.bind(t,4604))},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var l=t(5155);function r(e){let{size:s="md",className:t=""}=e;return(0,l.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,l.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(5155);t(2115);var r=t(6874),a=t.n(r);let i=e=>{let{children:s}=e;return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)("main",{className:"flex-1",children:s}),(0,l.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,l.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,l.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,l.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4604:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var l=t(5155),r=t(2115),a=t(2108),i=t(5695),c=t(4478),n=t(2731),d=t(9783),o=t(5734),x=t(6063),m=t(9869),u=t(8164),h=t(1284);let g=["AI助手","ChatGPT","对话AI","智能问答","语言模型","写作助手","内容生成","文案创作","博客写作","营销文案","图像生成","图像编辑","AI绘画","头像生成","背景移除","视频生成","视频编辑","视频剪辑","短视频制作","视频字幕","语音合成","语音识别","音乐生成","语音转文字","文字转语音","代码生成","代码补全","代码审查","开发助手","低代码平台","数据分析","数据可视化","商业智能","机器学习","深度学习","办公自动化","文档处理","项目管理","团队协作","笔记工具","UI设计","Logo设计","网页设计","平面设计","原型设计","SEO优化","社交媒体营销","邮件营销","内容营销","市场分析","机器翻译","实时翻译","文档翻译","语音翻译"];var b=t(4416),p=t(7924),j=t(3332);function f(e){let{selectedTags:s,onTagsChange:t,maxTags:a=3}=e,[i,c]=(0,r.useState)(""),[n,d]=(0,r.useState)(!1),o=e=>{s.includes(e)?t(s.filter(s=>s!==e)):s.length<a&&t([...s,e])},x=e=>{t(s.filter(s=>s!==e))},m=g.filter(e=>e.toLowerCase().includes(i.toLowerCase())&&!s.includes(e));return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"选择标签"}),(0,l.jsxs)("span",{className:"text-sm text-gray-500",children:["已选择 ",s.length,"/",a," 个标签"]})]}),s.length>0&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"已选择的标签："}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.map(e=>(0,l.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[e,(0,l.jsx)("button",{type:"button",onClick:()=>x(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,l.jsx)(b.A,{className:"h-3 w-3"})})]},e))})]}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["选择标签（最多",a,"个）"]}),(0,l.jsxs)("div",{className:"relative mb-3",children:[(0,l.jsx)("input",{type:"text",placeholder:"搜索标签...",value:i,onChange:e=>c(e.target.value),onFocus:()=>d(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,l.jsx)(p.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(n||i)&&(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:m.length>0?(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 gap-1",children:m.map(e=>{let t=s.length>=a;return(0,l.jsx)("button",{type:"button",onClick:()=>{o(e),c(""),d(!1)},disabled:t,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(t?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(j.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e]})},e)})}),m.length>50&&(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:["找到 ",m.length," 个匹配标签"]})]}):(0,l.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:i?"未找到匹配的标签":"开始输入以搜索标签"})})})]})}),(n||i)&&(0,l.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{d(!1),c("")}}),s.length>=a&&(0,l.jsxs)("p",{className:"text-sm text-amber-600",children:["最多只能选择",a,"个标签"]})]})}let v=[{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"},{value:"translation",label:"语言翻译"},{value:"search-engines",label:"搜索引擎"},{value:"education",label:"教育学习"},{value:"marketing",label:"营销工具"},{value:"productivity",label:"生产力工具"},{value:"customer-service",label:"客户服务"}],y=[{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}];function N(){var e,s,t;let{data:g,status:b}=(0,a.useSession)(),p=(0,i.useRouter)(),[j,N]=(0,r.useState)({name:"",tagline:"",description:"",longDescription:"",website:"",logo:"",category:"",tags:[],pricing:"",pricingDetails:"",screenshots:[]}),[w,k]=(0,r.useState)(""),[C,A]=(0,r.useState)(!1),[S,I]=(0,r.useState)("idle"),[T,D]=(0,r.useState)({}),[O,P]=(0,r.useState)(""),[E,L]=(0,r.useState)(!1),[G,_]=(0,r.useState)(null),[z,F]=(0,r.useState)(""),J=()=>{let e={};return j.name.trim()||(e.name="工具名称是必填项"),j.description.trim()||(e.description="工具描述是必填项"),j.website.trim()||(e.website="官方网站是必填项"),j.category||(e.category="请选择一个分类"),j.pricing||(e.pricing="请选择价格模式"),j.website&&!j.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),D(e),0===Object.keys(e).length},R=async e=>{if(e.preventDefault(),!g)return void L(!0);if(J()){A(!0),I("idle");try{let e=j.logo;if(G){let s=new FormData;s.append("logo",G);let t=await fetch("/api/upload/logo",{method:"POST",body:s}),l=await t.json();if(l.success)e=l.data.url;else throw Error(l.message||"Logo上传失败")}let s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:j.name,tagline:j.tagline,description:j.description,longDescription:j.longDescription,website:j.website,logo:e||void 0,category:j.category,tags:j.tags,pricing:j.pricing,pricingDetails:j.pricingDetails,screenshots:j.screenshots})}),t=await s.json();t.success?p.push("/submit/launch-date/".concat(t.data.toolId)):(I("error"),P(t.message||"提交失败，请重试"))}catch(e){console.error("Error submitting tool:",e),I("error"),P("网络错误，请检查连接后重试")}finally{A(!1)}}};return(0,l.jsxs)(c.A,{children:[(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)(m.A,{className:"inline-block mr-3 h-8 w-8 text-blue-600"}),"提交 AI 工具"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。"})]}),"success"===S&&(0,l.jsx)(o.A,{message:O||"工具提交成功！我们会在 1-3 个工作日内审核您的提交。",onClose:()=>I("idle"),className:"mb-6"}),"error"===S&&(0,l.jsx)(d.A,{message:O||"提交失败，请检查网络连接后重试。",onClose:()=>I("idle"),className:"mb-6"}),(0,l.jsxs)("form",{onSubmit:R,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,l.jsx)("input",{type:"text",value:j.name,onChange:e=>N(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(T.name?"border-red-300":"border-gray-300"),placeholder:"例如：ChatGPT"}),T.name&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:T.name})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语（可选）"}),(0,l.jsx)("input",{type:"text",value:j.tagline,onChange:e=>N(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"简短描述工具的核心价值"})]})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{type:"url",value:j.website,onChange:e=>N(s=>({...s,website:e.target.value})),className:"w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(T.website?"border-red-300":"border-gray-300"),placeholder:"https://example.com"}),(0,l.jsx)(u.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),T.website&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:T.website})]})}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,l.jsx)("textarea",{value:j.description,onChange:e=>N(s=>({...s,description:e.target.value})),rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(T.description?"border-red-300":"border-gray-300"),placeholder:"详细描述这个 AI 工具的功能和特点..."}),T.description&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:T.description})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo图片（可选）"}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){_(t);let e=new FileReader;e.onload=e=>{var s;F(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(t)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),z&&(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("img",{src:z,alt:"Logo预览",className:"w-16 h-16 object-cover rounded-lg border border-gray-300"})})]}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB"})]})]}),(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,l.jsxs)("select",{value:j.category,onChange:e=>N(s=>({...s,category:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(T.category?"border-red-300":"border-gray-300"),children:[(0,l.jsx)("option",{value:"",children:"请选择分类"}),v.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}),T.category&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:T.category})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,l.jsxs)("select",{value:j.pricing,onChange:e=>N(s=>({...s,pricing:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(T.pricing?"border-red-300":"border-gray-300"),children:[(0,l.jsx)("option",{value:"",children:"请选择价格模式"}),y.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}),T.pricing&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:T.pricing})]})]})]}),(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsx)(f,{selectedTags:j.tags,onTagsChange:e=>N(s=>({...s,tags:e})),maxTags:3})}),g&&(0,l.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-green-800 mb-2",children:"提交者信息"}),(0,l.jsxs)("p",{className:"text-sm text-green-700",children:["提交者：",(null==(e=g.user)?void 0:e.name)||(null==(s=g.user)?void 0:s.email)]}),(0,l.jsxs)("p",{className:"text-sm text-green-700",children:["邮箱：",null==(t=g.user)?void 0:t.email]})]}),(0,l.jsx)("div",{className:"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"提交指南"}),(0,l.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,l.jsx)("li",{children:"• 请确保提交的是真实存在且可正常访问的 AI 工具"}),(0,l.jsx)("li",{children:"• 工具描述应该准确、客观，避免过度营销"}),(0,l.jsx)("li",{children:"• 我们会在 1-3 个工作日内审核您的提交"}),(0,l.jsx)("li",{children:"• 审核通过后，工具将出现在我们的目录中"}),(0,l.jsx)("li",{children:"• 如有问题，我们会通过邮箱联系您"})]})]})]})}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)("button",{type:"submit",disabled:C,className:"px-8 py-3 rounded-lg font-medium transition-colors ".concat(C?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:C?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(n.A,{size:"sm",className:"mr-2"}),"提交中..."]}):"提交工具"})})]})]}),(0,l.jsx)(x.A,{isOpen:E,onClose:()=>L(!1)})]})}},5734:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(5155),r=t(646),a=t(4416);function i(e){let{message:s,onClose:t,className:i=""}=e;return(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)(r.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsx)("p",{className:"text-green-800 text-sm",children:s})}),t&&(0,l.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,l.jsx)(a.A,{className:"w-4 h-4"})})]})})}},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var l=t(5155),r=t(2115),a=t(2108),i=t(9911);function c(e){let{isOpen:s,onClose:t}=e,[c,n]=(0,r.useState)("method"),[d,o]=(0,r.useState)(""),[x,m]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[g,b]=(0,r.useState)(""),p=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},j=()=>{n("method"),o(""),m(""),b(""),t()},f=async e=>{try{h(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{h(!1)}},v=async()=>{if(!d)return void b("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return void b("请输入有效的邮箱地址");b(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d})}),s=await e.json();s.success?(m(s.token),n("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}},y=async e=>{if(6===e.length){h(!0);try{let s=await (0,a.signIn)("email-code",{email:d,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(p("登录成功，欢迎回来！"),j()):p((null==s?void 0:s.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}}},N=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var l;null==(l=t[e+1])||l.focus()}let r=Array.from(t).map(e=>e.value).join("");6===r.length&&y(r)};return s?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:j}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===c&&"登录 AI Tools Directory","email"===c&&"邮箱登录","code"===c&&"输入验证码"]}),(0,l.jsx)("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(i.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===c&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>f("google"),disabled:u,children:[(0,l.jsx)(i.DSS,{}),"使用 Google 登录"]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>f("github"),disabled:u,children:[(0,l.jsx)(i.hL4,{}),"使用 GitHub 登录"]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>n("email"),children:[(0,l.jsx)(i.maD,{}),"使用邮箱登录"]})]}),"email"===c&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,l.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&v(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:v,disabled:u,children:u?"发送中...":"发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]}),"code"===c&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",d," 的6位验证码"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:s=>N(e,s.target.value),disabled:u,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("email"),children:"重新发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]})]})]})]}):null}},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(5155),r=t(5339),a=t(4416);function i(e){let{message:s,onClose:t,className:i=""}=e;return(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(i),children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)(r.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,l.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,l.jsx)(a.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2556,8441,1684,7358],()=>s(2676)),_N_E=e.O()}]);