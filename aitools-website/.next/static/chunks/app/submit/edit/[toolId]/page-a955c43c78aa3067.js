(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[120],{4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=s.createContext&&s.createContext(a),n=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var s,a,l;s=e,a=t,l=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):s[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:l,title:o}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)r=l[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,n),u=l||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==l?s.createElement(l.Consumer,null,e=>t(e)):t(a)}},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8431:(e,t,r)=>{Promise.resolve().then(r.bind(r,8658))},8658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(5155),a=r(2115),l=r(5695),n=r(2108),i=r(4478),o=r(2731),c=r(6063),d=r(7550),u=r(9869),m=r(4416),g=r(4616),x=r(6874),b=r.n(x);function p(e){let{params:t}=e,{data:r,status:x}=(0,n.useSession)(),p=(0,l.useRouter)(),[h,f]=(0,a.useState)(""),[y,v]=(0,a.useState)(null),[j,N]=(0,a.useState)(!0),[w,k]=(0,a.useState)(!1),[A,O]=(0,a.useState)(!1),[C,P]=(0,a.useState)({}),[S,D]=(0,a.useState)("idle"),[E,I]=(0,a.useState)(""),[L,z]=(0,a.useState)(null),[R,_]=(0,a.useState)(""),[M,T]=(0,a.useState)(!1),[G,U]=(0,a.useState)(""),[W,F]=(0,a.useState)(""),[H,J]=(0,a.useState)({name:"",tagline:"",description:"",longDescription:"",website:"",logo:"",category:"",tags:[],pricing:"",pricingDetails:"",screenshots:[]});(0,a.useEffect)(()=>{t.then(e=>f(e.toolId))},[t]),(0,a.useEffect)(()=>{if(!h)return;let e=async()=>{try{let e=await fetch("/api/tools/".concat(h)),t=await e.json();if(t.success){let e=t.data;v(e),J({name:e.name||"",tagline:e.tagline||"",description:e.description||"",longDescription:e.longDescription||"",website:e.website||"",logo:e.logo||"",category:e.category||"",tags:e.tags||[],pricing:e.pricing||"",pricingDetails:e.pricingDetails||"",screenshots:e.screenshots||[]}),_(e.logo||"")}else D("error"),I(t.message||"获取工具信息失败")}catch(e){console.error("获取工具信息失败:",e),D("error"),I("网络错误，请重试")}finally{N(!1)}};r?e():"loading"!==x&&N(!1)},[h,r,x]),(0,a.useEffect)(()=>{var e;y&&(null==r||null==(e=r.user)||e.email)},[y,r]);let K=()=>{let e={};return H.name.trim()||(e.name="工具名称是必填项"),H.description.trim()||(e.description="工具描述是必填项"),H.website.trim()||(e.website="官方网站是必填项"),H.category||(e.category="请选择一个分类"),H.pricing||(e.pricing="请选择价格模式"),H.website&&!H.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),P(e),0===Object.keys(e).length},Z=async e=>{if(e.preventDefault(),!r)return void O(!0);if(K()){k(!0),D("idle");try{let e=await fetch("/api/tools/".concat(h),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:H.name,tagline:H.tagline,description:H.description,longDescription:H.longDescription,website:H.website,logo:R||void 0,category:H.category,tags:H.tags,pricing:H.pricing,pricingDetails:H.pricingDetails,screenshots:H.screenshots})}),t=await e.json();t.success?(D("success"),I("工具信息更新成功！"),setTimeout(()=>{p.push("/profile/submitted")},2e3)):(D("error"),I(t.error||"更新失败，请重试"))}catch(e){console.error("更新工具失败:",e),D("error"),I("网络错误，请重试")}finally{k(!1)}}},$=async e=>{if(!e)return;T(!0);let t=new FormData;t.append("logo",e);try{let e=await fetch("/api/upload/logo",{method:"POST",body:t}),r=await e.json();r.success?(_(r.data.url),J(e=>({...e,logo:r.data.url}))):(D("error"),I(r.message||"上传失败"))}catch(e){console.error("上传失败:",e),D("error"),I("上传失败，请重试")}finally{T(!1)}},q=()=>{G.trim()&&!H.tags.includes(G.trim())&&H.tags.length<3&&(J(e=>({...e,tags:[...e.tags,G.trim()]})),U(""))},B=e=>{J(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},Q=()=>{W.trim()&&!H.screenshots.includes(W.trim())&&(J(e=>({...e,screenshots:[...e.screenshots,W.trim()]})),F(""))},V=e=>{J(t=>({...t,screenshots:t.screenshots.filter(t=>t!==e)}))};return"loading"===x||j?(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)(o.A,{size:"lg"})})}):r?y?(0,s.jsxs)(i.A,{children:[(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)(b(),{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"返回工具列表"]}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"编辑工具信息"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"更新您的工具信息，让更多用户了解您的产品"})]}),"success"===S&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-green-800",children:E})}),"error"===S&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-800",children:E})}),(0,s.jsxs)("form",{onSubmit:Z,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,s.jsx)("input",{type:"text",value:H.name,onChange:e=>J(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(C.name?"border-red-300":"border-gray-300"),placeholder:"例如：ChatGPT"}),C.name&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语"}),(0,s.jsx)("input",{type:"text",value:H.tagline,onChange:e=>J(t=>({...t,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：AI驱动的对话助手"})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,s.jsx)("textarea",{value:H.description,onChange:e=>J(t=>({...t,description:e.target.value})),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(C.description?"border-red-300":"border-gray-300"),placeholder:"简要描述您的工具功能和特点..."}),C.description&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.description})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"详细描述"}),(0,s.jsx)("textarea",{value:H.longDescription,onChange:e=>J(t=>({...t,longDescription:e.target.value})),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"详细介绍您的工具，包括主要功能、使用场景、技术特点等..."})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,s.jsx)("input",{type:"url",value:H.website,onChange:e=>J(t=>({...t,website:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(C.website?"border-red-300":"border-gray-300"),placeholder:"https://example.com"}),C.website&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.website})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"工具Logo"}),(0,s.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"上传Logo图片"}),(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];r&&(z(r),$(r))},className:"hidden",id:"logo-upload"}),(0,s.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:M?"上传中...":"点击上传或拖拽图片到此处"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"支持 PNG, JPG, GIF 格式，建议尺寸 200x200px"})]})]})]}),R&&(0,s.jsx)("div",{className:"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden",children:(0,s.jsx)("img",{src:R,alt:"Logo预览",className:"w-full h-full object-cover"})})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,s.jsxs)("select",{value:H.category,onChange:e=>J(t=>({...t,category:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(C.category?"border-red-300":"border-gray-300"),children:[(0,s.jsx)("option",{value:"",children:"请选择分类"}),[{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"},{value:"design-tools",label:"设计工具"},{value:"productivity",label:"生产力工具"},{value:"customer-service",label:"客户服务"}].map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}),C.category&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.category})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,s.jsxs)("select",{value:H.pricing,onChange:e=>J(t=>({...t,pricing:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(C.pricing?"border-red-300":"border-gray-300"),children:[(0,s.jsx)("option",{value:"",children:"请选择价格模式"}),[{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}].map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}),C.pricing&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:C.pricing})]})]}),H.pricing&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格详情"}),(0,s.jsx)("textarea",{value:H.pricingDetails,onChange:e=>J(t=>({...t,pricingDetails:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"详细说明价格信息，如免费版功能、付费版价格等..."})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"最多选择3个标签来描述您的工具"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:H.tags.map((e,t)=>(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>B(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,s.jsx)(m.A,{className:"h-3 w-3"})})]},t))}),H.tags.length<3&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("input",{type:"text",value:G,onChange:e=>U(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),q()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入自定义标签",maxLength:20}),(0,s.jsx)("button",{type:"button",onClick:q,disabled:!G.trim()||H.tags.includes(G.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"或选择预定义标签："}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:["AI写作","AI绘画","AI视频","AI音频","AI编程","AI翻译","AI客服","AI分析","自然语言处理","计算机视觉","机器学习","深度学习","语音识别","图像识别","文本生成","图像生成","视频生成","音频生成","代码生成","数据分析","聊天机器人","虚拟助手","自动化","智能推荐","内容创作","设计工具","生产力工具","教育工具","营销工具","开发工具","API服务","云服务","移动应用","网页应用","桌面应用","浏览器插件","开源","商业","企业级","个人使用","团队协作","实时处理","批量处理","多语言支持"].filter(e=>!H.tags.includes(e)).map((e,t)=>(0,s.jsx)("button",{type:"button",onClick:()=>{H.tags.length<3&&J(t=>({...t,tags:[...t.tags,e]}))},className:"px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 transition-colors",children:e},t))})]})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"产品截图"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"添加产品截图来展示您的工具界面和功能"}),H.screenshots.length>0&&(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-4",children:H.screenshots.map((e,t)=>(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("img",{src:e,alt:"截图 ".concat(t+1),className:"w-full h-32 object-cover rounded-lg border border-gray-300"}),(0,s.jsx)("button",{type:"button",onClick:()=>V(e),className:"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity",children:(0,s.jsx)(m.A,{className:"h-3 w-3"})})]},t))}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("input",{type:"url",value:W,onChange:e=>F(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),Q()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入截图URL"}),(0,s.jsx)("button",{type:"button",onClick:Q,disabled:!W.trim()||H.screenshots.includes(W.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{type:"submit",disabled:w,className:"px-8 py-3 rounded-lg font-medium transition-colors ".concat(w?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:w?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.A,{size:"sm",className:"mr-2"}),"更新中..."]}):"更新工具信息"})})]})]}),(0,s.jsx)(c.A,{isOpen:A,onClose:()=>O(!1)})]}):(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"工具不存在"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"您要编辑的工具不存在或已被删除"}),(0,s.jsx)(b(),{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回工具列表"})]})})}):(0,s.jsxs)(i.A,{children:[(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"请先登录"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"您需要登录后才能编辑工具信息"}),(0,s.jsx)("button",{onClick:()=>O(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"登录"})]})}),(0,s.jsx)(c.A,{isOpen:A,onClose:()=>O(!1)})]})}},9869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:m,...g}=e;return(0,s.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:r,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",d),...!u&&!o(g)&&{"aria-hidden":"true"},...g},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{className:o,...c}=r;return(0,s.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),o),...c})});return r.displayName=n(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,316,8441,1684,7358],()=>t(8431)),_N_E=e.O()}]);