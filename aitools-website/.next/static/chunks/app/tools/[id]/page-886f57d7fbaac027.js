(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[664],{1852:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(5155),r=t(2115),l=t(6874),c=t.n(l),i=t(5695),n=t(4478),o=t(2731),d=t(9783),x=t(4601),m=t(2108),h=t(9911);function u(e){let{toolId:s,onLoginRequired:t}=e,{data:l}=(0,m.useSession)(),[c,i]=(0,r.useState)([]),[n,o]=(0,r.useState)(""),[d,x]=(0,r.useState)(null),[u,g]=(0,r.useState)(""),[j,p]=(0,r.useState)(!1),[b,y]=(0,r.useState)(!1),f=async()=>{p(!0);try{let e=await fetch("/api/tools/".concat(s,"/comments"));if(e.ok){let s=await e.json();s.success&&i(s.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{p(!1)}};(0,r.useEffect)(()=>{f()},[s]);let N=async()=>{if(!l){null==t||t();return}if(n.trim()){y(!0);try{let e=await fetch("/api/tools/".concat(s,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:n.trim()})});if(e.ok)(await e.json()).success&&(o(""),f());else{let s=await e.json();console.error("Comment submission failed:",s.message)}}catch(e){console.error("Comment submission error:",e)}finally{y(!1)}}},v=async e=>{if(!l){null==t||t();return}if(u.trim()){y(!0);try{let t=await fetch("/api/tools/".concat(s,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:u.trim(),parentId:e})});if(t.ok)(await t.json()).success&&(g(""),x(null),f());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{y(!1)}}},w=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/36e5);return t<1?"刚刚":t<24?"".concat(t,"小时前"):t<168?"".concat(Math.floor(t/24),"天前"):s.toLocaleDateString("zh-CN")},k=e=>{let{comment:s,isReply:t=!1}=e;return(0,a.jsx)("div",{className:"".concat(t?"ml-8 border-l-2 border-gray-100 pl-4":""),children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:s.userId.image?(0,a.jsx)("img",{src:s.userId.image,alt:s.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)(h.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s.userId.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:w(s.createdAt)})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-2",children:s.content}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:!t&&(0,a.jsxs)("button",{onClick:()=>x(d===s._id?null:s._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,a.jsx)(h.w1Z,{className:"w-3 h-3"}),"回复"]})}),d===s._id&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("textarea",{value:u,onChange:e=>g(e.target.value),placeholder:"写下你的回复...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,a.jsx)("button",{onClick:()=>{x(null),g("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,a.jsx)("button",{onClick:()=>v(s._id),disabled:b||!u.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?"发送中...":"发送"})]})]}),s.replies&&s.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:s.replies.map(e=>(0,a.jsx)(k,{comment:e,isReply:!0},e._id))})]})]})})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["评论 (",c.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:n,onChange:e=>o(e.target.value),placeholder:l?"写下你的评论...":"请先登录后评论",className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[n.length,"/1000"]}),(0,a.jsx)("button",{onClick:N,disabled:b||!n.trim()||!l,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?"发送中...":"发表评论"})]})]}),j?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载评论中..."})]}):0===c.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无评论，来发表第一条评论吧！"})}):(0,a.jsx)("div",{className:"space-y-6",children:c.map(e=>(0,a.jsx)(k,{comment:e},e._id))})]})}var g=t(6063),j=t(5731),p=t(7550),b=t(5868),y=t(2657),f=t(1976),N=t(6516),v=t(3332),w=t(3786);function k(){var e;let s=(0,i.useParams)(),[t,l]=(0,r.useState)(null),[m,h]=(0,r.useState)([]),[k,S]=(0,r.useState)(!0),[A,C]=(0,r.useState)(""),[T,_]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s.id&&O(s.id)},[s.id]);let O=async e=>{try{S(!0),C("");let s=await j.u.getTool(e);s.success&&s.data?(l(s.data),L(s.data.category)):C(s.error||"工具不存在")}catch(e){C("网络错误，请重试")}finally{S(!1)}},L=async e=>{try{let t=await j.u.getTools({category:e,status:"approved",limit:3});if(t.success&&t.data){let e=t.data.tools.filter(e=>e._id!==s.id);h(e.slice(0,3))}}catch(e){}},q=e=>{switch(e){case"free":return"bg-green-100 text-green-800";case"freemium":return"bg-blue-100 text-blue-800";case"paid":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},E=e=>{switch(e){case"free":return"免费";case"freemium":return"免费增值";case"paid":return"付费";default:return e}};return k?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(o.A,{size:"lg"})})}):A||!t?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(d.A,{message:A||"工具不存在",onClose:()=>C("")}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsxs)(c(),{href:"/tools",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"返回工具目录"]})})]})}):(0,a.jsxs)(n.A,{children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,a.jsx)(c(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(c(),{href:"/tools",className:"hover:text-blue-600",children:"工具目录"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:t.name})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(c(),{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"返回工具目录"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t.logo?(0,a.jsx)("img",{src:t.logo,alt:t.name,className:"w-16 h-16 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:t.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:t.name}),t.tagline&&(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:t.tagline}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(q(t.pricing)),children:[(0,a.jsx)(b.A,{className:"mr-1 h-4 w-4"}),E(t.pricing)]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[t.views||0," 浏览"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[t.likes||0," 喜欢"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{toolId:t._id,initialLikes:t.likes,onLoginRequired:()=>_(!0)}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,a.jsx)(N.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed mb-6",children:t.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:null==(e=t.tags)?void 0:e.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,a.jsx)(v.A,{className:"mr-1 h-3 w-3"}),e]},s))}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(w.A,{className:"mr-2 h-5 w-5"}),"访问 ",t.name]}),(0,a.jsx)(x.A,{toolId:t._id,initialLikes:t.likes,onLoginRequired:()=>_(!0)})]})]})}),(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"工具信息"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"分类"}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:"文本生成"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"价格模式"}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat(q(t.pricing)),children:E(t.pricing)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"提交日期"}),(0,a.jsx)("span",{className:"text-gray-900",children:t.submittedAt||t.createdAt})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"提交者"}),(0,a.jsx)("span",{className:"text-gray-900",children:t.submittedBy||"未知"})]})]})]}),m.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"相关工具"}),(0,a.jsx)("div",{className:"space-y-4",children:m.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:(0,a.jsxs)(c(),{href:"/tools/".concat(e._id),children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 hover:text-blue-600 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded ".concat(q(e.pricing)),children:E(e.pricing)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{children:[e.views||0," 浏览"]}),(0,a.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})},e._id))})]}),(0,a.jsx)("div",{className:"mt-12",children:(0,a.jsx)(u,{toolId:t._id,onLoginRequired:()=>_(!0)})})]})]})]}),(0,a.jsx)(g.A,{isOpen:T,onClose:()=>_(!1)})]})}},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(5155);function r(e){let{size:s="md",className:t=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(5155);t(2115);var r=t(6874),l=t.n(r);let c=e=>{let{children:s}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("main",{className:"flex-1",children:s}),(0,a.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,a.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4601:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(5155),r=t(2115),l=t(2108),c=t(9911);function i(e){let{toolId:s,initialLikes:t=0,initialLiked:i=!1,onLoginRequired:n}=e,{data:o}=(0,l.useSession)(),[d,x]=(0,r.useState)(i),[m,h]=(0,r.useState)(t),[u,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/tools/".concat(s,"/like"));if(e.ok){let s=await e.json();s.success&&(x(s.data.liked),h(s.data.likes))}}catch(e){console.error("Failed to fetch like status:",e)}};o&&e()},[s,o]);let j=async()=>{if(!o){null==n||n();return}if(!u){g(!0);try{let e=await fetch("/api/tools/".concat(s,"/like"),{method:"POST",headers:{"Content-Type":"application/json"}});if(e.ok){let s=await e.json();s.success&&(x(s.data.liked),h(s.data.likes))}else{let s=await e.json();console.error("Like failed:",s.message)}}catch(e){console.error("Like request failed:",e)}finally{g(!1)}}};return(0,a.jsxs)("button",{onClick:j,disabled:u,className:"\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ".concat(d?"bg-red-50 text-red-600 hover:bg-red-100":"bg-gray-50 text-gray-600 hover:bg-gray-100","\n        ").concat(u?"opacity-50 cursor-not-allowed":"hover:scale-105","\n        border border-gray-200 hover:border-gray-300\n      "),children:[d?(0,a.jsx)(c.Mbv,{className:"w-4 h-4 text-red-500"}):(0,a.jsx)(c.sOK,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:m>0?m:""})]})}},5731:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let a=t(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class r{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...s.headers},...s},r=await fetch(t,a),l=await r.json();if(!r.ok)throw Error(l.error||"HTTP error! status: ".concat(r.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=a){this.baseURL=e}}let l=new r},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(5155),r=t(2115),l=t(2108),c=t(9911);function i(e){let{isOpen:s,onClose:t}=e,[i,n]=(0,r.useState)("method"),[o,d]=(0,r.useState)(""),[x,m]=(0,r.useState)(""),[h,u]=(0,r.useState)(!1),[g,j]=(0,r.useState)(""),p=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},b=()=>{n("method"),d(""),m(""),j(""),t()},y=async e=>{try{u(!0),await (0,l.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{u(!1)}},f=async()=>{if(!o)return void j("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return void j("请输入有效的邮箱地址");j(""),u(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),s=await e.json();s.success?(m(s.token),n("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{u(!1)}},N=async e=>{if(6===e.length){u(!0);try{let s=await (0,l.signIn)("email-code",{email:o,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(p("登录成功，欢迎回来！"),b()):p((null==s?void 0:s.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{u(!1)}}},v=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var a;null==(a=t[e+1])||a.focus()}let r=Array.from(t).map(e=>e.value).join("");6===r.length&&N(r)};return s?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:b}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,a.jsx)("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>y("google"),disabled:h,children:[(0,a.jsx)(c.DSS,{}),"使用 Google 登录"]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>y("github"),disabled:h,children:[(0,a.jsx)(c.hL4,{}),"使用 GitHub 登录"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>n("email"),children:[(0,a.jsx)(c.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,a.jsx)("input",{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&f(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:f,disabled:h,children:h?"发送中...":"发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]}),"code"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",o," 的6位验证码"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:s=>v(e,s.target.value),disabled:h,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("email"),children:"重新发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]})]})]})]}):null}},8985:(e,s,t)=>{Promise.resolve().then(t.bind(t,1852))},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(5155),r=t(5339),l=t(4416);function c(e){let{message:s,onClose:t,className:c=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,a.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[711,874,108,592,441,684,358],()=>s(8985)),_N_E=e.O()}]);