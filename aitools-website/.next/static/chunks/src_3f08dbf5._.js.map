{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,qBACE,6LAAC;QAAI,WAAU;;0BAGb,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;KAjFM;uCAmFS", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const [liked, setLiked] = useState(initialLiked);\n  const [likes, setLikes] = useState(initialLikes);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // 获取点赞状态\n  useEffect(() => {\n    const fetchLikeStatus = async () => {\n      try {\n        const response = await fetch(`/api/tools/${toolId}/like`);\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success) {\n            setLiked(data.data.liked);\n            setLikes(data.data.likes);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to fetch like status:', error);\n      }\n    };\n\n    if (session) {\n      fetchLikeStatus();\n    }\n  }, [toolId, session]);\n\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (isLoading) return;\n\n    setIsLoading(true);\n    \n    try {\n      // 如果在liked页面，发送强制unlike请求\n      const requestBody = isInLikedPage ? { forceUnlike: true } : {};\n\n      const response = await fetch(`/api/tools/${toolId}/like`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const newLikedState = data.data.liked;\n          setLiked(newLikedState);\n          setLikes(data.data.likes);\n\n          // 如果用户取消了点赞，并且提供了onUnlike回调，则调用它\n          if (!newLikedState && onUnlike) {\n            onUnlike(toolId);\n          }\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Like failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Like request failed:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={isLoading}\n      className={`\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ${liked \n          ? 'bg-red-50 text-red-600 hover:bg-red-100' \n          : 'bg-gray-50 text-gray-600 hover:bg-gray-100'\n        }\n        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}\n        border border-gray-200 hover:border-gray-300\n      `}\n    >\n      {liked ? (\n        <FaHeart className=\"w-4 h-4 text-red-500\" />\n      ) : (\n        <FaRegHeart className=\"w-4 h-4\" />\n      )}\n      <span className=\"text-sm font-medium\">\n        {likes > 0 ? likes : ''}\n      </span>\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACL;;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;wDAAkB;oBACtB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC;wBACxD,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,IAAI,KAAK,OAAO,EAAE;gCAChB,SAAS,KAAK,IAAI,CAAC,KAAK;gCACxB,SAAS,KAAK,IAAI,CAAC,KAAK;4BAC1B;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA,IAAI,SAAS;gBACX;YACF;QACF;+BAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,WAAW;QAEf,aAAa;QAEb,IAAI;YACF,0BAA0B;YAC1B,MAAM,cAAc,gBAAgB;gBAAE,aAAa;YAAK,IAAI,CAAC;YAE7D,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,MAAM,gBAAgB,KAAK,IAAI,CAAC,KAAK;oBACrC,SAAS;oBACT,SAAS,KAAK,IAAI,CAAC,KAAK;oBAExB,iCAAiC;oBACjC,IAAI,CAAC,iBAAiB,UAAU;wBAC9B,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,gBAAgB,UAAU,OAAO;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,CAAC;;QAEV,EAAE,QACE,4CACA,6CACH;QACD,EAAE,YAAY,kCAAkC,kBAAkB;;MAEpE,CAAC;;YAEA,sBACC,6LAAC,iJAAA,CAAA,UAAO;gBAAC,WAAU;;;;;qCAEnB,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BAExB,6LAAC;gBAAK,WAAU;0BACb,QAAQ,IAAI,QAAQ;;;;;;;;;;;;AAI7B;GAxGwB;;QAQI,iJAAA,CAAA,aAAU;;;KARd", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Eye, Heart, ExternalLink } from 'lucide-react';\nimport LikeButton from './tools/LikeButton';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false }) => {\n  const getPricingColor = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return 'bg-green-100 text-green-800';\n      case 'freemium':\n        return 'bg-blue-100 text-blue-800';\n      case 'paid':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPricingText = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return '免费';\n      case 'freemium':\n        return '免费增值';\n      case 'paid':\n        return '付费';\n      default:\n        return pricing;\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <img\n                src={tool.logo}\n                alt={tool.name}\n                className=\"w-12 h-12 rounded-lg object-cover\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPricingColor(tool.pricing)}`}>\n                {getPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <LikeButton\n              toolId={tool._id}\n              initialLikes={tool.likes}\n              onLoginRequired={onLoginRequired}\n              onUnlike={onUnlike}\n              isInLikedPage={isInLikedPage}\n            />\n            <Link\n              href={`/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              查看详情\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAyBA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,gBAAgB,KAAK,EAAE;IACnG,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,6LAAC;oCACC,KAAK,KAAK,IAAI;oCACd,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;yDAGZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,gBAAgB,KAAK,OAAO,GAAG;sDACxH,eAAe,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKlC,6LAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,6LAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4IAAA,CAAA,UAAU;oCACT,QAAQ,KAAK,GAAG;oCAChB,cAAc,KAAK,KAAK;oCACxB,iBAAiB;oCACjB,UAAU;oCACV,eAAe;;;;;;8CAEjB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oCAC1B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KAxHM;uCA0HS", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface CategoryCardProps {\n  category: {\n    _id: string;\n    name: string;\n    slug: string;\n    description: string;\n    icon?: string;\n    color?: string;\n    toolCount: number;\n  };\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link href={`/categories/${category.slug}`}>\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer\">\n        <div className=\"p-6\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <div \n              className=\"w-12 h-12 rounded-lg flex items-center justify-center text-2xl\"\n              style={{ backgroundColor: category.color || '#3B82F6' }}\n            >\n              <span className=\"text-white\">\n                {category.icon || '🔧'}\n              </span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {category.name}\n              </h3>\n              <p className=\"text-sm text-gray-500\">\n                {category.toolCount} 个工具\n              </p>\n            </div>\n          </div>\n          \n          <p className=\"text-gray-600 text-sm\">\n            {category.description}\n          </p>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAcA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;kBACxC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK,IAAI;gCAAU;0CAEtD,cAAA,6LAAC;oCAAK,WAAU;8CACb,SAAS,IAAI,IAAI;;;;;;;;;;;0CAGtB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,SAAS,IAAI;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;;4CACV,SAAS,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;AAMjC;KA/BM;uCAiCS", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  publishedAt?: string;\n  status: 'draft' | 'pending' | 'approved' | 'rejected';\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    publishedAt?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACS;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;AAqI7D,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx"], "sourcesContent": ["'use client';\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport ToolCard from '@/components/ToolCard';\nimport CategoryCard from '@/components/CategoryCard';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { apiClient, Tool } from '@/lib/api';\nimport { Search, TrendingUp, Star, Zap, Calendar, Clock } from 'lucide-react';\n\n// 分类数据\n\nconst categories = [\n  {\n    _id: '1',\n    name: '文本生成',\n    slug: 'text-generation',\n    description: 'AI tools for generating and editing text content',\n    icon: '📝',\n    color: '#3B82F6',\n    toolCount: 25\n  },\n  {\n    _id: '2',\n    name: '图像生成',\n    slug: 'image-generation',\n    description: 'AI tools for creating and editing images',\n    icon: '🎨',\n    color: '#8B5CF6',\n    toolCount: 18\n  },\n  {\n    _id: '3',\n    name: '代码生成',\n    slug: 'code-generation',\n    description: 'AI tools for writing and debugging code',\n    icon: '💻',\n    color: '#F59E0B',\n    toolCount: 12\n  },\n  {\n    _id: '4',\n    name: '数据分析',\n    slug: 'data-analysis',\n    description: 'AI tools for analyzing and visualizing data',\n    icon: '📊',\n    color: '#06B6D4',\n    toolCount: 15\n  }\n];\n\nexport default function Home() {\n  const [featuredTools, setFeaturedTools] = useState<Tool[]>([]);\n  const [todayTools, setTodayTools] = useState<Tool[]>([]);\n  const [recentTools, setRecentTools] = useState<Tool[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n\n  const fetchAllData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // 准备日期参数\n      const today = new Date();\n      const todayStart = new Date(today);\n      todayStart.setHours(0, 0, 0, 0);\n      const todayEnd = new Date(today);\n      todayEnd.setHours(23, 59, 59, 999);\n\n      const sevenDaysAgo = new Date();\n      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n      sevenDaysAgo.setHours(0, 0, 0, 0);\n\n      // 并行获取所有数据\n      const [featuredResponse, todayResponse, recentResponse] = await Promise.all([\n        // 热门工具 - 按浏览量排序\n        apiClient.getTools({\n          status: 'approved',\n          limit: 6,\n          sort: 'views',\n          order: 'desc'\n        }),\n        // 当日发布的工具 - 使用日期筛选\n        apiClient.getTools({\n          status: 'approved',\n          limit: 20,\n          sort: 'publishedAt',\n          order: 'desc',\n          dateFrom: todayStart.toISOString(),\n          dateTo: todayEnd.toISOString()\n        }),\n        // 最近发布的工具 - 使用日期筛选\n        apiClient.getTools({\n          status: 'approved',\n          limit: 20,\n          sort: 'publishedAt',\n          order: 'desc',\n          dateFrom: sevenDaysAgo.toISOString()\n        })\n      ]);\n\n      if (featuredResponse.success && featuredResponse.data) {\n        setFeaturedTools(featuredResponse.data.tools);\n      }\n\n      if (todayResponse.success && todayResponse.data) {\n        setTodayTools(todayResponse.data.tools.slice(0, 6));\n      }\n\n      if (recentResponse.success && recentResponse.data) {\n        setRecentTools(recentResponse.data.tools.slice(0, 6));\n      }\n\n    } catch (err) {\n      setError('网络错误，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <LoadingSpinner size=\"lg\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      {/* Error Message */}\n      {error && (\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\">\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n          />\n        </div>\n      )}\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              发现最好的\n              <span className=\"text-blue-600\"> AI 工具</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美工具。\n            </p>\n\n            {/* Search Bar */}\n            <div className=\"max-w-2xl mx-auto mb-8\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具、分类或功能...\"\n                  className=\"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg\"\n                />\n                <Search className=\"absolute left-4 top-4 h-6 w-6 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link\n                href=\"/tools\"\n                className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n              >\n                <Zap className=\"mr-2 h-5 w-5\" />\n                浏览所有工具\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n              >\n                提交您的工具\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Today's Tools Section */}\n      {todayTools.length > 0 && (\n        <section className=\"py-16 bg-gradient-to-r from-green-50 to-blue-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                <Calendar className=\"inline-block mr-2 h-8 w-8 text-green-600\" />\n                今日发布\n              </h2>\n              <p className=\"text-lg text-gray-600\">\n                今天刚刚发布的最新 AI 工具\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {todayTools.map((tool) => (\n                <ToolCard\n                  key={tool._id}\n                  tool={tool}\n                  onLoginRequired={() => setIsLoginModalOpen(true)}\n                />\n              ))}\n            </div>\n          </div>\n        </section>\n      )}\n\n      {/* Recent Tools Section */}\n      {recentTools.length > 0 && (\n        <section className=\"py-16 bg-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                <Clock className=\"inline-block mr-2 h-8 w-8 text-blue-600\" />\n                最近发布\n              </h2>\n              <p className=\"text-lg text-gray-600\">\n                过去一周内发布的新 AI 工具\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {recentTools.map((tool) => (\n                <ToolCard\n                  key={tool._id}\n                  tool={tool}\n                  onLoginRequired={() => setIsLoginModalOpen(true)}\n                />\n              ))}\n            </div>\n\n            <div className=\"text-center mt-8\">\n              <Link\n                href=\"/tools?sort=publishedAt&order=desc\"\n                className=\"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n              >\n                查看更多最新工具\n              </Link>\n            </div>\n          </div>\n        </section>\n      )}\n\n      {/* Featured Tools Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              <TrendingUp className=\"inline-block mr-2 h-8 w-8 text-blue-600\" />\n              热门工具\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              最受欢迎和评价最高的 AI 工具\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {featuredTools.map((tool) => (\n              <ToolCard\n                key={tool._id}\n                tool={tool}\n                onLoginRequired={() => setIsLoginModalOpen(true)}\n              />\n            ))}\n          </div>\n\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/tools\"\n              className=\"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n            >\n              查看更多工具\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              <Star className=\"inline-block mr-2 h-8 w-8 text-blue-600\" />\n              热门分类\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              按功能分类浏览 AI 工具\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {categories.map((category) => (\n              <CategoryCard key={category._id} category={category} />\n            ))}\n          </div>\n\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/categories\"\n              className=\"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n            >\n              查看所有分类\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-16 bg-blue-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">500+</div>\n              <div className=\"text-blue-100\">AI 工具</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">50+</div>\n              <div className=\"text-blue-100\">工具分类</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">10K+</div>\n              <div className=\"text-blue-100\">用户访问</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;;AAYA,OAAO;AAEP,MAAM,aAAa;IACjB;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YAET,SAAS;YACT,MAAM,QAAQ,IAAI;YAClB,MAAM,aAAa,IAAI,KAAK;YAC5B,WAAW,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC7B,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,QAAQ,CAAC,IAAI,IAAI,IAAI;YAE9B,MAAM,eAAe,IAAI;YACzB,aAAa,OAAO,CAAC,aAAa,OAAO,KAAK;YAC9C,aAAa,QAAQ,CAAC,GAAG,GAAG,GAAG;YAE/B,WAAW;YACX,MAAM,CAAC,kBAAkB,eAAe,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1E,gBAAgB;gBAChB,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;oBACjB,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;gBACT;gBACA,mBAAmB;gBACnB,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;oBACjB,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,UAAU,WAAW,WAAW;oBAChC,QAAQ,SAAS,WAAW;gBAC9B;gBACA,mBAAmB;gBACnB,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;oBACjB,QAAQ;oBACR,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,UAAU,aAAa,WAAW;gBACpC;aACD;YAED,IAAI,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,EAAE;gBACrD,iBAAiB,iBAAiB,IAAI,CAAC,KAAK;YAC9C;YAEA,IAAI,cAAc,OAAO,IAAI,cAAc,IAAI,EAAE;gBAC/C,cAAc,cAAc,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;YAClD;YAEA,IAAI,eAAe,OAAO,IAAI,eAAe,IAAI,EAAE;gBACjD,eAAe,eAAe,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;YACpD;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;oBAAC,MAAK;;;;;;;;;;;;;;;;IAI7B;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;;YAEJ,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;oBACX,SAAS;oBACT,SAAS,IAAM,SAAS;;;;;;;;;;;0BAM9B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,WAAW,MAAM,GAAG,mBACnB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA6C;;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,iIAAA,CAAA,UAAQ;oCAEP,MAAM;oCACN,iBAAiB,IAAM,oBAAoB;mCAFtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;YAWxB,YAAY,MAAM,GAAG,mBACpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA4C;;;;;;;8CAG/D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,iIAAA,CAAA,UAAQ;oCAEP,MAAM;oCACN,iBAAiB,IAAM,oBAAoB;mCAFtC,KAAK,GAAG;;;;;;;;;;sCAOnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAA4C;;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,iIAAA,CAAA,UAAQ;oCAEP,MAAM;oCACN,iBAAiB,IAAM,oBAAoB;mCAFtC,KAAK,GAAG;;;;;;;;;;sCAOnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAA4C;;;;;;;8CAG9D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,UAAY;oCAAoB,UAAU;mCAAxB,SAAS,GAAG;;;;;;;;;;sCAInC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,2IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C;GAvSwB;KAAA", "debugId": null}}]}